// postSlice.js
import { createSlice } from "@reduxjs/toolkit";
import { createApi, fetchBaseQuery } from "@reduxjs/toolkit/query/react";
import { fetchAccessToken } from "./chatSlice";

export const postApi = createApi({
  reducerPath: "postApi",
  baseQuery: fetchBaseQuery({
    baseUrl: import.meta.env.VITE_API_BASE_URL,
    prepareHeaders: async (headers) => {
      const token = await fetchAccessToken();
      if (token) {
        headers.set("authorization", `Bearer ${token}`);
      }
      return headers;
    },
  }),
  keepUnusedDataFor: 600,
  tagTypes: ["Post", "Monologue"],
  endpoints: (builder) => ({
    getPosts: builder.query({
      query: (page = 1) => `posts?page_no=${page}`,
      providesTags: (result) =>
        result
          ? [
              ...result.data.map(({ post_id }) => ({
                type: "Post",
                id: post_id,
              })),
              { type: "Post", id: "LIST" },
            ]
          : [{ type: "Post", id: "LIST" }],
    }),
    getPostById: builder.query({
      query: (postId) => `posts/${postId}`,
      providesTags: (result, error, id) => [
        { type: "Post", id },
        ...(result?.data?.children_posts?.map((child) => ({
          type: "Post",
          id: child.post_id,
        })) || []),
      ],
    }),
    addUpvote: builder.mutation({
      query: (postId) => ({
        url: `posts/add_upvote/${postId}`,
        method: "POST",
      }),
      invalidatesTags: (result, error, id) => [{ type: "Post", id }],
    }),
    removeUpvote: builder.mutation({
      query: (postId) => ({
        url: `posts/remove_upvote/${postId}`,
        method: "DELETE",
      }),
      invalidatesTags: (result, error, id) => [{ type: "Post", id }],
    }),
    deletePost: builder.mutation({
      query: (postId) => ({
        url: `posts/${postId}`,
        method: "DELETE",
      }),
      invalidatesTags: [{ type: "Post", id: "LIST" }],
    }),
    addPost: builder.mutation({
      query: (postData) => ({
        url: "posts/add_post",
        method: "POST",
        body: postData,
      }),
      invalidatesTags: (result, error, { parent_id }) => [
        { type: "Post", id: "LIST" },
        { type: "Post", id: parent_id },
      ],
    }),
    getShareableLink: builder.query({
      query: ({ postId, viewToken }) =>
        `posts/shareable_link/${postId}${
          viewToken ? `?view_token=${viewToken}` : ""
        }`,
    }),
    togglePostVisibility: builder.mutation({
      query: (postId) => ({
        url: `/posts/toggle_visibility/${postId}`,
        method: "PATCH",
      }),
      invalidatesTags: (result, error, id) => [{ type: "Post", id }],
    }),
    // New endpoints for monologues and private posts
    getMonologues: builder.query({
      query: ({ page, limit = 25, searchTerm = '', activeChip, genderFilter, categoryFilter }) => {
        return {
          url: '/monologues/get_monologues',
          params: { _start: page, _limit: limit, _searchTerm: searchTerm, _chip: activeChip, _gender: genderFilter, _category: categoryFilter  },
        };
      },
      providesTags: ["Monologue"],
    }),
    generateMonologue: builder.mutation({
      query: (prompt) => ({
        url: "/monologues/generate_monologue",
        method: "POST",
        body: { prompt },
        responseHandler: "text",
      }),
    
    }),
    getPrivatePosts: builder.query({
      query: (page = 1) => `posts/private?page_no=${page}`,
      providesTags: (result) =>
        result
          ? [
              ...result.data.map(({ post_id }) => ({
                type: "Post",
                id: post_id,
              })),
              { type: "Post", id: "PRIVATE_LIST" },
            ]
          : [{ type: "Post", id: "PRIVATE_LIST" }],
    }),
    getSinglePost: builder.query({
      query: ({ postId, viewToken }) => ({
        url: `posts/${postId}${viewToken ? `?view_token=${viewToken}` : ""}`,
        method: "GET",
      }),
      providesTags: (result, error, { postId }) => [
        { type: "Post", id: postId },
      ],
    }),
    insertMonologue: builder.mutation({
      query: (postData) => ({
        url: "/monologues/insert_monologue",
        method: "POST",
        body: postData
      }),
    }),
    getMonologueFilter: builder.query({
      query: () => "/monologues/get_monologues_filter",
      providesTags: ["Filters"],
      transformResponse: (response) => response.data,
    }),
    deleteMonologue: builder.mutation({
      query: (monologueId) => ({
        url: `/monologues/${monologueId}`,
        method: "DELETE",
      })
    }),
    retryAnalysis: builder.mutation({
      query: (postId) => ({
        url: `/expression-analysis/retry/${postId}`,
        method: "POST",
      }),
      invalidatesTags: (result, error, postId) => [{ type: "Post", id: postId }],
    }),
  }),
});

// Export the auto-generated hooks for usage in functional components
export const {
  useGetPostsQuery,
  useGetPostByIdQuery,
  useAddUpvoteMutation,
  useRemoveUpvoteMutation,
  useDeletePostMutation,
  useAddPostMutation,
  useGetShareableLinkQuery,
  useTogglePostVisibilityMutation,
  useGetMonologuesQuery,
  useGetPrivatePostsQuery,
  useGetSinglePostQuery,
  useGenerateMonologueMutation,
  useInsertMonologueMutation,
  useGetMonologueFilterQuery,
  useDeleteMonologueMutation,
  useRetryAnalysisMutation,
} = postApi;

// Create a slice for any additional post-related state
const postSlice = createSlice({
  name: "posts",
  initialState: {
    currentPage: 1,
    hasMore: true,
  },
  reducers: {
    setCurrentPage: (state, action) => {
      state.currentPage = action.payload;
    },
    setHasMore: (state, action) => {
      state.hasMore = action.payload;
    },
  },
});

export const { setCurrentPage, setHasMore } = postSlice.actions;

export default postSlice.reducer;
