import { createApi, fetchBaseQuery } from "@reduxjs/toolkit/query/react";
import { fetchAccessToken } from "./chatSlice";

export const surveyApi = createApi({
  reducerPath: "surveyApi",
  baseQuery: fetchBaseQuery({
    baseUrl: import.meta.env.VITE_API_BASE_URL,
    prepareHeaders: async (headers) => {
      const token = await fetchAccessToken();
      if (token) {
        headers.set("authorization", `Bear<PERSON> ${token}`);
      }
      return headers;
    },
  }),
  keepUnusedDataFor: 600,
  endpoints: (builder) => ({
    submitSurvey: builder.mutation({
      query: (surveyData) => {
        return {
          url: "survey/submit_survey",
          method: "POST",
          body: surveyData,
        };
      },
    }),

    getAllSurvey: builder.query({
      query: ({ page, limit = 10 }) => {
        return {
          url: "survey/get_all_survey",
          method: "GET",
          params: { _start: page, _limit: limit },
        };
      },
    }),
  }),
});

export const {
  useSubmitSurveyMutation,
  useGetAllSurveyQuery
} = surveyApi;
