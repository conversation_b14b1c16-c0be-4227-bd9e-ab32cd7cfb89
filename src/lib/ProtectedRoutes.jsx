// ProtectedRoutes Component
import { Outlet, Navigate } from "react-router-dom";
import { useSelector } from "react-redux";
import Loading from "@/components/Loading";


const ProtectedRoutes = () => {
  const { session, isLoading } = useSelector((state) => state.user);
  const isAuthenticated = session?.user;
  if (isLoading) {
    return (
      <div>
        <Loading />
      </div>
    );
  }

  return isAuthenticated ? <Outlet /> : <Navigate to="/home" />;
};

export default ProtectedRoutes;
