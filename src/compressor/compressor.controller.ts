import {
  Controller,
  Post,
  UploadedFile,
  UseInterceptors,
} from '@nestjs/common';
import { CompressorService } from './compressor.service';
import { FileInterceptor } from '@nestjs/platform-express';
@Controller('compressor')
export class CompressorController {
  constructor(private readonly compressorService: CompressorService) {}
  @Post('compress') // to test compression, could be used by frontend as well
  @UseInterceptors(FileInterceptor('file'))
  async compressVideo(@UploadedFile() file: Express.Multer.File) {
    return await this.compressorService.compress(file);
  }
}
