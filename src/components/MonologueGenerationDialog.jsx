import { useState, useEffect } from "react";
import { <PERSON><PERSON>, DialogContent, DialogTitle } from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { useGenerateMonologueMutation } from "@/redux/features/postSlice";
import { <PERSON>rk<PERSON>, <PERSON> } from "lucide-react";
import { Textarea } from "./ui/textarea";
import { ScrollArea } from "./ui/scroll-area";

const MonologueGenerationDialog = ({ isOpen, onClose, onUse }) => {
  const [prompt, setPrompt] = useState("");
  const [generatedMonologue, setGeneratedMonologue] = useState("");
  const [editedMonologue, setEditedMonologue] = useState("");
  const [isEdited, setIsEdited] = useState(false);
  const [generateMonologue, { isLoading, isError, error, reset }] =
    useGenerateMonologueMutation();

  useEffect(() => {
    if (generatedMonologue) {
      setEditedMonologue(generatedMonologue);
      setIsEdited(false);
    }
  }, [generatedMonologue]);

  const handleGenerate = async () => {
    try {
      const result = await generateMonologue(prompt).unwrap();

      setGeneratedMonologue(result);
    } catch (error) {
      console.error("Failed to generate monologue:", error);
      setGeneratedMonologue("Failed to generate monologue. Please try again.");
    }
  };

  const handleEdit = (e) => {
    setEditedMonologue(e.target.value);
    setIsEdited(true);
  };

  const handleUse = () => {
    onUse(isEdited ? editedMonologue : generatedMonologue);
    onClose();
  };

  const handleOnClose = (event) => {
    event.preventDefault(); // Prevents backdrop click from closing the dialog
    setPrompt("");
    setGeneratedMonologue("");
    reset();
    onClose();
  };

  return (
    <Dialog open={isOpen} onOpenChange={(open) => open && onClose()}>
      <DialogContent className="max-w-3xl bg-primary1 text-text1 border-text2 p-10 discord-scrollbar">
        <DialogTitle>Enter your Prompt</DialogTitle>

        <Textarea
          value={prompt}
          onChange={(e) => setPrompt(e.target.value)}
          className="w-full mt-4 bg-secondary2 border rounded-lg px-4 py-2  placeholder:text-text2"
          disabled={isLoading}
          placeholder={`Examples:
  1. “Write a monologue similar to Meryl Streep in Sophie’s Choice.”
  2. “Modernize this Shakespearean monologue.”
  3. “Write a clean, non-violent monologue about getting lost in a cemetery on Halloween night.”`}
          rows={8}
        />
        {generatedMonologue && (
        <div className="flex justify-end mt-4 gap-2 ">
                <Button onClick={handleGenerate} disabled={isLoading}>
              {isLoading ? (
                <span>
                  <Sparkles size={16} className="animate-pulse" />{" "}
                </span>
              ) : (
                <span className="flex gap-2 items-center">
                  <Sparkles size={16} /> Regenerate
                </span>
              )}
            </Button>
        </div>
        )}
        {generatedMonologue ? (
          ""
        ) : (
          <div className="flex justify-end mt-4 gap-2 ">
            <Button
              className="bg-close shadow-none hover:bg-close/80"
              onClick={handleOnClose}
            >
              <span className="flex gap-2 items-center">
                <X size={16} /> Close
              </span>
            </Button>
            <Button onClick={handleGenerate} disabled={isLoading}>
              {isLoading ? (
                <span>
                  <Sparkles size={16} className="animate-pulse" />{" "}
                </span>
              ) : (
                <span className="flex gap-2 items-center">
                  <Sparkles size={16} /> Generate
                </span>
              )}
            </Button>
          </div>
        )}

        {isError && (
          <div className="mt-4 text-red-500">
            Error: {error?.data?.message || "Failed to generate monologue"}
          </div>
        )}
        {generatedMonologue && (
          <div className="mt-6">
            <Textarea
              value={editedMonologue}
              onChange={handleEdit}
              className="w-full bg-secondary2 border border-text2 rounded-lg px-4 py-2 focus:outline-none focus:ring focus:ring-text2"
              rows={10}
            />
            <div className="flex justify-end mt-4 gap-2 ">
              <Button
                className="bg-close shadow-none hover:bg-close/80"
                onClick={handleOnClose}
              >
                <span className="flex gap-2 items-center">
                  <X size={16} /> Close
                </span>
              </Button>
              <Button onClick={handleUse}>
                <span className="flex gap-2 items-center">
                  <Sparkles size={16} /> Use
                </span>
              </Button>
            </div>
          </div>
        )}
      </DialogContent>
    </Dialog>
  );
};

export default MonologueGenerationDialog;