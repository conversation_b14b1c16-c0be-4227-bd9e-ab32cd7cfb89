import { Button } from "@/components/ui/button";
import { useToast } from "@/components/ui/use-toast";
import supabase from "@/lib/supabase";
import { useGetCoachPlansQuery } from "@/redux/features/coachApi";
import { useGetCurrentPlanQuery } from "@/redux/features/planApi";
import { useEffect, useState } from "react";
import { useNavigate } from "react-router-dom";
import CoachPricingPlan, { CoachPricingPlanSkeleton } from "./CoachPricingPlan";

const CoachPalnsPage = () => {
  const { toast } = useToast();
  const navigate = useNavigate();
  const [isLoading, setIsLoading] = useState(true);

  const {
    data: plansData,
    error: plansError,
    isLoading: plansLoading,
  } = useGetCoachPlansQuery();

  const {
    data: currentPlanData,
    error: currentPlanError,
    isLoading: currentPlanLoading,
    refetch: refetchCurrentPlan,
  } = useGetCurrentPlanQuery();

  useEffect(() => {
    const timer = setTimeout(() => {
      setIsLoading(false);
    }, 1500);

    return () => clearTimeout(timer);
  }, []);

  const handlePlanCancelled = () => {
    refetchCurrentPlan();
    toast({
      title: "Plan Cancelled",
      description: "Your plan has been successfully cancelled.",
      variant: "success",
    });
  };

  const handlePlanError = (errorMessage) => {
    toast({
      title: "Error",
      description: errorMessage,
      variant: "destructive",
    });
  };

  const handleSignout = async () => {
    try {
      await supabase.auth.signOut();
      navigate("/signin");
      toast({ title: "Sign Out Successful" });
    } catch (error) {
      if (error instanceof Error) {
        console.error("Error signing out:", error.message);
      }
    }
  };

  if (plansError || currentPlanError) {
    return (
      <div className="flex justify-center items-center h-screen bg-primary1 text-[#FAFAFA]">
        <p>Error loading plans. Please try again later.</p>
      </div>
    );
  }

  const coachPlans = plansData?.data || [];

  return (
    <div className="flex justify-center flex-col items-center w-full min-h-screen bg-primary1 text-[#FAFAFA]">
      <div className="lg:w-[86%] 2xl:w-[52%] w-full  p-9">
        <div className="flex justify-between items-center  mb-10">
          <div className="flex items-center justify-center">
            <img
              src="/logo.png"
              className="invert mt-4 lg:h-20 lg:w-20 h-14 w-14 transition-all duration-1000 ease-in-out transform group-hover:scale-110 group-hover:rotate-3 animate-pulse mr-8"
              alt="Audition Rooms"
            />
            <h1 className="text-3xl font-extrabold">Coach Plans</h1>
          </div>
          <Button
            onClick={handleSignout}
            variant="outline"
            className="bg-otherBtn text-[#FAFAFA] hover:bg-hover2 hover:text-text1"
          >
            Logout
          </Button>
        </div>
        <div className="grid grid-cols-1 items-center  gap-6">
          {isLoading || plansLoading || currentPlanLoading
            ? Array(1)
                .fill(0)
                .map((_, index) => <CoachPricingPlanSkeleton key={index} />)
            : coachPlans.map((plan) => (
                <CoachPricingPlan
                  key={plan.plan_id}
                  isActive={currentPlanData?.data?.plan_id === plan.plan_id}
                  title={plan.plan_name}
                  price={plan.plan_price_monthly}
                  features={
                    plan.description ? plan.description.split(". ") : []
                  }
                  onCancelSuccess={handlePlanCancelled}
                  plan_id={plan.plan_id}
                  onError={handlePlanError}
                />
              ))}
        </div>
      </div>
    </div>
  );
};

export default CoachPalnsPage;
