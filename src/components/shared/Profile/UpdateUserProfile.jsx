import { useState, useEffect } from "react";
import { useF<PERSON>, Controller } from "react-hook-form";
import { useDispatch } from "react-redux";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Avatar, AvatarImage, AvatarFallback } from "@/components/ui/avatar";
import { Loader, SaveIcon, UploadIcon, X } from "lucide-react";
import { Textarea } from "@/components/ui/textarea";

import { DialogContent } from "@/components/ui/dialog";
import {
  updateProfileLocally,
  useFetchUserProfileQuery,
  useUpdateProfileMutation,
} from "@/redux/features/profileSlice";
import { useToast } from "@/components/ui/use-toast";

const UpdateProfileComponent = ({ onClose }) => {
  const dispatch = useDispatch();
  const { data: profile, isLoading, refetch } = useFetchUserProfileQuery();
  const [updateProfile, { isLoading: isUpdatingProfile }] =
    useUpdateProfileMutation();
  const { toast } = useToast();

  const {
    control,
    handleSubmit,
    formState: { errors },
    setValue,
  } = useForm({
    defaultValues: {
      full_name: profile?.full_name || "",
      email: profile?.email || "",
      profile_description: profile?.profile_description || "",
    },
  });
  const [previewUrl, setPreviewUrl] = useState(profile?.profile_picture_url);

  useEffect(() => {
    if (profile) {
      setValue("full_name", profile.full_name);
      setValue("email", profile.email);
      setValue("profile_description", profile.profile_description);
      setPreviewUrl(profile.profile_picture_url);
    }
  }, [profile, setValue]);

  const onSubmit = async (data) => {
    const formData = new FormData();
    formData.append("full_name", data.full_name);
    formData.append("email", data.email);
    formData.append("profile_description", data.profile_description);
    if (data.profile_picture) {
      formData.append("file", data.profile_picture);
    }

    // Update locally first
    dispatch(
      updateProfileLocally({
        full_name: data.full_name,
        email: data.email,
        profile_description: data.profile_description,
      })
    );

    // Then update on the server
    try {
      await updateProfile(formData).unwrap();
      if (typeof refetch === "function") {
        await refetch();
      }
      toast({
        title: "Profile updated successfully",
        description: "Your profile has been updated successfully.",
        variant: "success",
      });
      onClose();
    } catch (error) {
      console.error("Failed to update profile:", error);
    }
  };

  return (
    <DialogContent className="bg-primary1 border-4 border-otherBtn w-full rounded-xl md:rounded-2xl p-4 lg:p-7 max-w-80 sxl:max-w-[25rem] lg:max-w-[32rem]">
      <form onSubmit={handleSubmit(onSubmit)}>
        <div className="flex flex-col w-full justify-center gap-4">
          <div className="flex items-center gap-4">
            <Avatar className="w-24 h-24 md:w-28 md:h-28 hover:opacity-80 transition-opacity border-2 ">
              <AvatarImage src={previewUrl} alt="Profile picture" />
              <AvatarFallback className="text-primary1">
                {profile?.full_name?.[0] || "U"}
              </AvatarFallback>
            </Avatar>

            <Button
              className={`px-4 md:px-8 py-2 text-xs w-full bg-otherBtn shadow-none text-white hover:bg-hover2`}
              onClick={() =>
                document.getElementById("profile-picture-input").click()
              }
              type="button"
            >
              <div className="flex gap-2">
                <UploadIcon size={16} />
                Upload Profile Picture
              </div>
            </Button>

            <input
              id="profile-picture-input"
              type="file"
              accept="image/*"
              style={{ display: "none" }}
              onChange={(e) => {
                const file = e.target.files[0];
                if (file) {
                  setValue("profile_picture", file);
                  setPreviewUrl(URL.createObjectURL(file));
                }
              }}
            />
          </div>

          <div className="w-full flex flex-col gap-5 xl:gap-8">
            <div>
              <Label
                htmlFor="full_name"
                className="text-xs lg:text-sm font-semibold text-white"
              >
                Full Name
              </Label>
              <Controller
                name="full_name"
                control={control}
                rules={{ required: "Full name is required" }}
                render={({ field }) => (
                  <Input
                    {...field}
                    className="text-xs lg:text-sm text-text1 mt-2 bg-otherBtn border border-border1"
                    placeholder="Enter Full Name"
                  />
                )}
              />
              {errors.full_name && (
                <p className="text-red-500 text-sm">
                  {errors.full_name.message}
                </p>
              )}
            </div>
            <div>
              <Label
                htmlFor="email"
                className="text-xs lg:text-sm font-semibold text-white"
              >
                Email
              </Label>
              <Controller
                name="email"
                control={control}
                render={({ field }) => (
                  <Input
                    {...field}
                    readOnly
                    className="text-xs lg:text-sm text-text1 mt-2 bg-otherBtn border-border1 cursor-not-allowed"
                  />
                )}
              />
            </div>
            <div>
              <Label
                htmlFor="profile_description"
                className="text-xs lg:text-sm font-semibold pt-20 text-white"
              >
                Bio
              </Label>
              <Controller
                name="profile_description"
                control={control}
                rules={{ required: "Describe yourself" }}
                render={({ field }) => (
                  <Textarea
                    {...field}
                    placeholder="Tell us about yourself"
                    className="text-xs lg:text-sm text-text1 mt-2 bg-otherBtn border border-border1"
                    rows={5}
                    maxLength={500}
                  />
                )}
              />
              {errors.profile_description && (
                <p className="text-red-500 text-sm">
                  {errors.profile_description.message}
                </p>
              )}
            </div>
          </div>

          <div className="flex justify-end gap-4">
            <Button
              className={`px-4 py-1`}
              variant="destructive"
              type="button"
              onClick={onClose}
            >
              <div className="flex items-center gap-2 text-xs">
                Cancel
                <X size={13} />
              </div>
            </Button>
            <Button
              className={`px-4 py-1`}
              type="submit"
              disabled={isLoading || isUpdatingProfile}
            >
              <div className="flex items-center gap-2 text-white text-xs">
                {isUpdatingProfile ? (
                  <div className="flex items-center justify-center gap-2">
                    <Loader size={13} className="animate-spin" />
                    Updating...
                    </div>
                ) : (
                  <>
                    Save
                    <SaveIcon size={13} />
                  </>
                )}
              </div>
            </Button>
          </div>
        </div>
      </form>
    </DialogContent>
  );
};

export default UpdateProfileComponent;
