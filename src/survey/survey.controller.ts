import { Body, Controller, Get, HttpException, Post, Query, Req, UseGuards } from '@nestjs/common';
import { SurveyService } from './survey.service';
import { UserGuard } from 'src/auth/guards/user.guard';
import { SubmitSurveyDto } from './dto/submit-survey.dto';

@Controller('survey')
export class SurveyController {
  constructor(private readonly surveyService: SurveyService) { }

  @Post('/submit_survey')
  @UseGuards(UserGuard)
  async submitSurvey(@Req() req, @Body() surveyData: SubmitSurveyDto) {
    const surveyCreateInput = {
      usability_rating: surveyData.useabilityRating,
      overall_experience_rating: surveyData.overallExperienceRating,
      features_description: surveyData.featuresDescription,
      userid: req.user.id,
      email: req.user.email,
      full_name: req.user.user_metadata.full_name,
    };

    return await this.surveyService.createSurvey(surveyCreateInput);
  }

  @Get('/get_all_survey')
  @UseGuards(UserGuard)
  async getAllSurvey(@Query('_start') page: number, @Query('_limit') limit: number) {
    const resp = await this.surveyService.getAllSurvey(page, limit);
    if (resp.error) {
      throw new HttpException(resp.message, resp.status);
    }
    return resp;
  }
}
