import { IsString, <PERSON>NotEmpty, <PERSON>Optional, IsIn } from 'class-validator';

export class CreateMonologueDto {
  @IsString()
  @IsNotEmpty()
  title: string;

  @IsString()
  @IsNotEmpty()
  character: string;

  @IsString()
  @IsNotEmpty()
  category: string;

  @IsString()
  @IsOptional()
  @IsIn(['male', 'female', 'other'])
  gender?: string;

  @IsString()
  @IsNotEmpty()
  content: string;
}
