import React, { useEffect } from "react";
import { useSelector, useDispatch } from "react-redux";
import { useFetchUserProfileQuery } from "@/redux/features/profileSlice";
import ProfileDetail from "./ProfileDetail";
import {
  useGetPostsQuery,
  useGetPrivatePostsQuery,
} from "@/redux/features/postSlice";
import { Skeleton } from "@/components/ui/skeleton";
import { useToast } from "@/components/ui/use-toast";

const UserProfileList = ({ setPublicVideos, setPrivateVideos, changeStatus }) => {
  const {
    data: profile,
    isLoading: isLoadingProfile,
    error: profileError,
  } = useFetchUserProfileQuery();
  const {
    data: posts,
    isLoading: isLoadingPosts,
    error: postsError,
    refetch: reloadPosts,
  } = useGetPostsQuery();
  const {
    data: privatePosts,
    isLoading: isLoadingPrivatePosts,
    error: privatePostsError,
    refetch: reloadPrivatePosts,
  } = useGetPrivatePostsQuery();
  const { toast } = useToast();

  useEffect(() => {
    if (changeStatus === "public") {
      reloadPosts();
    } else if (changeStatus === "private") {
      reloadPrivatePosts();
    }
  }, [changeStatus, reloadPosts, reloadPrivatePosts]);

  useEffect(() => {
    if (profile && posts?.data) {
      const userPosts = posts.data.filter(
        (post) => post.owner === profile.user_id
      );
      const publicVids = userPosts.filter(
        (post) => !post.is_private && post.url
      );
      const privateVids = userPosts.filter(
        (post) => post.is_private && post.url
      );
      setPublicVideos(publicVids);
      setPrivateVideos(privateVids);
    }
  }, [profile, posts, setPublicVideos, setPrivateVideos]);

  useEffect(() => {
    if (profile && privatePosts?.data) {
      const userPrivatePosts = privatePosts.data.filter(
        (post) => post.owner === profile.user_id && post.url
      );
      setPrivateVideos(userPrivatePosts);
    }
  }, [profile, privatePosts, setPrivateVideos]);

  if (profileError || postsError || privatePostsError) {
    toast({
      title: "Error",
      description: "Failed to load profile or posts. Please try again.",
      variant: "destructive",
    });
    return null;
  }

  if (isLoadingProfile || isLoadingPosts || isLoadingPrivatePosts) {
    return (
      <div className="px-4 py-12 lg:p-0 space-y-4">
        <Skeleton className="h-12 w-[250px]" />
        <Skeleton className="h-4 w-[300px]" />
        <div className="flex space-x-4">
          <Skeleton className="h-4 w-[100px]" />
          <Skeleton className="h-4 w-[100px]" />
          <Skeleton className="h-4 w-[100px]" />
        </div>
        <Skeleton className="h-32 w-full" />
      </div>
    );
  }

  if (!profile || !posts?.data || !privatePosts?.data) {
    return null;
  }

  return (
    <div >
      <ProfileDetail />
    </div>
  );
};

export default UserProfileList;
