import React, { useEffect, useState } from "react";
import { useSelector } from "react-redux";
import { useNavigate, useLocation } from "react-router-dom";
import {
  ArrowUp,
  ArrowDown,
  EllipsisVertical,
  Share2,
  Trash2,
  TvMinimalPlay,
  MessageCircle,
} from "lucide-react";
import { Button } from "../../ui/button";
import PostOpenCommentsDialog from "../../PostOpenCommentsDialog";
import { formatTimeAgo } from "@/lib/utils";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog";
import PostShareableLinkDialog from "@/components/PostShareDialog";
import {
  useGetPostByIdQuery,
  useAddUpvoteMutation,
  useRemoveUpvoteMutation,
  useDeletePostMutation,
} from "@/redux/features/postSlice";
import { PostSkeleton } from "./Feed";
import { useGetCurrentPlanQuery } from "@/redux/features/planApi";
import { useToast } from "@/components/ui/use-toast";
import ReactPlayer from 'react-player';
import { preload } from "react-dom";

const Post = ({ onDeletePost, postId, showReplyButton = false, onReply, originalPost }) => {
  const navigate = useNavigate();
  const location = useLocation();

  let post;
  let isLoadingPost = false;
  let errorPost = null;

  if (originalPost) {
    post = originalPost;
  } else {
    const { data, isLoading, error } = useGetPostByIdQuery(postId);
    post = data?.data;
    isLoadingPost = isLoading;
    errorPost = error;
  }
  const [addUpvote] = useAddUpvoteMutation();
  const [removeUpvote] = useRemoveUpvoteMutation();
  const [deletePost] = useDeletePostMutation();

  const [userHasUpvoted, setUserHasUpvoted] = useState(false);
  const [upvoteCount, setUpvoteCount] = useState(0);
  const userId = useSelector((state) => state.user?.session?.user?.id);
  const { data: currentPlanData } = useGetCurrentPlanQuery();
  const { session } = useSelector((state) => state.user);
  const isCoach = session?.user?.metadata?.role === "coach";
  const isAdmin = session?.user?.metadata?.role === "admin";

  const isFreePlan = !currentPlanData?.data?.plan_id;
  const retakesCount = post?.children_posts?.length || 0;
  const { toast } = useToast();
  const [isDeleting, setIsDeleting] = useState(false);


  const showUpgradeToast = () => {
    toast({
      title: "Upgrade Required",
      description:
        "This feature is available for Pro plan users. Upgrade to access.",
      duration: 3000,
    });
  };

  useEffect(() => {
    if (post) {
      setUserHasUpvoted(post.upvoted);
      setUpvoteCount(post.post_upvotes.length);
    }
  }, [post]);

  const handleUpvoteToggle = () => {
    const newHasUpvoted = !userHasUpvoted;
    const adjustment = newHasUpvoted ? 1 : -1;

    // Optimistically update the UI
    setUserHasUpvoted(newHasUpvoted);
    setUpvoteCount((prevCount) => prevCount + adjustment);

    try {
      if (newHasUpvoted) {
        addUpvote(postId).unwrap();
      } else {
        removeUpvote(postId).unwrap();
      }
    } catch (error) {
      // Revert UI changes if the request fails
      setUserHasUpvoted(!newHasUpvoted);
      setUpvoteCount((prevCount) => prevCount - adjustment);

      console.error("Failed to update upvote:", error);
    }
  };

  // const handleDeletePost = async () => {
  //   await deletePost(postId).unwrap();
  //   if (location.pathname === "/") {
  //     onDeletePost();
  //   }
  //   else {
  //     navigate("/");
  //   }
  // };

const handleDeletePost = async () => {
    setIsDeleting(true);

    try {
        await deletePost(postId).unwrap();

        if (location.pathname === "/") {
            onDeletePost();
        } else {
            navigate("/");
        }
    } catch (error) {
        console.error("Failed to delete post:", error);
    } finally {
        setIsDeleting(false);
    }
};

  const handlePostClick = () => {
    navigate(`/post/${postId}`);
  };

  const handleUpvoteClick = (event) => {
    event.stopPropagation();
    if (isFreePlan) {
      event.preventDefault();
      showUpgradeToast();
      return;
    }
    handleUpvoteToggle();
  };

  const handleCommentClick = (event) => {
    event.stopPropagation();
    if (isFreePlan) {
      event.preventDefault();
      showUpgradeToast();
      return;
    }
  };

  const handleShareClick = (event) => {
    event.stopPropagation();
    if (isFreePlan) {
      event.preventDefault();
      showUpgradeToast();
      return;
    }
  };

  const handleReplyClick = (event) => {
    event.stopPropagation();
    if (isFreePlan) {
      event.preventDefault();
      showUpgradeToast();
      return;
    }
    if (onReply) {
      onReply(postId);
    }
  };

  if (isLoadingPost)
    return (
      <div>
        <PostSkeleton />
      </div>
    );
  if (errorPost) return <div>Error: {errorPost.message}</div>;

  return (
    <div
      className="text-text1 py-3 sm:py-5 px-4 sm:px-8 lg:px-0 rounded-lg mb-3 sm:mb-5"
      onClick={handlePostClick}
    >
      <div className="flex justify-between">
        <div className="flex flex-col sm:flex-row sm:items-center sm:gap-4 mb-2 sm:mb-3">
          <h2 className="text-sm sm:text-base lg:text-xl font-semibold">
            {post?.users.metadata?.full_name}
          </h2>
          <span className="text-xs sm:text-sm text-text2">
            {formatTimeAgo(post?.created_at)}
          </span>
        </div>
        {(post?.users.user_id === userId || isAdmin) && (
          <DropdownMenu>
            <DropdownMenuTrigger>
              <EllipsisVertical size={20} />
            </DropdownMenuTrigger>
            <DropdownMenuContent>
              <AlertDialog>
                <AlertDialogTrigger asChild onClick={(e) => e.stopPropagation()} disabled={isDeleting}>
                  <DropdownMenuItem onSelect={(e) => e.preventDefault()}>
                    <Trash2 size={16} className="mr-2" />
                    <span>{isDeleting ? "Deleting..." : "Delete Post"}</span>
                  </DropdownMenuItem>
                </AlertDialogTrigger>
                <AlertDialogContent className="bg-primary1" onClick={(e) => e.stopPropagation()}>
                  <AlertDialogHeader>
                    <AlertDialogTitle className="text-text1">
                      Are you sure?
                    </AlertDialogTitle>
                    <AlertDialogDescription className="text-text1">
                      This action cannot be undone. This will permanently delete
                      your post.
                    </AlertDialogDescription>
                  </AlertDialogHeader>
                  <AlertDialogFooter>
                    <AlertDialogCancel>Cancel</AlertDialogCancel>
                    <AlertDialogAction
                      className="bg-close text-text1"
                      onClick={handleDeletePost}
                    >
                      Delete
                    </AlertDialogAction>
                  </AlertDialogFooter>
                </AlertDialogContent>
              </AlertDialog>
            </DropdownMenuContent>
          </DropdownMenu>
        )}
      </div>
      <p className="mb-2 sm:mb-3 text-xs sm:text-sm lg:text-base font-normal">
        {post?.content}
      </p>
      {post?.url && (
        <div className="mb-3">
          <ReactPlayer
              url={post?.url}
              controls
              playsinline
              width="100%"
              height="100%"
              config={{
                file: {
                  attributes: { preload: "auto" },
                },
              }}
            />
        </div>
        // <div className="relative mb-2 sm:mb-3 mt-2 pb-[56.25%] h-0 overflow-hidden max-w-full bg-primary1">
        //   <iframe
        //     src={post?.url}
        //     title="Video"
        //     frameBorder="0"
        //     allow="accelerometer; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
        //     allowFullScreen
        //     className="absolute top-0 left-0 w-full h-full rounded-lg"
        //   ></iframe> 
        // </div>
      )}
      <div className="flex text-xs sm:text-sm gap-2 sm:gap-4 text-text2">
        <span>
          <strong className="text-text1 mr-1">{upvoteCount}</strong> Upvotes
        </span>
        <span>
          <strong className="text-text1 mr-1">{post?.comments?.length}</strong>{" "}
          Comments
        </span>
        {retakesCount > 0 && (
          <span>
            <strong className="text-text1 mr-1">{retakesCount}</strong>
            {retakesCount === 1 ? "Retake" : "Retakes"}
          </span>
        )}
      </div>
      <div className="pt-2 sm:pt-4 flex">
        <div onClick={handleUpvoteClick}>
          <Button
            className={`px-2 sm:px-4 py-1 sm:py-2 mr-2 sm:mr-4 ${userHasUpvoted ? "bg-hover1 text-text1" : ""}`}
            disabled={isFreePlan}
          >
            {userHasUpvoted ?
            <ArrowDown size={16} />
            :
            <ArrowUp size={16} />            
            }
          </Button>
        </div>
        <div onClick={handleCommentClick} disabled={isFreePlan}>
          <PostOpenCommentsDialog
            comments={post?.comments}
            postId={postId}
            isFreePlan={isFreePlan}
          >
            <Button className="px-2 sm:px-4 py-1 sm:py-2 mr-2 sm:mr-4">
              <MessageCircle size={16} />
              <span className="hidden sm:inline ml-2">Comment</span>
            </Button>
          </PostOpenCommentsDialog>
        </div>
        <div onClick={handleShareClick}>
          <PostShareableLinkDialog
            postId={postId}
            viewToken={post?.is_private ? post?.view_token : undefined}
            isFreePlan={isFreePlan}
          >
            <Button
              className="px-2 sm:px-4 sm:ml-4 ml-2 py-1 sm:py-2 bg-navItemActive text-primary-foreground hover:bg-navItemActive/90"
              disabled={isFreePlan}
            >
              <Share2 size={16} />
              <span className="hidden sm:inline ml-2">Share</span>
            </Button>
          </PostShareableLinkDialog>
        </div>
        {!isCoach && showReplyButton && (
          <Button
            className="px-2 sm:px-4 py-1 sm:py-2 ml-2 sm:ml-4 bg-navItemActive text-text1 hover:bg-navItemActive/90"
            onClick={handleReplyClick}
            disabled={isFreePlan}
          >
            <TvMinimalPlay size={16} />
            <span className="hidden sm:inline ml-2">Retake</span>
          </Button>
        )}
      </div>
    </div>
  );
};

export default Post;
