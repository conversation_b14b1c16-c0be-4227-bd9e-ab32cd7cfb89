import React, { useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  <PERSON><PERSON>,
  DialogTrigger,
  DialogContent,
  DialogTitle,
} from "@/components/ui/dialog";
import { Setting<PERSON>, CheckCircle, BadgeCheck, Loader2 } from "lucide-react";
import { Switch } from "@/components/ui/switch";
import UpdateProfileComponent from "./UpdateUserProfile";
import { useFetchUserProfileQuery } from "@/redux/features/profileSlice";
import { useUpdateCoachStatusMutation } from "@/redux/features/coachApi";
import { useSelector } from "react-redux";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";

const ProfileDetail = () => {
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const {
    data: profile,
    isLoading,
    error,
    refetch,
  } = useFetchUserProfileQuery();
  const [updateCoachStatus] = useUpdateCoachStatusMutation();

  const { session } = useSelector((state) => state.user);
  const isCoach = session?.user?.user_metadata?.role === "coach";
  const [isUpdatingStatus, setIsUpdatingStatus] = useState(false);

  const handleDialogOpen = () => {
    setIsDialogOpen(true);
  };

  const handleDialogClose = () => {
    setIsDialogOpen(false);
  };

  const handleStatusToggle = async (checked) => {
    setIsUpdatingStatus(true);
    const newStatus = checked ? "Available" : "Unavailable";
    try {
      await updateCoachStatus(newStatus).unwrap();
      await refetch();
    } catch (error) {
      console.error("Failed to update coach status", error);
    } finally {
      setIsUpdatingStatus(false);
    }
  };

  if (isLoading) return <p>Loading...</p>;
  if (error) return <p>Error loading profile</p>;
  if (!profile) return null;

  const isAvailable = profile.status === "Available";

  return (
    <div className="flex flex-col p-8 lg:p-0 justify-center items-center w-full">
      <div className="flex flex-col justify-between w-full gap-4 md:gap-0 md:flex-row items-center md:justify-between py-4 mb-8 md:my-8">
        <div className="flex flex-col items-center md:items-start gap-6 w-[90%] md:w-[60%]">
          <h1 className="text-2xl font-extrabold flex items-center gap-2">
            {profile.full_name}
            {isCoach && <BadgeCheck size={20} className="text-green-500 " />}
          </h1>
          <p className="text-sm lg:text-xs xl:text-base text-center md:text-start text-gray-300">
            {profile.profile_description}
          </p>
          {isCoach && (
            <div className="flex items-center gap-2">
              <span>Are you available for work?</span>
              {isUpdatingStatus ? (
                <Loader2
                  className=" animate-spin text-navItemActive"
                  size={24}
                />
              ) : (
                <Switch
                  checked={isAvailable}
                  onCheckedChange={handleStatusToggle}
                />
              )}
            </div>
          )}
          <div className="flex items-center gap-2">
            <p className="video-comment-upvote-text">{`${
              profile.videos || 0
            } Videos`}</p>
            <span className="text-gray-400">.</span>
            <p className="video-comment-upvote-text">{`${
              profile.comments || 0
            } Comments`}</p>
            <span className="text-gray-400">.</span>
            <p className="video-comment-upvote-text">{`${
              profile.upvotes || 0
            } Upvotes`}</p>
          </div>
        </div>
        <div>
          <Avatar className="w-24 h-24 md:w-32 md:h-32 hover:opacity-80 transition-opacity border-2 ">
            <AvatarImage
              src={profile.profile_picture_url}
              alt={profile.full_name}
            />
            <AvatarFallback className="text-text1 bg-primary1">
              {profile?.full_name?.[0] || "U"}
            </AvatarFallback>
          </Avatar>
        </div>
      </div>
      <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
        <DialogTrigger asChild>
          <Button
            className="px-4 py-2 flex justify-center rounded-md w-full bg-otherBtn cursor-pointer shadow-none hover:bg-hover2"
            onClick={handleDialogOpen}
          >
            <div className="flex items-center gap-2">
              <Settings size={16} />
              Edit Profile
            </div>
          </Button>
        </DialogTrigger>
        <DialogTitle>
          <DialogContent>
            <UpdateProfileComponent onClose={handleDialogClose} />
          </DialogContent>
        </DialogTitle>
      </Dialog>
    </div>
  );
};

export default ProfileDetail;
