import {
  Controller,
  Get,
  Param,
  UseGuards,
  HttpException,
  Req,
  Post,
  Body,
} from '@nestjs/common';
import { ExpressionAnalysisService } from './expression-analysis.service';
import { UserGuard } from '../auth/guards/user.guard';
import { ApiTags, ApiOperation, ApiResponse } from '@nestjs/swagger';

@ApiTags('expression-analysis')
@Controller('expression-analysis')
export class ExpressionAnalysisController {
  constructor(
    private readonly expressionAnalysisService: ExpressionAnalysisService,
  ) {}

  @Get('post/:postId')
  @UseGuards(UserGuard)
  @ApiOperation({ summary: 'Get expression analysis results for a specific post' })
  @ApiResponse({ status: 200, description: 'Analysis results retrieved successfully' })
  @ApiResponse({ status: 404, description: 'Post not found' })
  async getPostAnalysis(@Param('postId') postId: string) {
    const response = await this.expressionAnalysisService.getPostAnalysis(
      parseInt(postId, 10),
    );
    
    if (response.error) {
      throw new HttpException(response.message, response.status);
    }
    
    return response;
  }

  @Get('user/history')
  @UseGuards(UserGuard)
  @ApiOperation({ summary: 'Get user\'s expression analysis history' })
  @ApiResponse({ status: 200, description: 'Analysis history retrieved successfully' })
  async getUserAnalysisHistory(@Req() req) {
    const userRowId = req.user.public_user_data[0].id;
    
    const response = await this.expressionAnalysisService.getUserAnalysisHistory(
      userRowId,
    );
    
    if (response.error) {
      throw new HttpException(response.message, response.status);
    }
    
    return response;
  }

  @Get('health')
  @ApiOperation({ summary: 'Health check for expression analysis services' })
  @ApiResponse({ status: 200, description: 'Health check completed' })
  async healthCheck() {
    const response = await this.expressionAnalysisService.healthCheck();
    
    if (response.error) {
      throw new HttpException(response.message, response.status);
    }
    
    return response;
  }

  @Post('retry/:postId')
  @ApiOperation({ summary: 'Start or retry analysis for a post' })
  @ApiResponse({ status: 200, description: 'Analysis started successfully' })
  @ApiResponse({ status: 404, description: 'Post not found' })
  @ApiResponse({ status: 400, description: 'Post has no video to analyze' })
  async retryAnalysis(@Param('postId') postId: string) {
    const result = await this.expressionAnalysisService.retryAnalysisForPost(Number(postId));

    if (result.error) {
      throw new HttpException(result.message, result.status);
    }

    return result;
  }

  @Post('webhook/hume')
  @ApiOperation({ summary: 'Webhook endpoint for HumeAI job completion notifications' })
  @ApiResponse({ status: 200, description: 'Webhook processed successfully' })
  async humeWebhook(@Body() body: any) {
    // This endpoint can be used for HumeAI webhook notifications
    // For now, we're using polling, but this can be enhanced later

    // Log the webhook for debugging
    console.log('HumeAI webhook received:', JSON.stringify(body, null, 2));

    // TODO: Process webhook data and update post status accordingly
    // This would be more efficient than polling

    return { message: 'Webhook received', status: 'ok' };
  }

  @Get('can-analyze/:postId')
  @ApiOperation({ summary: 'Check if a post can be analyzed' })
  @ApiResponse({ status: 200, description: 'Analysis capability check completed' })
  @ApiResponse({ status: 404, description: 'Post not found' })
  async canAnalyze(@Param('postId') postId: string) {
    const result = await this.expressionAnalysisService.canAnalyzePost(Number(postId));

    return {
      canAnalyze: result.canAnalyze,
      reason: result.reason,
      currentStatus: result.post?.analysis_status,
      hasVideo: !!result.post?.url
    };
  }
}
