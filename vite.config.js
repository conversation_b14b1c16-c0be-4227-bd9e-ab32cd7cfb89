import { defineConfig } from "vite";
import react from "@vitejs/plugin-react";
import path from "path";

import fixReactVirtualized from "esbuild-plugin-react-virtualized";

// https://vitejs.dev/config/
export default defineConfig({
  plugins: [react()],
  resolve: {
    alias: {
      "@": path.resolve(__dirname, "./src"),
    },
  },
  scripts: {
    dev: "vite --host",
    build: "tsc && vite build",
    prod: "vite --mode production",
    prod_build: "tsc && vite build && vite preview",
    lint: "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0",
    preview: "vite preview",
  },
  optimizeDeps: {
    esbuildOptions: {
      plugins: [fixReactVirtualized],
    },
  },
});
