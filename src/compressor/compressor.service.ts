import { Injectable } from '@nestjs/common';
import * as ffmpeg from 'fluent-ffmpeg';
import * as ffmpegInstaller from '@ffmpeg-installer/ffmpeg'; // to get ffmpeg 'binary'
import { join } from 'path';
import * as os from 'os';
import * as fs from 'fs';

@Injectable()
export class CompressorService {
  constructor() {
    ffmpeg.setFfmpegPath(ffmpegInstaller.path);
  }

  compress(file: Express.Multer.File): Promise<{
    compressedBuffer: Buffer;
    originalSize: number;
    compressedSize: number;
  }> {
    const originalBuffer = file.buffer;
    // files are written to, read from and cleared from temp folder
    const tempInputPath = join(os.tmpdir(), `${Date.now()}-input.mp4`);
    const tempOutputPath = join(os.tmpdir(), `${Date.now()}-output.mp4`);

    return new Promise((resolve, reject) => {
      const inputWriteStream = fs.createWriteStream(tempInputPath); // create a stream from input which is written to a temp file
      inputWriteStream.write(originalBuffer);
      inputWriteStream.end();

      inputWriteStream.on('finish', () => {
        ffmpeg(tempInputPath)
          .outputOptions([
            '-c:v libx264', // H.265 codec for better compression
            '-crf 28', // lower = better quality, higher = lower quality
            '-r 30', // set fps to 30
          ])
          .save(tempOutputPath) // save post ffmepg operation data to a temp file
          .on('end', () => {
            const compressedBuffer = fs.readFileSync(tempOutputPath); // need this to be returned to replace original videos buffer
            const originalSize = originalBuffer.length / (1024 * 1024); // length is in bytes hence the division, sizes are just for comparison
            const compressedSize = compressedBuffer.length / (1024 * 1024);

            // clean up temporary files
            fs.unlinkSync(tempInputPath);
            fs.unlinkSync(tempOutputPath);

            resolve({
              compressedBuffer,
              originalSize,
              compressedSize,
            });
          })
          .on('error', (err) => {
            reject(err);
          });
      });
    });
  }
}
