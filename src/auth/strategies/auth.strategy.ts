import { Injectable } from '@nestjs/common';
import { PassportStrategy } from '@nestjs/passport';
import { PrismaClient } from '@prisma/client';
import { Strategy, ExtractJwt } from 'passport-jwt';
import { DatabaseService } from '../../database/database.service';
import { USER_ROLE } from '../../types/users.enum';

@Injectable()
export class AuthStrategy extends PassportStrategy(Strategy, 'auth') {
  private db: PrismaClient;
  constructor(private readonly dbService: DatabaseService) {
    super({
      jwtFromRequest: ExtractJwt.fromAuthHeaderAsBearerToken(), // to verify jwt
      ignoreExpiration: false,
      secretOrKey: process.env.SUPABASE_JWT_SECRET,
      passReqToCallback: true, // the request object will be passed to methods called after this
    });

    this.db = dbService.getClient();
  }

  async validate(req: Request) {
    const rawToken = req.headers['authorization'].split(' ')[1];
    // finding the user from the provided jwt and inserting into request object so that it's available to methods after the guard
    // using jwt to get user requires supabase methods that are not available in the prisma client so created a method in db service
    // another reason for using supabase client rather than prisma client in guard because the supabase client is faster in general
    // and we don't want to take up too much time in the middleware.
    const user = await this.dbService.getUserbyToken(rawToken);
    if (user == undefined || user.data.user == null) {
      return false;
    }
    const public_user = await this.dbService.getPublicUser(user.data.user.id);
    if (!('role' in user.data.user.user_metadata)) {
      // since normal user signup is completely controlled by frontend and we do not want role insertion in frontend,
      // if role does not exist in a user we assume they are a normal user. Coaches and admins are assigned their roles in
      // the backend because of their creation flow so assuming any other signued up user without a role is a normal user is acceptable
      user.data.user.user_metadata['role'] = USER_ROLE.NORMAL_USER;
    }
    this.dbService.eventBuffer.push({
      email: user.data.user.email,
      event: req.url,
      headers: JSON.stringify(
        Object.fromEntries(
          Object.entries(req.headers).map(([key, value]) => [
            key.toString(),
            Array.isArray(value) ? value.join(', ') : value.toString(),
          ]),
        ),
      ),
      user_id: user.data.user.id,
    });
    if (this.dbService.eventBuffer.length > 1000) {
      this.dbService.flushEventBuffer();
    }
    return {
      ...user.data.user,
      public_user_data: public_user.data,
      token: rawToken,
    };
  }
}
