@tailwind base;
@tailwind components;
@tailwind utilities;

body {
  font-family: "inter", sans-serif;
}

@layer base {
  :root {
    /* Default to dark mode colors to match the app theme */
    --background: 0 0% 9.2%; /* #171717 equivalent */
    --foreground: 0 0% 100%; /* #FFFFFF equivalent */

    --card: 0 0% 12.2%; /* #1F1F1F equivalent */
    --card-foreground: 0 0% 100%;

    --popover: 0 0% 12.2%;
    --popover-foreground: 0 0% 100%;

    --primary: 245 91% 67%; /* #625BF6 equivalent */
    --primary-foreground: 0 0% 100%;

    --secondary: 0 0% 12.2%; /* #1F1F1F equivalent */
    --secondary-foreground: 0 0% 100%;

    --muted: 0 0% 16.9%; /* #2B2B2B equivalent */
    --muted-foreground: 0 0% 92.2%; /* #EBEBEB equivalent */

    --accent: 245 91% 67%; /* #625BF6 equivalent */
    --accent-foreground: 0 0% 100%;

    --destructive: 0 84% 60%;
    --destructive-foreground: 0 0% 100%;

    --border: 0 0% 27.8%; /* #474747 equivalent */
    --input: 0 0% 27.8%;
    --ring: 245 91% 67%;

    --radius: 0.5rem;
  }

  .dark {
    /* Keep the same values for dark mode since we're defaulting to dark */
    --background: 0 0% 9.2%;
    --foreground: 0 0% 100%;

    --card: 0 0% 12.2%;
    --card-foreground: 0 0% 100%;

    --popover: 0 0% 12.2%;
    --popover-foreground: 0 0% 100%;

    --primary: 245 91% 67%;
    --primary-foreground: 0 0% 100%;

    --secondary: 0 0% 12.2%;
    --secondary-foreground: 0 0% 100%;

    --muted: 0 0% 16.9%;
    --muted-foreground: 0 0% 92.2%;

    --accent: 245 91% 67%;
    --accent-foreground: 0 0% 100%;

    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 0 0% 100%;

    --border: 0 0% 27.8%;
    --input: 0 0% 27.8%;
    --ring: 245 91% 67%;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-primary1 text-text1;
  }

  /* Force dark mode styling */
  html {
    @apply dark;
  }
}

/* ----------------- Custom Classes ----------------- */
.reels-vid-custom-class {
  @apply w-full rounded-md;
}

.video-comment-upvote-text {
  /* @apply text-base lg:text-xl xl:text-2xl font-extrabold; */
  @apply text-gray-400;
}

.chat-messages {
  height: calc(100vh - 18rem);
}

@media (min-width: 1024px) {
  .chat-messages {
    height: calc(100vh - 13rem);
  }
}

.discord-scrollbar {
  scrollbar-width: thin;
  scrollbar-color: rgba(0, 0, 0, 0.2) transparent;
}

.discord-scrollbar::-webkit-scrollbar {
  width: 8px;
}

.discord-scrollbar::-webkit-scrollbar-track {
  background-color: transparent;
}

.discord-scrollbar::-webkit-scrollbar-thumb {
  background-color: rgba(0, 0, 0, 0.2);
  border-radius: 4px;
}

.discord-scrollbar::-webkit-scrollbar-thumb:hover {
  background-color: rgba(0, 0, 0, 0.3);
}

/* Record video dialog scrollbar */


/* @keyframes sparkle {
  0% {
    background-position: 0% 100%, 0% 0%, 100% 0%;
  }
  33% {
    background-position: 100% 0%, 0% 100%, 0% 0%;
  }
  66% {
    background-position: 0% 0%, 100% 100%, 0% 100%;
  }
  100% {
    background-position: 0% 100%, 0% 0%, 100% 0%;
  }
}

.sparkle-border {
  background-image: 
    linear-gradient(90deg, transparent, #625BF6, transparent),
    linear-gradient(90deg, transparent, #625BF6, transparent),
    linear-gradient(0deg, transparent, #625BF6, transparent);
  background-repeat: no-repeat;
  background-size: 100% 2px, 2px 100%, 100% 2px;
  background-position: 0% 100%, 0% 0%, 100% 0%;
  animation: sparkle 4s linear infinite;
  opacity: 0.3;
  transition: opacity 0.3s, background-image 0.3s;
} */

/* @keyframes tilt {
  0%, 50%, 100% {
    transform: rotate(0deg);
  }
  25% {
    transform: rotate(0.5deg);
  }
  75% {
    transform: rotate(-0.5deg);
  }
}

.animate-tilt {
  animation: tilt 10s infinite linear;
}

.group:hover .sparkle-border {
  opacity: 1;
  background-image: 
    linear-gradient(90deg, transparent, #6D66FB, transparent),
    linear-gradient(90deg, transparent, #6D66FB, transparent),
    linear-gradient(0deg, transparent, #6D66FB, transparent);
}

  @keyframes flowSmooth {
    0% { stroke-dashoffset: 0; }
    100% { stroke-dashoffset: -30; }
  }
  @keyframes orbit {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
  }
  @keyframes pulseRefined {
    0%, 100% { transform: scale(1); opacity: 0.3; }
    50% { transform: scale(1.2); opacity: 0.6; }
  }
  @keyframes traverse {
    0% { transform: translateX(0); }
    100% { transform: translateX(80px); }
  }
  .animate-flow-smooth {
    stroke-dasharray: 30;
    animation: flowSmooth 10s linear infinite;
  }
  .animate-flow-smooth-reverse {
    stroke-dasharray: 30;
    animation: flowSmooth 10s linear infinite reverse;
  }
  .animate-orbit {
    transform-origin: 50px 25px;
    animation: orbit 20s linear infinite;
  }
  .animate-pulse-refined {
    animation: pulseRefined 4s ease-in-out infinite;
  }
  .animate-traverse {
    animation: traverse 8s cubic-bezier(0.45, 0.05, 0.55, 0.95) infinite;
  } */

 @keyframes progress {
    0% { width: 0; }
    100% { width: 100%; }
  }
  .animate-progress {
    animation: progress 3s linear infinite;
  }

  /* Cinematic emotion analytics animations */
  @keyframes filmGlow {
    0%, 100% {
      box-shadow: 0 0 20px rgba(98, 91, 246, 0.3);
    }
    50% {
      box-shadow: 0 0 30px rgba(98, 91, 246, 0.6), 0 0 40px rgba(98, 91, 246, 0.3);
    }
  }

  @keyframes emotionPulse {
    0%, 100% {
      transform: scale(1);
      opacity: 0.8;
    }
    50% {
      transform: scale(1.05);
      opacity: 1;
    }
  }

  @keyframes spotlightSweep {
    0% {
      background-position: -100% 0;
    }
    100% {
      background-position: 100% 0;
    }
  }

  .animate-film-glow {
    animation: filmGlow 3s ease-in-out infinite;
  }

  .animate-emotion-pulse {
    animation: emotionPulse 2s ease-in-out infinite;
  }

  .animate-spotlight-sweep {
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.1), transparent);
    background-size: 200% 100%;
    animation: spotlightSweep 3s ease-in-out infinite;
  }

@keyframes fadeIn {
  from { 
    opacity: 0; 
    transform: scale(0.8) rotate(var(--rotation)) translateX(100px) rotate(calc(-1 * var(--rotation))); 
  }
  to { 
    opacity: 1; 
    transform: scale(1) rotate(var(--rotation)) translateX(100px) rotate(calc(-1 * var(--rotation))); 
  }
}

.social-icon {
  --rotation: 0deg;
  animation: fadeIn 0.5s ease-out forwards;
  animation-delay: var(--delay);
}

.resize-none {
  resize: none;
}

  
input:-webkit-autofill,
input:-webkit-autofill:hover,
input:-webkit-autofill:focus,
input:-webkit-autofill:active {
    transition: background-color 5000s ease-in-out 0s;
    -webkit-text-fill-color: #fff !important;
    
}
    

