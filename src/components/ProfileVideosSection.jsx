import { useState, useEffect } from "react";

import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs";
import { useToast } from "@/components/ui/use-toast";
import { ListVideoIcon, Share2 } from "lucide-react";
import { Link } from "react-router-dom";
import PostShareableLinkDialog from "./PostShareDialog";
import { useTogglePostVisibilityMutation } from "@/redux/features/postSlice";

const ProfileVideosSection = ({ publicVideos, privateVideos, setChangeStatus }) => {
  const { toast } = useToast();
  const [localPublicVideos, setLocalPublicVideos] = useState(publicVideos);
  const [localPrivateVideos, setLocalPrivateVideos] = useState(privateVideos);
  const [togglePostVisibility] = useTogglePostVisibilityMutation();

  useEffect(() => {
    setLocalPublicVideos(publicVideos);
    setLocalPrivateVideos(privateVideos);
  }, [publicVideos, privateVideos]);

  const handleToggleVisibility = async (postId, isCurrentlyPrivate) => {
    try {
      await togglePostVisibility(postId).unwrap();

      const newStatus = isCurrentlyPrivate ? "public" : "private";
      toast({
        title: "Video visibility changed",
        description: `The video is now ${newStatus}.`,
        variant: "success",
      });

      // Update local state
      if (isCurrentlyPrivate) {
        const video = localPrivateVideos.find((v) => v.post_id === postId);
        setLocalPrivateVideos((prevVideos) =>
          prevVideos.filter((v) => v.post_id !== postId)
        );
        setLocalPublicVideos((prevVideos) => [
          ...prevVideos,
          { ...video, is_private: false },
        ]);
      } else {
        const video = localPublicVideos.find((v) => v.post_id === postId);
        setLocalPublicVideos((prevVideos) =>
          prevVideos.filter((v) => v.post_id !== postId)
        );
        setLocalPrivateVideos((prevVideos) => [
          ...prevVideos,
          { ...video, is_private: true },
        ]);
      }
      setChangeStatus(newStatus);
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to toggle video visibility. Please try again.",
        variant: "destructive",
      });
      setChangeStatus('');
    }
  };

  const renderVideos = (videos) => {
    if (videos.length === 0) {
      return (
        <div className="flex flex-col items-center justify-center py-8">
          <ListVideoIcon className="text-4xl text-gray-400 mb-2" />
          <p className="text-gray-500">No videos available</p>
        </div>
      );
    }

    return (
      <div className="grid md:grid-cols-2 gap-4">
        {videos.map((video) => (
          <div key={video.post_id} className="relative">
            <Link
              to={`/post/${video.post_id}${
                video.is_private ? `?view_token=${video.view_token}` : ""
              }`}
            >
              <video
                className="reels-vid-custom-class w-full h-64 object-cover"
                controls
                autoPlay
                muted
              >
                <source src={video.url} type="video/mp4" />
              </video>
            </Link>
            <button
              onClick={() =>
                handleToggleVisibility(video.post_id, video.is_private)
              }
              className="absolute top-2 right-2 bg-primary text-primary-foreground hover:bg-primary/90 px-2 py-1 rounded text-sm"
            >
              {video.is_private ? "Make Public" : "Make Private"}
            </button>
            <PostShareableLinkDialog
              postId={video.post_id}
              viewToken={video.is_private ? video.view_token : undefined}
            >
              <button className="absolute bottom-2 right-2 bg-primary text-primary-foreground hover:bg-primary/90 px-2 py-1 rounded text-sm flex items-center">
                <Share2 size={16} className="mr-2" />
                Share
              </button>
            </PostShareableLinkDialog>
          </div>
        ))}
      </div>
    );
  };

  return (
    <div className="my-16 p-8 lg:p-0">
      <Tabs defaultValue="public" className="w-full">
        <TabsList className="grid w-full gap-x-4 grid-cols-2 rounded-xl bg-otherBtn">
          <TabsTrigger
            value="public"
            className="rounded-lg data-[state=active]:bg-primary1 data-[state=active]:text-text1"
          >
            Reels
          </TabsTrigger>
          <TabsTrigger
            value="private"
            className="rounded-lg data-[state=active]:bg-primary1 data-[state=active]:text-text1"
          >
            Videos
          </TabsTrigger>
        </TabsList>
        <TabsContent value="public">
          {renderVideos(localPublicVideos)}
        </TabsContent>
        <TabsContent value="private">
          {renderVideos(localPrivateVideos)}
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default ProfileVideosSection;
