import { <PERSON><PERSON>, Globe, <PERSON>2, <PERSON><PERSON><PERSON>, <PERSON>hare2, MoveRight } from 'lucide-react';
import { <PERSON><PERSON> } from "@/components/ui/button";
import { NavLink } from 'react-router-dom';
import AuditoriumImg from '../../assets/images/auditorium.jpg';
import SpeechImg from '../../assets/images/home-about.png';

const AboutUsSection = ({ showKnowMoreBtn = false, heading, description = [] }) => {
  return (
    <>
      <section
        id="about-us"
        className="relative bg-primary1 text-white items-center lg:px-10  md:px-10 sm:px-10 px-5 pt-20 pb-10"
      >
        <div className="flex gap-8 flex-col sm:flex-row">
          <img
            src={SpeechImg}
            alt="speech on stage"
            className="shadow-2xl rounded-3xl overflow-hidden w-full sm:w-[50%] 2xl:w-[30%] h-[300px] sm:h-[350px] md:h-[450px] lg:h-[530px] xl:h-[480px] object-cover object-bottom"
          ></img>
          <div className="w-full sm:w-[50%] 2xl:w-[70%] h-auto">
            <h2 className="text-2xl lg:text4xl md:text-3xl font-bold text-text1">{heading}</h2>
            <div className="w-[100px] h-[3px] mt-2 bg-navItemActive" />
            <div className="mt-10 text-text2">
              {
                description.map((text, index) => (
                  <div key={index} className="mb-6">
                    {text}
                    <br />
                  </div>
                ))
              }
            </div>
            {showKnowMoreBtn && (
              <div>
                <NavLink to="/about-us">
                  <Button
                    className="bg-navItemActive hover:bg-navItemActive/90 text-white py-3 px-6 rounded-lg h-14"
                  >
                    Know More <MoveRight className="ml-2 h-5 w-5" />
                  </Button>
                </NavLink>
              </div>
            )}
          </div>
        </div>
      </section>
    </>
  );
}
export default AboutUsSection;