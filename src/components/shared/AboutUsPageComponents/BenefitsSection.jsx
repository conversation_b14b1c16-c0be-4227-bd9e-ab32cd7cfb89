import { <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON> } from "lucide-react";
import { AiOutlineSafety } from "react-icons/ai";
//import { BiSolidOffer } from "react-icons/bi";
import { FaBinoculars } from "react-icons/fa6";
import { GiProgression } from "react-icons/gi";

// images import
import EarthImg from '../../../assets/images/earth.jpg';
import growthFeedback from '../../../assets/images/growth-with-feedback.png';
import GrowthImg from '../../../assets/images/growth.png';
import WorkImg from '../../../assets/images/work.png';
import SecureImg from '../../../assets/images/meeting.png';
import MeetingImg from '../../../assets/images/affordable-and-accessible.png';
import ClapperImg from '../../../assets/images/advanced-tools.png';
import talentDiscovery from '../../../assets/images/talent-discovery.png';

const BenefitsCard = ({ icon: Icon, title, description, bgImage }) => (
  <div className={
    `relative p-8 border-8 rounded-3xl border-navItemActive group
    bg-cover bg-center bg-no-repeat flex items-center justify-center overflow-hidden`
  }
    style={{ backgroundImage: `url(${bgImage})` }}
  >
    <div className="absolute top-0 left-0 h-full w-full bg-[linear-gradient(to_right,rgba(0,0,0,0.8)_10%,rgba(0,0,0,0.6)_80%)]
    group-hover:bg-navItemActive group-hover:opacity-100 transition duration-300
    "></div>
    <div className="relative">
      <div className="flex justify-center mb-4">
        <Icon className="h-8 w-8 text-white" />
      </div>
      <h3 className="text-2xl font-bold text-text1 mb-2">
        {title}
      </h3>
      <p className="invisible group-hover:visible transition duration-500 text-text2 text-sm mb-1">{description}</p>
    </div>
  </div>
);

const BenefitsSection = () => {
  return (
    <>
      <h2 className="text-3xl md:text-4xl font-bold py-10 text-text1 text-center">Benefits</h2>
      <div className="lg:p-10 md:p-10 sm:p-10 p-5 flex justify-center">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-8 text-center w-[700px]">
          <BenefitsCard
            icon={LineChart}
            title="Talent Without Boundaries"
            description="Access opportunities and tools from anywhere in the world, breaking the limitations of location."
            bgImage={EarthImg}
          />
          <BenefitsCard
            icon={Globe}
            title="Growth with Feedback"
            description="Refine your skills with constructive feedback and performance tracking tools."
            bgImage={growthFeedback}
          />
          <BenefitsCard
            icon={GiProgression}
            title="Professional Monologues"
            description="Choose from a curated library of monologues for men and women or customize your own ensuring it's free to use."
            bgImage={GrowthImg}
          />
          <BenefitsCard
            icon={Share2}
            title="Social Visibility"
            description="Share your auditions on platforms like TikTok and Instagram to connect with your peers."
            bgImage={WorkImg}
          />
          <BenefitsCard
            icon={AiOutlineSafety}
            title="One-on-One Coaching"
            description="Find specialized coaches for personalized training in techniques like Shakespeare, Meisner, or improvisation."
            bgImage={SecureImg}
          />
          <BenefitsCard
            icon={FaBinoculars}
            title="Affordable and Accessible"
            description=" Join our platform at a fraction of the cost of traditional acting workshops."
            bgImage={MeetingImg}
          />
          <BenefitsCard
            icon={BadgeCheck}
            title="Advanced Tools for Actors"
            description="Leverage AI-powered features and intuitive editing tools to create polished submissions."
            bgImage={ClapperImg}
          />
          <BenefitsCard
            icon={BadgeCheck}
            title="Talent Discovery Community"
            description="Our digital platform can help you connect to the right audience across the community quickly and effortlessly."
            bgImage={talentDiscovery}
          />
        </div>
      </div >
    </>
  )
}

export default BenefitsSection