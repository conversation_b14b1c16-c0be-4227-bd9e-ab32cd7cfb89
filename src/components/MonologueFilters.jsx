import React, { useState } from 'react';
import { ChevronDown, ChevronUp, X, Filter } from "lucide-react";
import MonologueSearchBar from './MonologueSearchBar';

const MonologueFilters = ({
  searchTerm,
  onSearchChange,
  chips,
  activeChip,
  genderOptions,
  categoryOptions,
  activeGender,
  activeCategory,
  activeFilters = [],
  onChipChange,
  onGenderChange,
  onCategoryChange,
  onRemoveFilter,
  onClearAll
}) => {
  const [isFilterOpen, setIsFilterOpen] = useState(false);
  const hasActiveFilters = activeFilters.length > 0 || searchTerm;

  return (
    <div className="bg-primary1 rounded-lg border border-text2/20 shadow-sm divide-y divide-text2/10">
      {/* Search Bar Section */}
      <div className="p-4">
        <MonologueSearchBar
          value={searchTerm}
          onChange={onSearchChange}
          placeholder="Search by title, character or content..."
        />
      </div>

      {/* Filter Section */}
      <div className="divide-y divide-text2/10">
        {/* Filter Header */}
        <div className="px-4 py-3 flex items-center justify-between">
          <button
            onClick={() => setIsFilterOpen(!isFilterOpen)}
            className="flex items-center gap-2 text-text1 hover:text-text2 transition-colors duration-200"
          >
            <Filter 
              size={20} 
              className={activeFilters.length > 0 ? "text-secondary2" : ""} 
            />
            <span className="font-medium">
              {activeFilters.length > 0 
                ? `Filters (${activeFilters.length})` 
                : "Filters"}
            </span>
            {isFilterOpen ? <ChevronUp size={20} /> : <ChevronDown size={20} />}
          </button>
          {hasActiveFilters && (
            <button
              onClick={onClearAll}
              className="text-text2 hover:text-text1 text-sm font-medium"
            >
              Clear all
            </button>
          )}
        </div>

        {/* Active Filters */}
        {activeFilters.length > 0 && (
          <div className="px-4 py-3">
            <div className="flex flex-wrap gap-2">
              {activeFilters.map((filter, index) => (
                <span
                  key={index}
                  className="inline-flex items-center gap-1 px-3 py-1.5 rounded-full 
                           bg-white text-primary1 text-sm group"
                >
                  {filter.value}
                  <button
                    onClick={() => onRemoveFilter(filter)}
                    className="hover:text-primary1 ml-1 opacity-60 group-hover:opacity-100 
                             transition-opacity duration-200"
                  >
                    <X size={14} />
                  </button>
                </span>
              ))}
            </div>
          </div>
        )}

        {/* Collapsible Filter Content */}
        <div
          className={`overflow-hidden transition-all duration-300 ease-in-out
                     ${isFilterOpen ? "max-h-[500px]" : "max-h-0"}`}
        >
          <div className="p-4 space-y-6">
            {/* Collection Filter */}
            <div className="space-y-2">
              <label className="text-sm font-medium text-text1">Collection</label>
              <div className="flex flex-wrap gap-2">
                <FilterChip
                  active={activeChip === "All"}
                  onClick={() => onChipChange("All")}
                >
                  All
                </FilterChip>
                {chips.map((chip) => (
                  <FilterChip
                    key={chip}
                    active={activeChip === chip}
                    onClick={() => onChipChange(chip)}
                  >
                    {chip}
                  </FilterChip>
                ))}
              </div>
            </div>

            {/* Gender and Category Grid */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {/* Gender Filter */}
              <div className="space-y-2">
                <label className="text-sm font-medium text-text1">Gender</label>
                <div className="flex flex-wrap gap-2">
                  <FilterChip
                    active={activeGender === "All"}
                    onClick={() => onGenderChange("All")}
                  >
                    All
                  </FilterChip>
                  {genderOptions.map((gender) => (
                    <FilterChip
                      key={gender}
                      active={activeGender === gender}
                      onClick={() => onGenderChange(gender)}
                    >
                      {gender}
                    </FilterChip>
                  ))}
                </div>
              </div>

              {/* Category Filter */}
              <div className="space-y-2">
                <label className="text-sm font-medium text-text1">Type</label>
                <div className="flex flex-wrap gap-2">
                  <FilterChip
                    active={activeCategory === "All"}
                    onClick={() => onCategoryChange("All")}
                  >
                    All
                  </FilterChip>
                  {categoryOptions.map((category) => (
                    <FilterChip
                      key={category}
                      active={activeCategory === category}
                      onClick={() => onCategoryChange(category)}
                    >
                      {category}
                    </FilterChip>
                  ))}
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

const FilterChip = ({ children, active, onClick }) => {
  return (
    <button
      onClick={onClick}
      className={`px-3 py-1.5 rounded-full text-sm font-medium transition-all duration-200
                ${active 
                  ? "bg-white text-primary1" 
                  : "bg-secondary2/10 text-text2 hover:bg-secondary2/20"}`}
    >
      {children}
    </button>
  );
};

export default MonologueFilters;