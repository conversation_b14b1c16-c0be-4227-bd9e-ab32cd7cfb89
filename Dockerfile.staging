FROM node:20.11.0

# Install pnpm
RUN npm install -g pnpm

# Set the working dir
WORKDIR /app

# Copy application code
COPY . .

# Generate Prisma Client with the correct binary
RUN npx prisma generate --schema=/app/prisma/schema.prisma

# Install dependencies
RUN pnpm install

# Build application
RUN pnpm run build

# Expose the port the app runs on
EXPOSE 3000

# Start application
CMD ["pnpm", "run", "start:staging"]
