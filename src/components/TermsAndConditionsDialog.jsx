import React from "react";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  Di<PERSON>Header,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";

import { ScrollArea } from "@/components/ui/scroll-area";

const TermsAndConditionsDialog = ({ children }) => {
  return (
    <Dialog>
      <DialogTrigger asChild>{children}</DialogTrigger>
      <DialogContent className="sm:max-w-2xl h-[90vh] p-0 gap-0 bg-white dark:bg-gray-900">
        <DialogHeader className="px-6 py-4 border-b border-gray-200 dark:border-gray-800 sticky top-0 bg-white dark:bg-gray-900 z-10">
          <DialogTitle className="text-2xl font-bold bg-gradient-to-r from-primary to-primary/80 bg-clip-text text-transparent">
            Terms and Conditions
          </DialogTitle>
          <DialogDescription className="text-gray-500 dark:text-gray-400 mt-1">
            Please read these terms and conditions carefully before using our
            service.
          </DialogDescription>
        </DialogHeader>

        <ScrollArea className="flex-1 p-6">
          <div className="space-y-8 mb-5">
            {/* 1. Introduction */}
            <section className="rounded-lg border border-gray-100 dark:border-gray-800 p-6 hover:shadow-md transition-all duration-300 bg-gray-50/50 dark:bg-gray-800/50">
              <h3 className="text-xl font-semibold mb-3 text-primary">
                1. Introduction
              </h3>
              <p className="text-gray-600 dark:text-gray-300 leading-relaxed">
                By accessing and using this website and our services, you accept
                and agree to be bound by the terms and conditions set forth
                herein. If you do not agree to these terms, please do not use
                our service.
              </p>
            </section>

            {/* 2. User Obligations */}
            <section className="rounded-lg border border-gray-100 dark:border-gray-800 p-6 hover:shadow-md transition-all duration-300 bg-gray-50/50 dark:bg-gray-800/50">
              <h3 className="text-xl font-semibold mb-3 text-primary">
                2. User Obligations
              </h3>
              <div className="space-y-3">
                <p className="text-gray-600 dark:text-gray-300 leading-relaxed pl-4 border-l-2 border-primary/20">
                  2.1. You must be at least 18 years old to use this service.
                </p>
                <p className="text-gray-600 dark:text-gray-300 leading-relaxed pl-4 border-l-2 border-primary/20">
                  2.2. You agree to provide accurate, current, and complete
                  information during registration.
                </p>
                <p className="text-gray-600 dark:text-gray-300 leading-relaxed pl-4 border-l-2 border-primary/20">
                  2.3. You are responsible for maintaining the confidentiality
                  of your account and password.
                </p>
                <p className="text-gray-600 dark:text-gray-300 leading-relaxed pl-4 border-l-2 border-primary/20">
                  2.4. You agree not to share your account credentials with any
                  third party.
                </p>
              </div>
            </section>

            {/* 3. Privacy Policy */}
            <section className="rounded-lg border border-gray-100 dark:border-gray-800 p-6 hover:shadow-md transition-all duration-300 bg-gray-50/50 dark:bg-gray-800/50">
              <h3 className="text-xl font-semibold mb-3 text-primary">
                3. Privacy Policy
              </h3>
              <p className="text-gray-600 dark:text-gray-300 leading-relaxed">
                Your privacy is important to us. Our Privacy Policy explains how
                we collect, use, protect, and when necessary, disclose your
                personal information. Please review our Privacy Policy, which is
                incorporated into these Terms and Conditions by reference.
              </p>
            </section>

            {/* 4. Service Usage */}
            <section className="rounded-lg border border-gray-100 dark:border-gray-800 p-6 hover:shadow-md transition-all duration-300 bg-gray-50/50 dark:bg-gray-800/50">
              <h3 className="text-xl font-semibold mb-3 text-primary">
                4. Service Usage
              </h3>
              <div className="space-y-3">
                <p className="text-gray-600 dark:text-gray-300 leading-relaxed pl-4 border-l-2 border-primary/20">
                  4.1. You agree to use the service only for lawful purposes.
                </p>
                <p className="text-gray-600 dark:text-gray-300 leading-relaxed pl-4 border-l-2 border-primary/20">
                  4.2. You agree not to engage in any activity that interferes
                  with or disrupts the service.
                </p>
                <p className="text-gray-600 dark:text-gray-300 leading-relaxed pl-4 border-l-2 border-primary/20">
                  4.3. We reserve the right to terminate or suspend accounts
                  that violate these terms.
                </p>
              </div>
            </section>

            {/* 5. Content */}
            <section className="rounded-lg border border-gray-100 dark:border-gray-800 p-6 hover:shadow-md transition-all duration-300 bg-gray-50/50 dark:bg-gray-800/50">
              <h3 className="text-xl font-semibold mb-3 text-primary">
                5. Content
              </h3>
              <div className="space-y-3">
                <p className="text-gray-600 dark:text-gray-300 leading-relaxed pl-4 border-l-2 border-primary/20">
                  5.1. Users are responsible for all content they post or share
                  through our service.
                </p>
                <p className="text-gray-600 dark:text-gray-300 leading-relaxed pl-4 border-l-2 border-primary/20">
                  5.2. We reserve the right to remove any content that violates
                  these terms.
                </p>
                <p className="text-gray-600 dark:text-gray-300 leading-relaxed pl-4 border-l-2 border-primary/20">
                  5.3. You retain ownership of your content but grant us a
                  license to use it for service operation.
                </p>
              </div>
            </section>

            {/* 6. Payments and Refunds */}
            <section className="rounded-lg border border-gray-100 dark:border-gray-800 p-6 hover:shadow-md transition-all duration-300 bg-gray-50/50 dark:bg-gray-800/50">
              <h3 className="text-xl font-semibold mb-3 text-primary">
                6. Payments and Refunds
              </h3>
              <div className="space-y-3">
                <p className="text-gray-600 dark:text-gray-300 leading-relaxed pl-4 border-l-2 border-primary/20">
                  6.1. All payments are processed securely through our payment
                  providers.
                </p>
                <p className="text-gray-600 dark:text-gray-300 leading-relaxed pl-4 border-l-2 border-primary/20">
                  6.2. Refunds are provided in accordance with our refund
                  policy.
                </p>
                <p className="text-gray-600 dark:text-gray-300 leading-relaxed pl-4 border-l-2 border-primary/20">
                  6.3. We reserve the right to modify our pricing with
                  appropriate notice.
                </p>
              </div>
            </section>

            {/* 7. Termination */}
            <section className="rounded-lg border border-gray-100 dark:border-gray-800 p-6 hover:shadow-md transition-all duration-300 bg-gray-50/50 dark:bg-gray-800/50">
              <h3 className="text-xl font-semibold mb-3 text-primary">
                7. Termination
              </h3>
              <p className="text-gray-600 dark:text-gray-300 leading-relaxed">
                We reserve the right to terminate or suspend access to our
                service immediately, without prior notice or liability, for any
                reason whatsoever, including without limitation if you breach
                the Terms and Conditions.
              </p>
            </section>

            {/* 8. Changes to Terms */}
            <section className="rounded-lg border border-gray-100 dark:border-gray-800 p-6 hover:shadow-md transition-all duration-300 bg-gray-50/50 dark:bg-gray-800/50">
              <h3 className="text-xl font-semibold mb-3 text-primary">
                8. Changes to Terms
              </h3>
              <p className="text-gray-600 dark:text-gray-300 leading-relaxed">
                We reserve the right to modify these terms at any time. We will
                notify users of any material changes via email or through the
                service. Your continued use of the service following such
                modifications constitutes your acceptance of the modified terms.
              </p>
            </section>

            {/* 9. Contact Information */}
            <section className="rounded-lg border border-gray-100  dark:border-gray-800 p-6 hover:shadow-md transition-all duration-300 bg-gray-50/50 dark:bg-gray-800/50">
              <h3 className="text-xl font-semibold mb-3 text-primary">
                9. Contact Information
              </h3>
              <p className="text-gray-600 dark:text-gray-300 leading-relaxed">
                If you have any questions about these Terms and Conditions,
                please contact <NAME_EMAIL>
              </p>
            </section>
          </div>
        </ScrollArea>
      </DialogContent>
    </Dialog>
  );
};

export default TermsAndConditionsDialog;
