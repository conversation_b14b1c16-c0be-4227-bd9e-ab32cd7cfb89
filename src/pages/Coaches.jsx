import React, { useState } from "react";
import { useNavigate } from "react-router-dom";
import { useSelector } from "react-redux";
import { AnimatePresence, motion } from "framer-motion";
import ScreenTopSection from "@/components/ScreenTopSection";
import { <PERSON>rk<PERSON> } from "lucide-react";
import { useGetCoachesQuery } from "@/redux/features/coachApi";
import {
  useCreateChatMutation,
  
} from "@/redux/features/chatApi";
import CoachSearchBar from "@/components/shared/Coaches/CoachSearchBar";
import CoachCardSkeleton from "@/components/shared/Coaches/CoachCardSkeleton";
import CoachCard from "@/components/shared/Coaches/CoachCard";

const Coaches = () => {
  const [showSearch, setShowSearch] = useState(false);
  const { data: coaches = [], isLoading, error } = useGetCoachesQuery();
  const [createChat] = useCreateChatMutation();

  const navigate = useNavigate();
  const currentUserId = useSelector((state) => state.user.session?.user?.id);

  const toggleSearch = () => setShowSearch(!showSearch);

   const handleHire = async (coach) => {
     if (coach.user_id === currentUserId) {
       console.error("Cannot start a chat with yourself");
       return;
     }
     try {
       const result = await createChat(  coach.user_id ).unwrap();
       navigate(`/messages/${result.data.id}`, { state: { coachData: coach } });
     } catch (error) {
       console.error("Failed to create chat:", error);
     }
   };
  
  const availableCoaches = coaches.filter(
    (coach) => coach && coach.status === "Available"
  );


  return (
    <div className="flex flex-col justify-center items-center w-full">
      <div className="lg:w-[52%] w-full ">
        <ScreenTopSection
          page="Coaches"
          btnText="Filter"
          Icon={Sparkles}
          onClick={toggleSearch}
        />
        <AnimatePresence>
          {showSearch && (
            <motion.div
              initial={{ opacity: 0, height: 0 }}
              animate={{ opacity: 1, height: "auto" }}
              exit={{ opacity: 0, height: 0 }}
              transition={{ duration: 0.3 }}
            >
              <CoachSearchBar
                coaches={coaches}
                setShowSearch={setShowSearch}
                onHire={handleHire}
                currentUserId={currentUserId}
              />
            </motion.div>
          )}
        </AnimatePresence>
        <div className="w-full mt-8">
          {isLoading ? (
            <CoachCardSkeleton />
          ) : error ? (
            <p className="text-close">Error loading coaches: {error.message}</p>
          ) : availableCoaches.length > 0 ? (
            availableCoaches.map((coach) => (
              <CoachCard
                key={coach.id}
                coach={coach}
                onHire={() => handleHire(coach)}
                currentUserId={currentUserId}
              />
            ))
          ) : (
            <p className="text-text2">No coaches found</p>
          )}
        </div>
      </div>
    </div>
  );
};

export default Coaches;
