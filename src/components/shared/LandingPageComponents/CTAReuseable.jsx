
import { Button } from "@/components/ui/button";

const CTAReuseable = ({
  title,
  description,
  buttonText,
  buttonIcon: ButtonIcon,
  className = "",
  onClick = () => {},
}) => {
  return (
    <div
      className={`relative bg-secondary2 rounded-3xl p-8 transition-all duration-300 hover:bg-[#252525] group md:p-12 overflow-hidden ${className}`}
    >
      <div className="absolute inset-0 z-0 overflow-hidden">
        <div className="absolute inset-0 sparkle-border opacity-30"></div>
      </div>
      <div className="relative z-10 flex flex-col md:flex-row items-center justify-between">
        <div className="mb-6 md:mb-0 md:mr-8">
          <h3 className="text-3xl font-bold mb-4 group-hover:text-navItemActive transition-colors duration-300">
            {title}
          </h3>
          <p className="text-text2 text-lg mb-6">{description}</p>
        </div>
        <Button
          size="lg"
          className="text-lg font-bold px-8 py-4 bg-navItemActive hover:bg-hover1 transition-all duration-300 transform hover:scale-105 flex items-center"
          onClick={onClick}
        >
          {buttonText}
          <ButtonIcon className="ml-2 h-5 w-5" />
        </Button>
      </div>
      <div className="absolute bottom-0 right-0 opacity-10">
        <svg
          width="200"
          height="200"
          viewBox="0 0 200 200"
          fill="none"
          xmlns="http://www.w3.org/2000/svg"
        >
          <circle cx="100" cy="100" r="100" fill="url(#paint0_linear)" />
          <defs>
            <linearGradient
              id="paint0_linear"
              x1="0"
              y1="0"
              x2="200"
              y2="200"
              gradientUnits="userSpaceOnUse"
            >
              <stop stopColor="#625BF6" />
              <stop offset="1" stopColor="#6D66FB" />
            </linearGradient>
          </defs>
        </svg>
      </div>
    </div>
  );
};

export default CTAReuseable;
