import React from "react";
import { Skeleton } from "@/components/ui/skeleton";

const CoachCardSkeleton = ({ count = 8 }) => (
  <>
    {[...Array(count)].map((_, index) => (
      <div
        key={index}
        className=" rounded-lg p-4 mb-4 flex justify-between items-center"
      >
        <div className="flex items-center gap-4">
          <Skeleton className="w-12 h-12 rounded-full" />
          <div>
            <Skeleton className="h-5 w-32 mb-2" />
            <div className="flex gap-1">
              <Skeleton className="h-4 w-16" />
              <Skeleton className="h-4 w-16" />
              <Skeleton className="h-4 w-16" />
            </div>
          </div>
        </div>
        <Skeleton className="h-9 w-20" />
      </div>
    ))}
  </>
);

export default CoachCardSkeleton;
