import { createApi, fetchBaseQuery } from "@reduxjs/toolkit/query/react";
import { fetchAccessToken } from "./chatSlice";

const planApi = createApi({
  baseQuery: fetchBaseQuery({
    baseUrl: import.meta.env.VITE_API_BASE_URL,
    prepareHeaders: async (headers, { endpoint }) => {
      if (endpoint !== "getAllPlans") {
        const token = await fetchAccessToken();
        if (token) {
          headers.set("authorization", `Bearer ${token}`);
        }
      }
      return headers;
    },
  }),
  keepUnusedDataFor: 600,
  endpoints: (builder) => ({
    getUserPlans: builder.query({
      query: () => "plans/get_plans_user",
    }),
    getCurrentPlan: builder.query({
      query: () => "plans/get_current_plan",
    }),
    getStripeDashboard: builder.query({
      query: () => "plans/dashboard",
    }),
    getAllPlans: builder.query({
      query: () => "plans/get_all_plans",
    }),
    cancelPlan: builder.mutation({
      query: () => ({
        url: "plans/cancel_plan",
        method: "PATCH",
      }),
    }),
    buyPlan: builder.mutation({
      query: (planId) => ({
        url: `plans/buy_plan/${planId}`,
        method: "POST",
      }),
    }),
  }),
});

export const {
  useGetUserPlansQuery,
  useGetCurrentPlanQuery,
  useGetStripeDashboardQuery,
  useGetAllPlansQuery,
  useCancelPlanMutation,
  useBuyPlanMutation,
} = planApi;

export default planApi;
