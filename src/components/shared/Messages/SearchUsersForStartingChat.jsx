import  { useState, useEffect } from "react";
import { useNavigate } from "react-router-dom";
import { debounce } from "lodash";
import { Button } from "@/components/ui/button";
import { Loader2, Search } from "lucide-react";
import placeholderImage from "@/assets/images/blank-profile-picture.png";

import { useSelector } from "react-redux";
import {
  useCreateChatMutation,
  useGetChatsQuery,
  useSearchUsersQuery,
} from "@/redux/features/chatApi";

const SearchUsersForStartingChat = ({ setShowSearch }) => {
  const navigate = useNavigate();
  const [searchQuery, setSearchQuery] = useState("");
  const [createChat] = useCreateChatMutation();
  const currentUserId = useSelector((state) => state.user.session?.user?.id);
  const { refetch } = useGetChatsQuery();

 

  const {
    data: searchResults = [],
    isLoading: searchLoading,
    isSuccess: searchSuccess,
  } = useSearchUsersQuery(searchQuery, {
    skip: !searchQuery.trim(),
  });

  const searchResultsData = searchResults?.data;

 

  const debouncedSearch = debounce((query) => {
    setSearchQuery(query);
  }, 300);

  useEffect(() => {
    debouncedSearch(searchQuery);
    return () => debouncedSearch.cancel();
  }, [searchQuery]);

 const handleCreateChat = async (user) => {
   if (user.user_id === currentUserId) {
     console.error("Cannot start a chat with yourself");
     return;
   }
   try {
     const result = await createChat(user.user_id).unwrap();
     setShowSearch(false);
     navigate(`/messages/${result.data.id}`, { state: { searchResult: user } });
     await refetch();
   } catch (error) {
     console.error("Failed to create chat:", error);
   }
 };

  return (
    <div className="lg:mt-4 mt-8 flex flex-col items-center w-full">
      <div className="flex gap-2 mb-4 lg:w-full w-[96%]">
        <input
          type="text"
          placeholder="Search users..."
          value={searchQuery}
          onChange={(e) => setSearchQuery(e.target.value)}
          className="flex-grow p-2 bg-chatSec text-text1 rounded-md border border-gray-700 focus:outline-none focus:ring-2 focus:ring-navItemActive"
        />
        <Button className="bg-navItemActive hover:bg-navItemActive/80">
          <Search size={16} />
        </Button>
      </div>

      {searchLoading && (
        <div className="flex justify-center items-center w-full mt-4">
          <Loader2 className="animate-spin text-navItemActive" size={40} />
        </div>
      )}
      {searchSuccess && searchResultsData.length > 0 && (
        <div className="bg-chatSec rounded-lg p-4 w-full mt-4">
          <h3 className="text-lg font-semibold mb-2">Search Results</h3>
          {searchResultsData
            .filter((user) => user.user_id !== currentUserId)
            .map((user) => (
              <div
                key={user.user_id}
                className="flex items-center gap-2 p-2 hover:bg-gray-700 rounded-xl cursor-pointer transition-colors duration-200"
                onClick={() => handleCreateChat(user)}
              >
                <img
                  src={
                    user.profile_picture_url || placeholderImage
                  }
                  alt={user.full_name}
                  className="w-10 h-10 rounded-full object-cover"
                />
                <span className="font-medium text-white">
                  {user?.metadata.full_name}
                </span>
              </div>
            ))}
        </div>
      )}
    </div>
  );
};

export default SearchUsersForStartingChat;
