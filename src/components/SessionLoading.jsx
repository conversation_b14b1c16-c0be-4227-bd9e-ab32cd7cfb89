const SessionLoading = () => (
  <div className="fixed inset-0 flex items-center justify-center bg-primary1 z-50 transition-opacity duration-300">
    <div className="relative">
      <svg
        className="w-24 h-24"
        viewBox="0 0 100 100"
        xmlns="http://www.w3.org/2000/svg"
      >
        <circle
          cx="50"
          cy="50"
          r="45"
          fill="none"
          stroke="#2C2C2C"
          strokeWidth="8"
        />
        <circle
          cx="50"
          cy="50"
          r="45"
          fill="none"
          stroke="#6D66FB"
          strokeWidth="8"
          strokeDasharray="70 283"
          strokeLinecap="round"
        >
          <animateTransform
            attributeName="transform"
            type="rotate"
            from="0 50 50"
            to="360 50 50"
            dur="1.5s"
            repeatCount="indefinite"
          />
        </circle>
      </svg>
      <div className="absolute inset-0 flex items-center justify-center">
        <div className="w-12 h-12 bg-secondary2 rounded-full animate-pulse"></div>
      </div>
    </div>
  </div>
);

export default SessionLoading;