import { But<PERSON> } from "@/components/ui/button";
import { Circle<PERSON>heckBig } from "lucide-react";
import { FaAward } from "react-icons/fa6";
import { Link } from "react-router-dom";

const Plans = ({ title,
  price,
  features,
  type
}) => {
  return (
    <div
      className={`relative rounded-lg p-8 flex flex-col h-full bg-navItemActive`}
    >
      <div className="absolute w-full left-0 top-[-9px] flex justify-center">
        <FaAward size={40} className="" />
      </div>

      <h3
        className={`text-center text-xl font-bold text-white py-5`}
      >
        {title}
      </h3>
      <p
        className={`text-center text-3xl font-bold mb-6 text-white`}
      >
        ${price}/mo
      </p>
      <ul className="flex-grow mb-6">
        {features.map((feature, index) => (
          <li
            key={index}
            className={`mb-3 flex items-center text-white`}
          >
            <CircleCheckBig className="w-5 h-5 mr-3 flex-shrink-0 mt-0.5" />
            {feature}
          </li>
        ))}
      </ul>
      <Link to={type === 2 ? "/coach-apply" : "/signup"}>
        <Button className={`w-full py-3 rounded-lg font-semibold 
         bg-white text-primary1 hover:bg-[#383481] hover:text-white`}>
          Get Started
        </Button>
      </Link>
    </div>
  );
}

export default Plans;