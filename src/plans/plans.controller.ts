import {
  Controller,
  Get,
  HttpException,
  Param,
  Patch,
  Post,
  Req,
  UseGuards,
} from '@nestjs/common';
import { PlansService } from './plans.service';
import { UserGuard } from '../auth/guards/user.guard';
import { PLANS_TYPE } from '../types/users.enum';

@Controller('plans')
export class PlansController {
  constructor(private readonly plansService: PlansService) {}

  @Get('/get_plans_user')
  @UseGuards(UserGuard)
  async getPlansUser() {
    const resp = await this.plansService.getPlans(PLANS_TYPE.USER_PLAN); // there will be specific plans for normal users and coaches so we can fetch by name
    if (resp.error) {
      throw new HttpException(resp.message, resp.status);
    }
    return resp;
  }

  @Get('/get_all_plans')
  async getAllPlans() {
    const resp = await this.plansService.getAllPlans();
    if (resp.error) {
      throw new HttpException(resp.message, resp.status);
    }
    return resp;
  }

  @Get('/get_plans_coach')
  @UseGuards(UserGuard)
  async getPlansCoach() {
    const resp = await this.plansService.getPlans(PLANS_TYPE.COACH_PLAN);
    if (resp.error) {
      throw new HttpException(resp.message, resp.status);
    }
    return resp;
  }

  @Get('/get_current_plan')
  @UseGuards(UserGuard)
  async getCurrentPlan(@Req() req) {
    const resp = await this.plansService.getCurrentPlan(req.user.id);
    if (resp.error) {
      throw new HttpException(resp.message, resp.status);
    }
    return resp;
  }

  @Get('/dashboard')
  @UseGuards(UserGuard)
  async getStripeDashboard(@Req() req) {
    const resp = await this.plansService.getStripeDashboard(req.user.email);
    if (resp.error) {
      throw new HttpException(resp.message, resp.status);
    }
    return resp;
  }

  @Post('/buy_plan/:plan_id')
  @UseGuards(UserGuard)
  async buyPlan(@Req() req, @Param('plan_id') plan_id: number) {
    const resp = await this.plansService.buyPlan(req.user.email, plan_id);
    if (resp.error) {
      throw new HttpException(resp.message, resp.status);
    }
    return resp;
  }

  @Patch('/cancel_plan')
  @UseGuards(UserGuard)
  async cancelPlan(@Req() req) {
    const resp = await this.plansService.cancelPlan(req.user.email);
    if (resp.error) {
      throw new HttpException(resp.message, resp.status);
    }
    return resp;
  }
}
