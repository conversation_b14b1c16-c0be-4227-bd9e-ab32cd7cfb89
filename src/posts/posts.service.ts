import { HttpStatus, Injectable } from '@nestjs/common';
import { CreatePostDto } from './dto/create-post.dto';
import { AddCommentDto, UpdatePostDto } from './dto/update-post.dto';
import { PrismaClient } from '@prisma/client';
import { DatabaseService } from 'src/database/database.service';
import { CustomResponse } from 'src/utils/CustomResponse';
import { HttpService } from '@nestjs/axios';
import { lastValueFrom, retry } from 'rxjs';
import { CompressorService } from '../compressor/compressor.service';
import { ExpressionAnalysisService } from '../expression-analysis/expression-analysis.service';
import { v4 as uuidv4 } from 'uuid';
import { SupabaseClient } from '@supabase/supabase-js';
import { HumeAnalysisResult } from '../services/hume-ai.service';
import { InsightResult } from '../services/azure-openai.service';

@Injectable()
export class PostsService {
  private prisma: PrismaClient;
  private supabase: SupabaseClient;
  constructor(
    private readonly dbService: DatabaseService,
    private readonly httpService: HttpService,
    private readonly compressionService: CompressorService,
    private readonly expressionAnalysisService: ExpressionAnalysisService,
  ) {
    this.prisma = dbService.getClient();
    this.supabase = dbService.getSupabaseClient();
  }
  async create(
    user_row_id: number,
    createPostDto: CreatePostDto,
    video: Express.Multer.File,
  ): Promise<CustomResponse> {
    try {
      // upload file to bucket
      const uniqueFileName = `${Date.now()}-${Math.random() * 100000}-${video.originalname}`;
      const { compressedBuffer } =
        await this.compressionService.compress(video);
      const upload = await this.dbService.uploadToStorage(
        'videos',
        {
          // 'recreating' original file but with compressed buffer
          buffer: compressedBuffer,
          filename: video.filename,
          fieldname: video.fieldname,
          destination: video.destination,
          originalname: video.originalname,
          mimetype: video.mimetype,
          path: video.path,
          size: compressedBuffer.length / (1024 * 1024),
          stream: video.stream,
          encoding: 'H.265',
        },
        {
          contentType: video.mimetype,
          upsert: false,
        },
        uniqueFileName,
      );
      // get uploaded files url
      const file_url = await this.dbService.getFileUrl(
        'videos',
        uniqueFileName,
        {},
      );
      const post = await this.supabase.from('posts').insert({
        content: createPostDto.content,
        is_private: createPostDto.is_private == 'true' ? true : false,
        parent_id: +createPostDto.parent_id,
        user_row_id,
        url: file_url.data.publicUrl,
      }).select('post_id');

      // Start expression analysis for the uploaded video (async, non-blocking)
      if (post.data && post.data[0] && file_url.data.publicUrl) {
        const postId = Number(post.data[0].post_id);
        const videoUrl = file_url.data.publicUrl;
        const postContent = createPostDto.content;

        // Trigger expression analysis in background (don't await)
        this.expressionAnalysisService
          .startAnalysisForPost(postId, videoUrl, postContent)
          .catch((error) => {
            console.error(`Expression analysis failed for post ${postId}:`, error);
          });
      }

      return {
        data: post.data,
        error: false,
        message: 'Post created successfully',
        status: HttpStatus.OK,
      };
    } catch (err) {
      console.log(err);
      return {
        data: null,
        error: true,
        message: 'Unable to create post',
        status: HttpStatus.INTERNAL_SERVER_ERROR,
      };
    }
  }

  async deletePost(
    post_id: number,
    user_row_id: number,
  ): Promise<CustomResponse> {
    try {
      const resp = await this.prisma.posts.deleteMany({
        where: { user_row_id, post_id: post_id },
      });
      return {
        data: resp,
        error: false,
        message: 'Post Deleted',
        status: HttpStatus.OK,
      };
    } catch (err) {
      console.log(err);
      return {
        data: null,
        error: true,
        message: 'Error deleting post',
        status: HttpStatus.INTERNAL_SERVER_ERROR,
      };
    }
  }

  async findAll(page_no: number, user_id: string): Promise<CustomResponse> {
    try {
      const data = await this.supabase
        .from('posts')
        .select(`*, users(metadata, user_id), post_upvotes(user_id) , comments(post_id)`)
        .eq('is_private', false)
        .is('parent_id', null)
        .range((page_no - 1) * 10, page_no * 10)
        .order('created_at', { ascending: false });
      for (let i = 0; i < data.data.length; i++) {
        // data.data[i]['upvoted'] = false;
        data.data[i]['upvoted'] = data.data[i]['post_upvotes'].some(
          (upvote) => upvote.user_id == user_id,
        );
        // data.data[i]['upvoted'] = data.data[i]['post_upvotes'].length ? true : false;
        // data.data[i]['owner_metadata'] = data.data[i].users.metadata;
        // data.data[i]['owner'] = data.data[i].users.user_id;
        // delete data.data[i]['users'];
        // data.data[i]['upvotes'] = data.data[i]['post_upvotes'].length;
        // delete data.data[i]['post_upvotes'];
        // delete data.data[i]['view_token'];
        // data.data[i]['comment_count'] = data.data[i]['comments'].length;
        // delete data.data[i]['comments'];
      }
      return {
        data: data.data,
        error: false,
        message: 'All posts fetched successfully',
        status: HttpStatus.OK,
      };
    } catch (err) {
      console.log(err);
      return {
        data: null,
        error: true,
        message: 'Error fetching posts',
        status: HttpStatus.INTERNAL_SERVER_ERROR,
      };
    }
  }

  async findPrivate(
    user_id: string,
    page_no: number,
    user_row_id: number,
  ): Promise<CustomResponse> {
    try {
      const data = await this.supabase
        .from('posts')
        .select(`*, users(*), post_upvotes(*), comments(*)`)
        .eq('is_private', true)
        .eq('user_row_id', user_row_id)
        .range((page_no - 1) * 10, page_no * 10)
        .order('created_at', { ascending: false });
      for (let i = 0; i < data.data.length; i++) {
        data.data[i]['upvoted'] = false;
        data.data[i]['upvoted'] = data.data[i]['post_upvotes'].some(
          (upvote) => upvote.user_id == user_id,
        );
        data.data[i]['owner_metadata'] = data.data[i].users.metadata;
        data.data[i]['owner'] = data.data[i].users.user_id;
        delete data.data[i]['users'];
        data.data[i]['upvotes'] = data.data[i]['post_upvotes'].length;
        delete data.data[i]['post_upvotes'];
        delete data.data[i]['view_token'];
        data.data[i]['comment_count'] = data.data[i]['comments'].length;
        delete data.data[i]['comments'];
      }
      return {
        data: data.data,
        error: false,
        message: 'All posts fetched successfully',
        status: HttpStatus.OK,
      };
    } catch (err) {
      console.log(err);
      return {
        data: null,
        error: true,
        message: 'Error fetching private posts',
        status: HttpStatus.INTERNAL_SERVER_ERROR,
      };
    }
  }

  async findOwnPublic(
    user_id: string,
    page_no: number,
    user_row_id: number,
  ): Promise<CustomResponse> {
    try {
      const data = await this.supabase
        .from('posts')
        .select(`*, users(*), post_upvotes(*), comments(*)`)
        .eq('is_private', false)
        .eq('user_row_id', user_row_id)
        .range((page_no - 1) * 10, page_no * 10)
        .order('created_at', { ascending: false });
      for (let i = 0; i < data.data.length; i++) {
        data.data[i]['upvoted'] = false;
        data.data[i]['upvoted'] = data.data[i]['post_upvotes'].some(
          (upvote) => upvote.user_id == user_id,
        );
        data.data[i]['owner_metadata'] = data.data[i].users.metadata;
        data.data[i]['owner'] = data.data[i].users.user_id;
        delete data.data[i]['users'];
        data.data[i]['upvotes'] = data.data[i]['post_upvotes'].length;
        delete data.data[i]['post_upvotes'];
        delete data.data[i]['view_token'];
        data.data[i]['comment_count'] = data.data[i]['comments'].length;
        delete data.data[i]['comments'];
      }
      return {
        data: data.data,
        error: false,
        message: 'All own public posts fetched successfully',
        status: HttpStatus.OK,
      };
    } catch (err) {
      console.log(err);
      return {
        data: null,
        error: true,
        message: 'Error fetching own public posts',
        status: HttpStatus.INTERNAL_SERVER_ERROR,
      };
    }
  }

  async upvotePost(user_id: string, post_id: number): Promise<CustomResponse> {
    try {
      const already_upvoted = await this.supabase
        .from('post_upvotes')
        .select('upvote_id')
        .eq('post_id', post_id)
        .eq('user_id', user_id);
      if (already_upvoted.data.length > 0) {
        return {
          data: null,
          error: true,
          message: 'Already upvoted.',
          status: HttpStatus.BAD_REQUEST,
        };
      }
      const upvote = await this.supabase.from('post_upvotes').insert({
        post_id,
        user_id,
      });
      return {
        data: upvote.data,
        error: false,
        message: 'Upvoted successfully',
        status: HttpStatus.OK,
      };
    } catch (err) {
      console.log(err);
      return {
        data: null,
        error: true,
        message: 'Error upvoting',
        status: HttpStatus.INTERNAL_SERVER_ERROR,
      };
    }
  }

  async removeUpvote(
    user_id: string,
    post_id: number,
  ): Promise<CustomResponse> {
    try {
      const upvote = await this.prisma.post_upvotes.deleteMany({
        // have to use deleteMany to use where clause on non unique keys
        where: {
          post_id,
          user_id,
        },
      });
      return {
        data: upvote,
        error: false,
        message: 'Upvoted removed successfully',
        status: HttpStatus.OK,
      };
    } catch (err) {
      console.log(err);
      return {
        data: null,
        error: true,
        message: 'Error removing upvoting',
        status: HttpStatus.INTERNAL_SERVER_ERROR,
      };
    }
  }

  async addComment(
    user_id: string,
    addCommentDto: AddCommentDto,
  ): Promise<CustomResponse> {
    try {
      const sentiment = await lastValueFrom(
        // httpService returns an Obervable, convert it to a Promise using lastValueFrom
        this.httpService
          .post(
            process.env.BERT_URL,
            { inputs: addCommentDto.comment },
            {
              headers: {
                Authorization: `Bearer ${process.env.HUGGINGFACE_TOKEN}`,
              },
            },
          )
          .pipe(retry(3)), // retry bert fetch once since it sometimes gives loading error
      );
      /* the response from huggingface looks like this: 
      [
          [
            { label: NEGATIVE/POSITIVE, score: x },
            { label: NEGATIVE/POSITIVE, score: y }
          ] 
      ]
      */
      let negative: number;
      let postive: number;
      if (sentiment.data[0][0]['label'] == 'POSITIVE') {
        postive = sentiment.data[0][0]['score'];
        negative = sentiment.data[0][1]['score'];
      } else {
        postive = sentiment.data[0][1]['score'];
        negative = sentiment.data[0][0]['score'];
      }
      if (negative >= 0.99) {
        const comment = await this.prisma.comments.create({
          data: {
            parent_id: addCommentDto.parent_id,
            post_id: addCommentDto.post_id,
            comment: addCommentDto.comment,
            user_id,
            is_bad: true,
          },
        });
        return {
          data: null,
          error: true,
          message: 'A bad comment was entered',
          status: HttpStatus.BAD_REQUEST,
        };
      }
      const comment = await this.prisma.comments.create({
        data: {
          parent_id: addCommentDto.parent_id,
          post_id: addCommentDto.post_id,
          comment: addCommentDto.comment,
          user_id,
          is_bad: false,
        },
      });
      return {
        data: comment,
        error: false,
        message: 'Commented successfully',
        status: HttpStatus.OK,
      };
    } catch (err) {
      console.log(err);
      return {
        data: null,
        error: true,
        message: 'Error commenting',
        status: HttpStatus.INTERNAL_SERVER_ERROR,
      };
    }
  }

  async getComments(post_id: number) {
    try {
      const comments = await this.prisma.comments.findMany({
        where: {
          post_id,
          is_bad: false,
        },
        include: {
          users: {
            select: {
              raw_user_meta_data: true,
              users: true,
            },
          },
          other_comments: true,
        },
      });
      for (let i = 0; i < comments.length; i++) {
        comments[i]['user_metadata'] = {
          email: comments[i].users.raw_user_meta_data['email'],
          gender: comments[i].users.raw_user_meta_data['gender'],
          full_name: comments[i].users.raw_user_meta_data['full_name'],
          profile_picture: comments[i].users.users[0].profile_picture_url,
        };
        delete comments[i].users; // have to remove some user info since auth.users is joined
      }
      return {
        data: comments,
        error: false,
        message: 'Commented fetched successfully',
        status: HttpStatus.OK,
      };
    } catch (err) {
      console.log(err);
      return {
        data: null,
        error: true,
        message: 'Error fetching comments',
        status: HttpStatus.INTERNAL_SERVER_ERROR,
      };
    }
  }

  async deleteComment(
    comment_id: number,
    user_id: string,
  ): Promise<CustomResponse> {
    try {
      const comment = await this.prisma.comments.delete({
        // can use delete instead of deleteMany since comment_id is unique
        where: {
          comment_id,
          user_id,
        },
      });
      return {
        data: comment,
        error: false,
        message: 'Comment deleted successfully',
        status: HttpStatus.OK,
      };
    } catch (err) {
      console.log(err);
      return {
        data: null,
        error: true,
        message: 'Error deleting comment',
        status: HttpStatus.INTERNAL_SERVER_ERROR,
      };
    }
  }

  async togglePostVisibility(
    post_id: number,
    user_row_id: number,
  ): Promise<CustomResponse> {
    try {
      const resp = await this.prisma.posts.updateMany({
        data: {
          is_private: {
            set: !(
              await this.prisma.posts.findFirst({
                where: { post_id, user_row_id },
              })
            ).is_private,
          },
        },
        where: {
          post_id: post_id,
          user_row_id,
        },
      });
      return {
        data: null,
        error: false,
        message: 'Post visibility updated',
        status: HttpStatus.OK,
      };
    } catch (err) {
      console.log(err);
      return {
        data: null,
        error: true,
        message: 'Error making post public. The post may not belong to you.',
        status: HttpStatus.INTERNAL_SERVER_ERROR,
      };
    }
  }

  async getShareableLink(post_id: number): Promise<CustomResponse> {
    try {
      const data = await this.supabase
        .from('posts')
        .select('is_private, view_token')
        .eq('post_id', post_id);
      if (data.data.length == 0) {
        throw 'Post does not exist';
      }
      let link = `${process.env.FRONTEND_URL}/post/${post_id}`;
      if (data.data[0].is_private) {
        link += `?view_token=${data.data[0].view_token}`;
      }
      return {
        data: link,
        error: false,
        message: 'Shareable link fetched',
        status: HttpStatus.OK,
      };
    } catch (err) {
      console.log(err);
      return {
        data: null,
        error: true,
        message: 'Error fetching sharing link',
        status: HttpStatus.INTERNAL_SERVER_ERROR,
      };
    }
  }

  async getPost(
    user_id: string,
    user_row_id: number,
    post_id: number,
    view_token: string,
  ): Promise<CustomResponse> {
    try {
      const post = await this.supabase
        .from('posts')
        .select(`*, users(metadata, user_id), post_upvotes(user_id), comments(post_id)`)
        .eq('post_id', post_id);
      if (post.data[0].is_private) {
        if (
          post.data[0].view_token != view_token &&
          post.data[0].user_row_id != user_row_id
        ) {
          // if post is private you need to have a view token or be the owner
          return {
            data: null,
            error: true,
            message: 'Unauthorized link',
            status: HttpStatus.BAD_REQUEST,
          };
        }
      }
      // post.data[0]['upvoted'] = false;
      post.data[0]['upvoted'] = post.data[0]['post_upvotes'].some(
        (upvote) => upvote.user_id == user_id,
      );

      // post.data[0]['owner_metadata'] = post.data[0].users.metadata;
      // post.data[0]['owner'] = post.data[0].users.user_id;
      // delete post.data[0]['users'];
      // post.data[0]['upvotes'] = post.data[0]['post_upvotes'].length;
      // delete post.data[0]['post_upvotes'];
      // delete post.data[0]['view_token'];
      // post.data[0]['comment_count'] = post.data[0]['comments'].length;

      const children = await this.supabase
        .from('posts')
        .select('*')
        .eq('parent_id', post_id);
      post.data[0]['children_posts'] = children.data;
      return {
        data: post.data[0],
        error: false,
        message: 'Post fetched',
        status: HttpStatus.OK,
      };
    } catch (err) {
      console.log(err);
      return {
        data: null,
        error: true,
        message: 'Error fetching post',
        status: HttpStatus.BAD_REQUEST,
      };
    }
  }

  async invalidateShareableLinks(
    user_row_id: number,
    post_id: number,
  ): Promise<CustomResponse> {
    try {
      if (post_id != undefined || post_id != null) {
        await this.prisma.posts.updateMany({
          where: {
            post_id,
            user_row_id,
          },
          data: {
            view_token: uuidv4(),
          },
        });
      } else {
        const posts = await this.prisma.posts.findMany({
          where: {
            user_row_id,
          },
          select: {
            _count: true,
            post_id: true,
          },
        });
        for (let i = 0; i < posts.length; i++) {
          await this.prisma.posts.updateMany({
            where: {
              post_id: posts[i].post_id,
            },
            data: {
              view_token: uuidv4(),
            },
          });
        }
      }
      return {
        data: null,
        error: false,
        message: 'Links invalidated',
        status: HttpStatus.OK,
      };
    } catch (err) {
      console.log(err);
      return {
        data: null,
        error: true,
        message: 'Error invalidating links',
        status: HttpStatus.INTERNAL_SERVER_ERROR,
      };
    }
  }

  /**
   * Find post by HumeAI job ID
   */
  async findByHumeJobId(jobId: string): Promise<any> {
    try {
      const result = await this.supabase
        .from('posts')
        .select('*')
        .eq('hume_job_id', jobId)
        .single();

      return result.data;
    } catch (error) {
      console.error(`Error finding post by HumeAI job ID ${jobId}:`, error);
      return null;
    }
  }

  /**
   * Update post with HumeAI analysis results and AI insights
   */
  async updateHumeAnalysis(
    postId: number,
    analysisData: {
      hume_analysis?: HumeAnalysisResult;
      ai_insights?: InsightResult;
      analysis_status?: 'analyzing' | 'completed' | 'failed';
    }
  ): Promise<void> {
    try {
      const updateData: any = {};

      if (analysisData.hume_analysis) {
        updateData.hume_analysis = analysisData.hume_analysis;
      }

      if (analysisData.ai_insights) {
        updateData.ai_insights = analysisData.ai_insights;
      }

      if (analysisData.analysis_status) {
        updateData.analysis_status = analysisData.analysis_status;
      }

      await this.supabase
        .from('posts')
        .update(updateData)
        .eq('post_id', postId);

      console.log(`Updated post ${postId} with analysis results`);
    } catch (error) {
      console.error(`Error updating post ${postId} with analysis:`, error);
      throw error;
    }
  }
}
