import { HttpAdapterHost, NestFactory } from '@nestjs/core';
import { AppModule } from './app.module';
import * as Sentry from '@sentry/nestjs';
import { SentryFilter } from './sentry.filter';
import { DocumentBuilder, SwaggerModule } from '@nestjs/swagger';
import { ValidationPipe } from '@nestjs/common';
import * as compression from 'compression';
import * as bodyParser from 'body-parser';

async function bootstrap() {
  // prisma is unable to serialize int8 types so have to do such workarounds
  BigInt.prototype['toJSON'] = function () {
    return this.toString();
  };

  // sentry for error logging
  Sentry.init({
    dsn: process.env.SENTRY_DSN,
    tracesSampleRate: 1.0,
  });

  const app = await NestFactory.create(AppModule);
  const { httpAdapter } = app.get(HttpAdapterHost);
  app.useGlobalFilters(new SentryFilter(httpAdapter));

  // Configure body parser with increased limits for HumeAI webhook payloads
  app.use(bodyParser.json({ limit: '50mb' }));
  app.use(bodyParser.urlencoded({ limit: '50mb', extended: true }));

  app.use(compression());

  // swagger for docs at /api when not in production
  if (process.env.NODE_ENV != 'production') {
    const swagger_config = new DocumentBuilder()
      .setTitle('audition-room')
      .setVersion('0.1')
      .build();
    const swagger_doc = SwaggerModule.createDocument(app, swagger_config);
    SwaggerModule.setup('api', app, swagger_doc);
  }

  // validation pipe to make sure incoming data is matched with dto if present
  app.useGlobalPipes(new ValidationPipe());

  app.enableCors();

  app.enableShutdownHooks();

  await app.listen(process.env.PORT);
}
bootstrap();
