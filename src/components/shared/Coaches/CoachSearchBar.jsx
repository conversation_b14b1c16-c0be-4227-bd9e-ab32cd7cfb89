import React, { useState, useEffect } from "react";
import { debounce } from "lodash";
import { Button } from "@/components/ui/button";
import { Loader2, Search, X } from "lucide-react";
import placeholderImage from "@/assets/images/blank-profile-picture.png";

const searchInCoach = (coach, term) => {
  if (!coach || !term) return false;

  const searchTerm = term.toLowerCase();
  const fieldsToSearch = [
    coach.metadata?.full_name,
    coach.email,
    ...(coach.expertise || []),
    coach.profile_description,
    coach.interview_status,
    coach.application_status,
    coach.status,
  ];

  return fieldsToSearch.some((field) => {
    if (field) {
      const fieldStr = field.toString().toLowerCase();
      for (let i = 0; i < fieldStr.length - searchTerm.length + 1; i++) {
        if (fieldStr.substring(i, i + searchTerm.length) === searchTerm) {
          return true;
        }
      }
    }
    return false;
  });
};

const CoachSearchBar = ({ coaches, setShowSearch, onHire, currentUserId }) => {
  const [searchQuery, setSearchQuery] = useState("");
  const [filteredCoaches, setFilteredCoaches] = useState([]);
  const [isLoading, setIsLoading] = useState(false);

  const debouncedSearch = debounce((query) => {
    setIsLoading(true);
    const filtered = (coaches || []).filter((coach) =>
      searchInCoach(coach, query)
    );
    setFilteredCoaches(filtered);
    setIsLoading(false);
  }, 300);

  useEffect(() => {
    if (searchQuery) {
      debouncedSearch(searchQuery);
    } else {
      setFilteredCoaches([]);
    }
    return () => debouncedSearch.cancel();
  }, [searchQuery, coaches]);

  const closeSearch = () => {
    setSearchQuery("");
    setFilteredCoaches([]);
    setShowSearch(false);
  };

const handleHire = async (coach) => {
  if (coach.user_id === currentUserId) {
    console.error("Cannot start a chat with yourself");
    return;
  }
  setIsLoading(true);
  try {
    await onHire(coach);
  } catch (error) {
    console.error("Failed to hire coach:", error);
  } finally {
    setIsLoading(false);
  }
};


  

  return (
    <div className="w-full mt-2 p-4">
      <div className="flex gap-2 mb-4">
        <input
          type="text"
          placeholder="Search coaches..."
          value={searchQuery}
          onChange={(e) => setSearchQuery(e.target.value)}
          className="flex-grow p-2 bg-chatSec text-text1 rounded-md border border-gray-700 focus:outline-none focus:ring-2 focus:ring-navItemActive"
        />
        <Button className="bg-[#625BF6] hover:bg-[#625BF6]/80">
          <Search size={16} />
        </Button>
      </div>
      {searchQuery && (
        <div className="bg-chatSec rounded-lg p-4 w-full mt-4 relative">
          <h3 className="text-lg font-semibold mb-2 text-text1">
            Search Results
          </h3>
          <button
            onClick={closeSearch}
            className="absolute top-2 right-2 text-text1 hover:text-gray-300"
          >
            <X size={20} />
          </button>
          {isLoading ? (
            <div className="flex justify-center items-center w-full mt-4">
              <Loader2 className="animate-spin text-[#625BF6]" size={40} />
            </div>
          ) : filteredCoaches.length > 0 ? (
            filteredCoaches.map((coach) => (
              <div
                key={coach?.id || "unknown"}
                className="flex items-center justify-between py-2 border-b border-gray-700"
              >
                <div className="flex items-center">
                  <img
                    src={
                      (coach?.profile_picture_url &&
                        coach.profile_picture_url) ||
                        placeholderImage
                    }
                    alt={coach?.metadata?.full_name || "Coach"}
                    className="w-12 h-12 rounded-full mr-3"
                  />
                  <div>
                    <h4 className="text-white font-semibold">
                      {coach?.metadata?.full_name || "Unknown Coach"}
                    </h4>
                    <span className="text-[#5CFFB1] text-xs">
                      {coach?.status || "Unknown Status"}
                    </span>
                    <p className="text-[#625BF6] text-sm">
                      {(coach?.expertise || []).join(", ")}
                    </p>
                  </div>
                </div>
                <Button
                  onClick={() => handleHire(coach)}
                  className="bg-[#625BF6] hover:bg-[#625BF6]/80"
                  disabled={
                    !coach?.user_id ||
                    coach.user_id === currentUserId ||
                    isLoading
                  }
                >
                  Hire
                </Button>
              </div>
            ))
          ) : (
            <p className="text-gray-400">No coaches found</p>
          )}
        </div>
      )}
    </div>
  );
};

export default CoachSearchBar;
