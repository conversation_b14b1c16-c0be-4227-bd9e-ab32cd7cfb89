import { Module } from '@nestjs/common';
import { ExpressionAnalysisController } from './expression-analysis.controller';
import { ExpressionAnalysisService } from './expression-analysis.service';
import { HumeAiService } from '../services/hume-ai.service';
import { AzureOpenAiService } from '../services/azure-openai.service';
import { DatabaseModule } from '../database/database.module';

@Module({
  imports: [DatabaseModule],
  controllers: [ExpressionAnalysisController],
  providers: [
    ExpressionAnalysisService,
    HumeAiService,
    AzureOpenAiService,
  ],
  exports: [
    ExpressionAnalysisService,
    HumeAiService,
    AzureOpenAiService,
  ], // Export so other modules can use them
})
export class ExpressionAnalysisModule {}
