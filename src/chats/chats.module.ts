import { Modu<PERSON> } from '@nestjs/common';
import { ChatsService } from './chats.service';
import { ChatsGateway } from './chats.gateway';
import { DatabaseModule } from '../database/database.module';
import { ChatsController } from './chats.controller';
import { ThrottlerModule } from '@nestjs/throttler';

@Module({
  controllers: [ChatsController],
  imports: [
    DatabaseModule,
    ThrottlerModule.forRoot({
      throttlers: [{ ttl: 60000, limit: 60, name: 'default' }], // 60 messages every minute
    }),
  ],
  providers: [ChatsGateway, ChatsService],
})
export class ChatsModule {}
