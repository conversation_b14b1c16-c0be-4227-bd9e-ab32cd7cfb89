import React, { useState, useEffect } from "react";
import Navbar from "@/components/shared/Navbar";
import HeroSection from "@/components/shared/LandingPageComponents/HeroSection";
import AboutUsSection from "@/components/shared/AboutUsSection";
import HowItWorks from "@/components/shared/LandingPageComponents/HowItWorks";
import MonologueSection from "@/components/shared/LandingPageComponents/MonologueSection";
import CoacheSection from "@/components/shared/LandingPageComponents/CoacheSection";
import FooterSection from "@/components/shared/FooterSection";
import TalentSection from "@/components/shared/TalentSection";
import Banner from "@/components/shared/Banner";
import InteractiveDemo from "@/components/shared/LandingPageComponents/InteractiveDemo";
import { Helmet } from "react-helmet-async";
import { X, Play } from "lucide-react";

// images import
import PlayImg from '../assets/images/next-monologue.png';
import DancersTwo from '../assets/images/level-up.png';
import launchingSoon from '../assets/images/app-launching-soon.png';

const LandingPage = () => {
  const descriptionText = [
    `Audition Rooms is more than just a Virtual Audition Platform—it's a creative hub where the community of all actors of all experience levels come together to shape the future of storytelling. Designed for both budding and professional actors, our platform offers a seamless experience with tools like Digital Acting Training Programs, monologue customization, and Self-Taping Audition Guides.`,
    `Whether you're preparing for a major audition or looking to refine your craft, we provide a safe, inclusive, and innovative space to help you shine. With free monologue auditions for men & women, and a Virtual Audition Platform, Audition Rooms is here to connect talent with opportunity, anytime, anywhere.`
  ]
  const [isVisible, setIsVisible] = useState(true);
  const [isDemoOpen, setIsDemoOpen] = useState(false);

  useEffect(() => {
    const handleOpenDemo = () => setIsDemoOpen(true);
    window.addEventListener('openInteractiveDemo', handleOpenDemo);
    return () => window.removeEventListener('openInteractiveDemo', handleOpenDemo);
  }, []);

  return (
    <>
      <Helmet>
        <title>Audition Rooms | Virtual Audition Platform for Actors, Self-Tapes & Casting Solutions</title>
        <meta name="title" content="Audition Rooms | Virtual Audition Platform for Actors, Self-Tapes &
            Casting Solutions" />
        <meta name="Description" content="Audition Rooms is the premier virtual audition platform
            offering remote casting, self-tape submissions, and online acting training. Explore free
            monologues for men, women, and kids, and access personalized coaching to elevate your
            acting career." />
        <meta name="Keywords" content="Virtual Audition Platform, Digital Acting Platform, Online
            Audition Platform, Remote Casting Platform, Free Monologues Auditions for Men, Women, and
            Kids, Customize Your Monologues Online, Self-Tape Audition Platform, Online Coaching
            Platform for Actors, Online Acting Auditions, Kids Casting and Auditions, Submit Self-Tapes
            Online, Digital Casting Platform for Artists, Actor Audition Portal, Video Audition Platform,
            Streamlined Casting Process, Online Talent Casting Network, Digital Talent Marketplace,
            Feedback Tools for Casting, Secure Audition Submissions"
        />
      </Helmet>
      <div className="bg-[#0D0D0F] animate-smooth-appear">
        <Navbar><Banner /></Navbar>
        <main>
          <HeroSection />
          <AboutUsSection showKnowMoreBtn={true} heading='About Us' description={descriptionText} />
          <div className="relative">
            <HowItWorks />
          </div>
          <MonologueSection />
          <TalentSection image={PlayImg} title={`Your Next <span class="text-purple">Monologue</span> <br /> Is Waiting!`} desc="Discover, customize, and perfect your monologue with ease." btnText="Explore Now" />
          {/* remove monologue section from below and uncomment above monologue section after carousel is stable */}
          {/* <MonologueSection /> */}
          {/* remove false when coaches carousel functionality is stable */}
          {false && <CoacheSection />}
          <TalentSection image={DancersTwo} title={`Level Up Your <br /> <span class="text-purple">Craft!</span>`} desc="Find your perfect acting coach and take your skills to the next level." btnText="Meet Our Coaches" />
          <FooterSection />
          {isVisible && (
            <div className="fixed z-40 bottom-10 left-0 w-64 sm:w-72 bg-transparent">
              <button
                onClick={() => setIsVisible(false)}
                className="absolute -top-4 -right-4 sm:-top-6 sm:-right-6 bg-black text-white rounded-full p-1 hover:text-gray-300"
              >
                <X size={20} />
              </button>
              <img src={launchingSoon} alt="Launching Soon" className="w-full" />
            </div>
          )}
          <InteractiveDemo isOpen={isDemoOpen} onClose={() => setIsDemoOpen(false)} />
        </main>
      </div>
    </>
  );
};

export default LandingPage;
