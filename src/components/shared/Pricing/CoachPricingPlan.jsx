import { Button } from "@/components/ui/button";
import {
  useBuyPlanMutation,
  useCancelPlanMutation,
} from "@/redux/features/planApi";

import { CircleCheckBig, Loader2 } from "lucide-react";

export const CoachPricingPlanSkeleton = () => (
  <div className="animate-pulse bg-primary1 rounded-lg p-8 flex flex-col h-full">
    <div className="h-8 bg-secondary2 rounded mb-4"></div>
    <div className="h-10 bg-secondary2 rounded mb-6"></div>
    <div className="space-y-3 mb-6">
      {[1, 2, 3].map((i) => (
        <div key={i} className="flex items-center">
          <div className="w-5 h-5 bg-secondary2 rounded-full mr-3"></div>
          <div className="h-4 bg-secondary2 rounded w-3/4"></div>
        </div>
      ))}
    </div>
    <div className="h-10 bg-secondary2 rounded"></div>
  </div>
);

const CoachPricingPlan = ({
  isActive,
  title,
  price,
  features,
  onCancelSuccess,
  plan_id,
  onError,
}) => {
  const [cancelPlan, { isLoading: isCancelling }] = useCancelPlanMutation();
  const [buyPlan, { isLoading: isBuying }] = useBuyPlanMutation();

  const handleBuyPlan = async () => {
    if (!plan_id) {
      onError("Cannot upgrade to this plan. Please try another.");
      return;
    }
    try {
      const response = await buyPlan(plan_id).unwrap();
      window.location.href = response.data;
    } catch (error) {
      onError("Failed to initiate plan purchase. Please try again.");
    }
  };

  const handleCancelPlan = async () => {
    try {
      await cancelPlan().unwrap();
      if (onCancelSuccess) {
        onCancelSuccess();
      }
    } catch (error) {
      onError("Failed to cancel plan. Please try again.");
    }
  };

  return (
    <div
      className={`rounded-lg lg:p-40 p-8  flex flex-col items-start justify-start h-full ${
        isActive ? "bg-navItemActive" : "bg-[#343434]"
      } transition-all duration-300 hover:shadow-xl`}
    >
      <div className="flex justify-between items-center mb-4">
        <h3
          className={`text-xl font-bold ${
            isActive ? "text-white" : "text-[#FAFAFA]"
          }`}
        >
          {title}
        </h3>
        {isActive && (
          <span className="text-xs bg-white text-navItemActive px-2 py-1 rounded">
            active
          </span>
        )}
      </div>
      <p
        className={`text-3xl font-bold mb-6 ${
          isActive ? "text-white" : "text-[#FAFAFA]"
        }`}
      >
        ${price}/mo
      </p>
      <ul className="flex-grow mb-6">
        {features.map((feature, index) => (
          <li
            key={index}
            className={`mb-3 flex items-center ${
              isActive ? "text-white" : "text-white"
            }`}
          >
            <CircleCheckBig className="w-5 h-5 mr-3 flex-shrink-0 mt-0.5 text-green-400" />
            {feature}
          </li>
        ))}
      </ul>
      <Button
        onClick={isActive ? handleCancelPlan : handleBuyPlan}
        disabled={(isActive && isCancelling) || (!isActive && isBuying)}
        className={`w-full py-3 rounded-lg font-semibold transition-colors duration-300 ${
          isActive
            ? "bg-white text-primary1 hover:bg-[#383481] hover:text-white"
            : "bg-navItemActive text-white hover:bg-opacity-90"
        }`}
      >
        {isActive ? (
          isCancelling ? (
            <Loader2 className="w-5 h-5 animate-spin" />
          ) : (
            "Cancel"
          )
        ) : isBuying ? (
          <Loader2 className="w-5 h-5 animate-spin" />
        ) : (
          "Subscribe"
        )}
      </Button>
    </div>
  );
};

export default CoachPricingPlan;
