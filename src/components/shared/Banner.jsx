import React, {useState} from 'react';
const Banner = () => {

  const [isVisible, setIsVisible] = useState(true);

  const handleClose = () => {
    setIsVisible(false);
  }

  if (!isVisible) return null;

  return (
    <div className="text-text1 text-center p-4 bg-navItemActive">
      Welcome to the Beta Launch of Audition Rooms! <b className="px-1">Join now for just $3.99/month</b> to shape the future of this platform. Your feedback will help us improve and expand.

      <button
        onClick={handleClose}
        className="absolute top-1 right-2 text-text1 text-2xl"
         aria-label="Close banner"
        >
        &times;
      </button>

    </div>
  );
}

export default Banner;