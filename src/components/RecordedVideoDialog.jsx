import {
  Dialog,
  DialogContent,
  DialogFooter,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import RecordedVideoPreview from "@/components/RecordedVideoPreview";

const RecordedVideoDialog = ({
  isOpen,
  onClose,
  videoSrc,
  parentId,
  onUploadComplete,
}) => {
  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="bg-primary1 border border-text2 py-4 w-[90vw]">
        <DialogTitle>
          <RecordedVideoPreview
            videoSrc={videoSrc}
            parentId={parentId}
            onClose={onClose}
            onUploadComplete={onUploadComplete}
          />
        </DialogTitle>
        <DialogFooter className="flex items-end">
          <Button onClick={onClose} className="bg-close text-text1 h-10 w-20 ">
            Close
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

export default RecordedVideoDialog;
