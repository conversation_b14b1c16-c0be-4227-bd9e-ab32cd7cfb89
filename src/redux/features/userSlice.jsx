import { createSlice } from "@reduxjs/toolkit";



const userSlice = createSlice({
  name: "user",
  initialState: {
    session: null,
    isLoading: true,
    user: null,
    error: null,
    role: null,
  },
  reducers: {
    setSession(state, action) {
      state.session = action.payload;
    },
    setIsLoading(state, action) {
      state.isLoading = action.payload;
    },
    clearSession: (state) => {
      state.session = null;
    },
    setRole(state, action) {
      state.role = action.payload;
    },
  },
});

export const { setSession, setIsLoading, clearSession, setRole } = userSlice.actions;
export default userSlice.reducer;
