import {
  Mic,
  Edit,
  Share2,
  DollarSign,
  Play,
} from "lucide-react";
import manImg from '../../../assets/images/public-domain.png';
import ladyDancerImg from '../../../assets/images/edit-record.png';
import laptopWorkImg from '../../../assets/images/upload-share.png';
//import blackWhiteImg from '../../../assets/images/black_white.jpg';
import arrowRightImg from '../../../assets/images/arrow-right.png';
import oneImg from '../../../assets/images/1.png';
import twoImg from '../../../assets/images/2.png';
import threeImg from '../../../assets/images/3.png';
import fourImg from '../../../assets/images/4.png';

import { useNavigate } from "react-router-dom";

const HowItWorksStep = ({ icon, title, description, index, images }) => (
  <div className={"relative lg:h-[400px]  md:h-[400px] sm:h-[400px] h-auto  max-sm:mx-0 md:mx-6  lg:mx-6 lg:mb-10 mb-5"}>
    {/* <div className={`h-full w-[150px] hidden ${index != 0 ? 'hidden' : 'xl:block'}`}></div> */}
    <div className="relative bg-black h-full rounded-[35px] w-full ">
      <div className="h-1/2">
        <img src={images[0]} alt="title" className="w-full h-full object-cover object-top bg-bottom rounded-t-[35px]" />
      </div>
      <div className="p-4">
        <img src={images[1]} alt="title" className="absolute h-20 right-5 bottom-18 object-cover opacity-60" />
        <h3 className="relative  pr-10 text-lg font-bold text-text1 group-hover:text-navItemActive transition-colors duration-300">
          {title}
        </h3>
        <p className="text-text2 text-sm relative pr-10">{description}</p>
      </div>
    </div>
    {/* Only render this div if it's not the last child */}
  {index !== 2 && (
    <div className={`h-full absolute right-[-45px] hidden top-0 align-middle xl:flex ${index === 2 ? "w-[40px]" : "w-[40px]"}`}>
      <img
        src={arrowRightImg}
        alt="arrow"
        className="object-contain"
      />
    </div>
  )}
  </div >
);

const HowItWorks = () => {

  const navigate = useNavigate();

  const handleNavigate = () => {
    navigate("/signup");
  };
  const steps = [
    {
      icon: <Mic size={24} />,
      title: "Public Domain",
      description:
        "Browse a handpicked collection of copyright free monologues for men and women or curate one for yourself ensuring it is copyright free or use our AI-powered tools to generate a unique piece that reflects your talent and style.",
      images: [manImg, oneImg],
    },
    {
      icon: <Edit size={24} />,
      title: "Edit Your Monologue and Record",
      description: "Turn any space into your personal stage with our easy-to-use tools for self-taping and editing. Add subtitles or touch up your video to perfection for your next audition.",
      images: [ladyDancerImg, twoImg],
    },
    {
      icon: <Share2 size={24} />,
      title: "Upload and Share",
      description:
        "Create and Upload your self tape audition directly to the community of actors or share them privately with peers, coaches, and industry insiders.",
      images: [laptopWorkImg, threeImg],
    },
    // {
    //   icon: <DollarSign size={24} />,
    //   title: "Advertise Your Coaching Services",
    //   description:
    //     "Looking to inspire others? Showcase your expertise as a coach and connect with budding actors through our actor-centric platform for just $5/month.",
    //   images: [blackWhiteImg, fourImg],
    // },
  ];

  return (
    <section id="how-it-works" className="bg-primary1 text-text1 lg:py-10  py-0">
      <div className="justify-content-center">
        <h2 className="text-3xl md:text-4xl font-bold text-center lg:mb-4 mb-2">
          How It Works
        </h2>
        <div className="flex flex-col items-center mb-12">
          <button
            onClick={() => window.dispatchEvent(new CustomEvent('openInteractiveDemo'))}
            className="group relative flex items-center gap-2 px-8 py-4 bg-purple-600 hover:bg-purple-700 text-white rounded-full transition-all duration-300 hover:scale-105 shadow-lg hover:shadow-purple-500/25"
          >
            <Play size={20} className="animate-pulse" />
            <span className="text-lg font-medium">View Interactive Demo</span>
            <div className="absolute -inset-1 bg-gradient-to-r from-purple-600 to-purple-400 rounded-full blur opacity-25 group-hover:opacity-50 transition duration-300" />
          </button>
          <div className="mt-3 text-gray-400 text-sm animate-bounce">Click to see how it works in action!</div>
        </div>
        <div className="relative grid grid-cols-1 sm:grid-cols-2 sm:gap-8 xl:gap-0 xl:grid-cols-3 lg:p-10 p-5">
          {steps.map((step, index) => (
            <HowItWorksStep key={index} {...step} index={index} />
          ))}
        </div>
      </div>
    </section>
  );
};

export default HowItWorks;