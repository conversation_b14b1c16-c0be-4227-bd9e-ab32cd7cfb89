import { HttpStatus, Injectable } from '@nestjs/common';
import { Prisma, PrismaClient } from '@prisma/client';
import { DatabaseService } from 'src/database/database.service';
import { CustomResponse } from 'src/utils/CustomResponse';

@Injectable()
export class ContactUsService {
  private prisma: PrismaClient;
  constructor(private readonly dbService: DatabaseService) {
    this.prisma = dbService.getClient();
  }

  async create(data: Prisma.contact_usCreateInput): Promise<CustomResponse> {
    const res = await this.prisma.contact_us.create({
      data,
    });
    return {
      data: res,
      error: null,
      status: HttpStatus.OK,
      message: 'Contact Us form submitted successfully',
    };
  }

  async getContacts(page, limit) {
    try {
      page = page || 1;
      limit = limit || 25;
      const offset = (Number(page) - 1) * Number(limit);

      const resp = await this.prisma.contact_us.findMany({
        skip: offset,
        take: Number(limit),
      });

      const totalContactUsData = await this.prisma.contact_us.count();

      return {
        data: resp,
        error: null,
        count: totalContactUsData,
        status: HttpStatus.OK,
        message: 'Surveys fetched successfully',
      };
    } catch (err) {
      console.log(err);
      return {
        data: null,
        error: true,
        status: HttpStatus.INTERNAL_SERVER_ERROR,
        message: 'Unable to fetch surveys',
      };
    }
  }
}
