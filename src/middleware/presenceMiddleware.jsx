import { setupPresence } from "@/lib/supabase";
import { updateOnlineStatus } from "@/redux/features/chatSlice";

let presenceChannel = null;

export const presenceMiddleware = (store) => (next) => (action) => {
  if (action.type === "user/setSession" || action.type === "SIGNED_IN") {
    const userId = action.payload?.user?.id;
    if (userId && !presenceChannel) {
      presenceChannel = setupPresence(userId, (newState) => {
        store.dispatch(updateOnlineStatus(newState));
      });
    }
  }

  if (
    (action.type === "user/clearSession" || action.type === "SIGNED_OUT") &&
    presenceChannel
  ) {
    presenceChannel.unsubscribe();
    presenceChannel = null;
  }

  return next(action);
};
