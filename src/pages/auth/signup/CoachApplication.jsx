import React, { useState, useCallback } from "react";
import { use<PERSON><PERSON>, Controller } from "react-hook-form";
import {
  X,
  Plus,
  Upload,
  CheckCircle,
  Video,
  Mic,
  Loader2,
} from "lucide-react";
import { useDropzone } from "react-dropzone";
import { zodResolver } from "@hookform/resolvers/zod";
import * as z from "zod";

import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Label } from "@/components/ui/label";
import { useToast } from "@/components/ui/use-toast";

import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import { useApplyCoachMutation } from "@/redux/features/coachApi";

const MAX_FILE_SIZE = 50 * 1024 * 1024; // 50MB
const ACCEPTED_RESUME_TYPES = [
  "application/pdf",
  "application/msword",
  "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
];
const ACCEPTED_VIDEO_TYPES = ["video/mp4", "video/quicktime"];
const ACCEPTED_AUDIO_TYPES = ["audio/mpeg", "audio/wav", "audio/ogg"];

const formSchema = z
  .object({
    email: z.string().email("Invalid email address"),
    full_name: z.string().min(2, "Name must be at least 2 characters"),
    gender: z.string().min(1, "Please select a gender"),
    expertise: z.array(z.string()),
    expertiseInput: z.string(),
    resume: z
      .any()
      .refine((file) => file, "Resume is required")
      .refine((file) => file?.size <= MAX_FILE_SIZE, `Max file size is 50MB.`)
      .refine(
        (file) => ACCEPTED_RESUME_TYPES.includes(file?.type),
        "Only .pdf, .doc, and .docx files are accepted for resume."
      ),
    video_reels: z
      .array(
        z
          .any()
          .refine(
            (file) => file?.size <= MAX_FILE_SIZE,
            `Max file size is 50MB.`
          )
          .refine(
            (file) => ACCEPTED_VIDEO_TYPES.includes(file?.type),
            "Only .mp4 and .mov files are accepted for video reels."
          )
      )
      .optional(),
    voice_reels: z
      .array(
        z
          .any()
          .refine((file) => file, "Resume is required")
          .refine(
            (file) => file?.size <= MAX_FILE_SIZE,
            `Max file size is 50MB.`
          )
          .refine(
            (file) => ACCEPTED_AUDIO_TYPES.includes(file?.type),
            "Only .mp3, .wav, and .ogg files are accepted for voice reels."
          )
      )
      .optional(),
  })
  .transform((data) => ({
    ...data,
    expertise: [
      ...data.expertise,
      ...(data.expertiseInput ? [data.expertiseInput] : []),
    ],
  }))
  .refine((data) => data.expertise.length > 0, {
    message: "At least one area of expertise is required",
    path: ["expertise"],
  });

export default function CoachApplicationForm() {
  const {
    register,
    handleSubmit,
    control,
    formState: { errors, isValid },
    setValue,
    watch,
    trigger,
  } = useForm({
    resolver: zodResolver(formSchema),
    mode: "onChange",
    defaultValues: {
      expertise: [],
      video_reels: [],
      voice_reels: [],
      resume: null,
    },
  });

  const [applyCoach, { isLoading }] = useApplyCoachMutation();
  const [resumeFile, setResumeFile] = useState(null);
  const [videoReels, setVideoReels] = useState([]);
  const [voiceReels, setVoiceReels] = useState([]);
  const { toast } = useToast();

  const expertiseTags = watch("expertise");
  const expertiseInput = watch("expertiseInput");
  const watchResume = watch("resume");

  const onDrop = useCallback(
    (acceptedFiles, fileType) => {
      if (fileType === "resume") {
        const file = acceptedFiles[0];
        setResumeFile(file);
        setValue("resume", file, { shouldValidate: true });
      } else if (fileType === "video") {
        setVideoReels((prev) => [...prev, ...acceptedFiles]);
        setValue("video_reels", [...videoReels, ...acceptedFiles], {
          shouldValidate: true,
        });
      } else if (fileType === "voice") {
        setVoiceReels((prev) => [...prev, ...acceptedFiles]);
        setValue("voice_reels", [...voiceReels, ...acceptedFiles], {
          shouldValidate: true,
        });
      }
    },
    [setValue, videoReels, voiceReels]
  );

  const {
    getRootProps: getResumeRootProps,
    getInputProps: getResumeInputProps,
  } = useDropzone({
    onDrop: (files) => onDrop(files, "resume"),
    accept: {
      "application/pdf": [".pdf"],
      "application/msword": [".doc"],
      "application/vnd.openxmlformats-officedocument.wordprocessingml.document":
        [".docx"],
    },
    multiple: false,
  });

  const { getRootProps: getVideoRootProps, getInputProps: getVideoInputProps } =
    useDropzone({
      onDrop: (files) => onDrop(files, "video"),
      accept: { "video/mp4": [".mp4"], "video/quicktime": [".mov"] },
      multiple: true,
    });

  const { getRootProps: getVoiceRootProps, getInputProps: getVoiceInputProps } =
    useDropzone({
      onDrop: (files) => onDrop(files, "voice"),
      accept: {
        "audio/mpeg": [".mp3"],
        "audio/wav": [".wav"],
        "audio/ogg": [".ogg"],
      },
      multiple: true,
    });

  const onSubmit = async (data) => {
    try {
      const formData = new FormData();

      Object.keys(data).forEach((key) => {
        if (
          key !== "expertise" &&
          key !== "resume" &&
          key !== "expertiseInput" &&
          key !== "video_reels" &&
          key !== "voice_reels"
        ) {
          formData.append(key, data[key]);
        }
      });

      data.expertise.forEach((tag, index) => {
        formData.append(`expertise[${index}]`, tag);
      });

      if (resumeFile) {
        formData.append("resume", resumeFile);
      }

      videoReels.forEach((file) => {
        formData.append(`video_reels`, file);
      });

      voiceReels.forEach((file) => {
        formData.append(`voice_reels`, file);
      });

      await applyCoach(formData).unwrap();
      toast({
        title: "Application Submitted",
        description: "Your coach application has been successfully submitted!",
        variant: "success",
      });
    } catch (err) {
      console.error("Failed to apply:", err);
      toast({
        title: "Submission Error",
        description:
          err.data?.message ||
          "An error occurred while submitting your application.",
        variant: "destructive",
      });
    }
  };

  const addExpertise = () => {
    if (expertiseInput && !expertiseTags.includes(expertiseInput)) {
      setValue("expertise", [...expertiseTags, expertiseInput]);
      setValue("expertiseInput", "");
      trigger("expertise");
    }
  };

  const removeExpertise = (tag) => {
    setValue(
      "expertise",
      expertiseTags.filter((t) => t !== tag)
    );
    trigger("expertise");
  };

  const removeFile = (fileType, index) => {
    if (fileType === "video") {
      const newVideoReels = videoReels.filter((_, i) => i !== index);
      setVideoReels(newVideoReels);
      setValue("video_reels", newVideoReels, { shouldValidate: true });
    } else if (fileType === "voice") {
      const newVoiceReels = voiceReels.filter((_, i) => i !== index);
      setVoiceReels(newVoiceReels);
      setValue("voice_reels", newVoiceReels, { shouldValidate: true });
    }
  };

  return (
    <div className="min-h-screen bg-primary1 text-text1 flex items-center justify-center p-4">
      <Card className="w-full bg-secondary2 backdrop-blur-md max-w-6xl border-border1 shadow-lg rounded-xl">
        <CardHeader>
          <CardTitle className="text-4xl font-bold text-center text-text1">
            Coach Application
          </CardTitle>
        </CardHeader>
        <CardContent>
          <form
            onSubmit={handleSubmit(onSubmit)}
            className="flex flex-wrap gap-8"
          >
            <div className="w-full lg:w-[calc(50%-1rem)] space-y-8">
              <div className="space-y-2">
                <Label
                  htmlFor="email"
                  className="text-lg font-medium block text-text1"
                >
                  Email
                </Label>
                <Input
                  id="email"
                  type="email"
                  placeholder="<EMAIL>"
                  {...register("email")}
                  className="bg-white/5 border-border1 text-text1 rounded-lg focus:ring-0 focus:ring-hover1 transition-all placeholder:text-text2"
                />
                {errors.email && (
                  <p className="text-close text-sm mt-1">
                    {errors.email.message}
                  </p>
                )}
              </div>

              <div className="space-y-2">
                <Label
                  htmlFor="fullName"
                  className="text-lg font-medium block text-text1"
                >
                  Full Name
                </Label>
                <Input
                  id="fullName"
                  placeholder="John Doe"
                  {...register("full_name")}
                  className="bg-white/5 border-border1 text-text1 rounded-lg focus:ring-0 focus:ring-hover1 transition-all placeholder:text-text2"
                />
                {errors.full_name && (
                  <p className="text-close text-sm mt-1">
                    {errors.full_name.message}
                  </p>
                )}
              </div>

              <div className="space-y-2">
                <Label
                  htmlFor="gender"
                  className="text-lg font-medium block text-text1"
                >
                  Gender
                </Label>
                <Controller
                  name="gender"
                  control={control}
                  render={({ field }) => (
                    <Select onValueChange={field.onChange} value={field.value}>
                      <SelectTrigger className="bg-white/5 border-border1 text-text1 rounded-lg focus:ring-0  focus:ring-hover1 transition-all">
                        <SelectValue placeholder="Select gender" />
                      </SelectTrigger>
                      <SelectContent className="bg-otherBtn border-border1 text-text1">
                        <SelectItem value="Male">Male</SelectItem>
                        <SelectItem value="Female">Female</SelectItem>
                        <SelectItem value="Other">Other</SelectItem>
                      </SelectContent>
                    </Select>
                  )}
                />
                {errors.gender && (
                  <p className="text-close text-sm mt-1">
                    {errors.gender.message}
                  </p>
                )}
              </div>

              <div className="space-y-2">
                <Label
                  htmlFor="expertise"
                  className="text-lg font-medium block text-text1"
                >
                  Expertise
                </Label>
                <div className="flex flex-wrap gap-2 mb-2">
                  {expertiseTags.map((tag, index) => (
                    <span
                      key={index}
                      className="bg-white/10 text-text1 px-3 py-1 rounded-full text-sm flex items-center transition-all"
                    >
                      {tag}
                      <button
                        type="button"
                        onClick={() => removeExpertise(tag)}
                        className="ml-2 focus:outline-none hover:text-text2 transition-colors"
                      >
                        <X size={14} />
                      </button>
                    </span>
                  ))}
                </div>
                <div className="flex gap-2">
                  <Input
                    id="expertiseInput"
                    placeholder="Add your expertise"
                    {...register("expertiseInput")}
                    className="bg-white/5 border-border1 text-text1 rounded-lg focus:ring-2 focus:ring-hover1 flex-grow transition-all placeholder:text-text2"
                    onKeyPress={(e) => {
                      if (e.key === "Enter") {
                        e.preventDefault();
                        addExpertise();
                      }
                    }}
                  />
                  <TooltipProvider>
                    <Tooltip>
                      <TooltipTrigger asChild>
                        <Button
                          type="button"
                          onClick={addExpertise}
                          className="bg-hover1 hover:bg-navItemActive text-text1 rounded-lg transition-colors"
                        >
                          <Plus size={20} />
                        </Button>
                      </TooltipTrigger>
                      <TooltipContent>
                        <p>Add expertise</p>
                      </TooltipContent>
                    </Tooltip>
                  </TooltipProvider>
                </div>
                {errors.expertise && (
                  <p className="text-close text-sm mt-1">
                    {errors.expertise.message}
                  </p>
                )}
              </div>
              <div className="space-y-2">
                <Label
                  htmlFor="resume"
                  className="text-lg font-medium block text-text1"
                >
                  Resume
                </Label>
                <div
                  {...getResumeRootProps()}
                  className="border-2 border-dashed rounded-lg p-6 text-center cursor-pointer transition-all hover:border-hover1 bg-white/5"
                >
                  <input {...getResumeInputProps()} />
                  {resumeFile ? (
                    <div className="flex items-center justify-center">
                      <CheckCircle className="text-hover1 mr-2" size={24} />
                      <p className="text-text1 text-lg">{resumeFile.name}</p>
                    </div>
                  ) : (
                    <div className="text-text2">
                      <Upload className="mx-auto mb-3" size={32} />
                      <p className="text-lg">
                        Drag & drop your resume here, or click to select
                      </p>
                      <p className="text-sm mt-2">
                        Supported formats: PDF, DOC, DOCX (Max 50MB)
                      </p>
                    </div>
                  )}
                </div>
                {errors.resume && (
                  <p className="text-close text-sm mt-1">
                    {errors.resume.message}
                  </p>
                )}
              </div>
            </div>

            <div className="w-full lg:w-[calc(50%-1rem)] space-y-8">
              <div className="space-y-2">
                <Label
                  htmlFor="video_reels"
                  className="text-lg font-medium block text-text1"
                >
                  Video Reels
                </Label>
                <div
                  {...getVideoRootProps()}
                  className="border-2 border-dashed rounded-lg p-6 text-center cursor-pointer transition-all hover:border-hover1 bg-white/5"
                >
                  <input {...getVideoInputProps()} />
                  <div className="text-text2">
                    <Video className="mx-auto mb-3" size={32} />
                    <p className="text-lg">
                      Drag & drop your video reels here, or click to select
                    </p>
                    <p className="text-sm mt-2">
                      Supported formats: MP4, MOV (Max 50MB each)
                    </p>
                  </div>
                </div>
                <div className="mt-4 space-y-2">
                  {videoReels.map((file, index) => (
                    <div
                      key={index}
                      className="flex items-center justify-between bg-white/10 p-3 rounded-lg"
                    >
                      <div className="flex items-center">
                        <Video className="text-hover1 mr-3" size={24} />
                        <span className="text-text1">{file.name}</span>
                      </div>
                      <Button
                        type="button"
                        variant="ghost"
                        size="sm"
                        onClick={() => removeFile("video", index)}
                        className="text-close hover:text-closeHover"
                      >
                        <X size={20} />
                      </Button>
                    </div>
                  ))}
                </div>
                {errors.video_reels && (
                  <p className="text-close text-sm mt-1">
                    {errors.video_reels.message}
                  </p>
                )}
              </div>

              <div className="space-y-2">
                <Label
                  htmlFor="voice_reels"
                  className="text-lg font-medium block text-text1"
                >
                  Voice Reels
                </Label>
                <div
                  {...getVoiceRootProps()}
                  className="border-2 border-dashed rounded-lg p-6 text-center cursor-pointer transition-all hover:border-hover1 bg-white/5"
                >
                  <input {...getVoiceInputProps()} />
                  <div className="text-text2">
                    <Mic className="mx-auto mb-3" size={32} />
                    <p className="text-lg">
                      Drag & drop your voice reels here, or click to select
                    </p>
                    <p className="text-sm mt-2">
                      Supported formats: MP3, WAV, OGG (Max 50MB each)
                    </p>
                  </div>
                </div>
                <div className="mt-4 space-y-2">
                  {voiceReels.map((file, index) => (
                    <div
                      key={index}
                      className="flex items-center justify-between bg-white/10 p-3 rounded-lg"
                    >
                      <div className="flex items-center">
                        <Mic className="text-hover1 mr-3" size={24} />
                        <span className="text-text1">{file.name}</span>
                      </div>
                      <Button
                        type="button"
                        variant="ghost"
                        size="sm"
                        onClick={() => removeFile("voice", index)}
                        className="text-close hover:text-closeHover"
                      >
                        <X size={20} />
                      </Button>
                    </div>
                  ))}
                </div>
                {errors.voice_reels && (
                  <p className="text-close text-sm mt-1">
                    {errors.voice_reels.message}
                  </p>
                )}
              </div>
            </div>

            <Button
              type="submit"
              disabled={isLoading || !isValid || !watchResume}
              className={`w-full py-4 rounded-lg transition-all text-xl font-semibold mt-8 ${
                isValid && watchResume
                  ? "bg-gradient-to-r from-hover1 to-navItemActive text-text1 hover:opacity-90"
                  : "bg-otherBtn text-text2 cursor-not-allowed"
              }`}
            >
              {isLoading ? (
                <Loader2 size={24} className="animate-spin" />
              ) : (
                "Submit Application"
              )}
            </Button>
          </form>
        </CardContent>
      </Card>
    </div>
  );
}
