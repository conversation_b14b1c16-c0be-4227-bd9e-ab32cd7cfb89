import React, { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { MessageCir<PERSON>, Loader2, BadgeCheck } from "lucide-react";
import placeholderImage from "@/assets/images/blank-profile-picture.png";

const CoachCard = ({ coach, onHire, currentUserId }) => {

  
  const [isLoading, setIsLoading] = useState(false);

  const handleHire = async () => {
    setIsLoading(true);
    try {
      await onHire(coach);
    } finally {
      setIsLoading(false);
    }
  };

  
  if (!coach || !coach.metadata || !coach.expertise) {
    return null; // Don't render the card if coach data is incomplete
  }

  return (
    <div className="list-box max-sm:border-[3px] max-sm:border-navItemActive  bg-[#202020] max-sm:ml-auto max-sm:mr-auto max-sm:w-[90%]  rounded-lg p-4 mb-4 max-sm:block md:flex lg:flex justify-between items-center">
      <div className=" profile-pic max-sm:block md:flex lg:flex items-center gap-4 ">
        <img 
          src={coach.profile_picture_url || placeholderImage}
          alt={coach.metadata.full_name}
          className="md:size-16 size-12 rounded-full object-cover max-sm:m-auto"
        />
         </div>
        <div className="profile-content max-sm:mx-auto max-sm:block max-sm:text-center">
          <div className="profile-inner   max-sm:m-auto max-sm:block max-sm:text-center md:flex lg:flex md:items-center md:flex-row flex-col gap-2">
          <h3 className="text-text1 text-xl font-semibold flex items-center justify-center sm:justify-start md:justify-start gap-1 w-auto sm:w-[250px]">
              {coach.metadata.full_name}
              <BadgeCheck size={20} className="text-green-500 " />
            </h3>
            <span className="  text-[#5CFFB1] px-2 rounded-full text-xs py-1 bg-[#00F07D21] w-16">
              {coach.status}
            </span>
          </div>
         
          <div className="user-designation max-sm:m-auto  max-sm:my-2.5 max-sm:block max-sm:text-center md:flex lg:flex flex-wrap gap-1 mt-2">
            {coach.expertise.map((skill, index) => (
              <span
                key={index}
                className="bg-[#625BF62B] text-navItemActive text-xs px-2 py-1 rounded-full"
              >
                {skill}
              </span>
            ))}
          </div>
          </div>
     
      {coach.user_id !== currentUserId ? (
        <Button
          onClick={handleHire}
          disabled={isLoading}
          className="max-sm:m-auto max-sm:block max-sm:text-center bg-navItemActive hover:bg-navItemActive/80 text-text1"
        >
          {isLoading ? (
            <Loader2 className="mr-2 h-4 w-4 animate-spin" />
          ) : (
            <span className="flex justify-center items-center gap-2">
              <MessageCircle size={14} />
              Hire
            </span>
          )}
        </Button>
      ) : (        
          <div className="w-[80px] h-[36px]"></div>       
      )}
    </div>
  );
};

export default CoachCard;
