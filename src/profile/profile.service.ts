import { HttpStatus, Injectable } from '@nestjs/common';
import { PrismaClient } from '@prisma/client';
import { DatabaseService } from '../database/database.service';
import { CustomResponse } from '../utils/CustomResponse';
import { UpdateProfileDto } from './dto/update-profile.dto';
import { extractFilename } from '../utils/functions';
import { SupabaseClient } from '@supabase/supabase-js';

@Injectable()
export class ProfileService {
  private prisma: PrismaClient;
  private supabase: SupabaseClient;
  constructor(private readonly dbService: DatabaseService) {
    this.prisma = dbService.getClient();
    this.supabase = dbService.getSupabaseClient();
  }
  async getUser(
    user_id: string,
    own_profile: boolean,
    coach: boolean,
  ): Promise<CustomResponse> {
    try {
      const user = await this.prisma.public_users.findFirst({
        where: {
          user_id,
        },
        include: {
          users: {
            include: {
              coaches: coach,
              _count: {
                select: {
                  comments: {
                    where: { is_bad: false },
                  },
                  post_upvotes: true,
                },
              },
            },
          },
          posts: true,
        },
      });
      if (!own_profile) {
        return {
          data: {
            full_name: user.metadata['full_name'],
            email: user.email,
            profile_picture_url: user.profile_picture_url,
            comments: user.users._count.comments,
            videos: user.posts.length,
            upvotes: user.users._count.post_upvotes,
            user_id: user.user_id,
            profile_description: user.profile_description,
          },
          error: false,
          message: 'User fetched successfully',
          status: HttpStatus.OK,
        };
      } else {
        if (coach) {
          return {
            data: {
              created_at: user.created_at,
              id: user.id,
              is_banned: user.is_banned,
              plan_in_use: user.plan_in_use,
              stripe_customer_id: user.stripe_customer_id,
              user_id: user.user_id,
              full_name: user.metadata['full_name'],
              email: user.email,
              profile_picture_url: user.profile_picture_url,
              comments: user.users._count.comments,
              videos: user.posts.length,
              upvotes: user.users._count.post_upvotes,
              profile_description: user.profile_description,
              headshots: user.users.coaches[0]?.headshots,
              voice_reels: user.users.coaches[0]?.voice_reels,
              video_reels: user.users.coaches[0]?.video_reels,
              status: user.users.coaches[0]?.status,
            },
            error: false,
            message: 'User fetched successfully',
            status: HttpStatus.OK,
          };
        }
        return {
          data: {
            created_at: user.created_at,
            id: user.id,
            is_banned: user.is_banned,
            plan_in_use: user.plan_in_use,
            stripe_customer_id: user.stripe_customer_id,
            user_id: user.user_id,
            full_name: user.metadata['full_name'],
            email: user.email,
            profile_picture_url: user.profile_picture_url,
            comments: user.users._count.comments,
            videos: user.posts.length,
            upvotes: user.users._count.post_upvotes,
            profile_description: user.profile_description,
          },
          error: false,
          message: 'User fetched successfully',
          status: HttpStatus.OK,
        };
      }
    } catch (err) {
      console.log(err);
      return {
        data: null,
        error: true,
        message: 'Unable to fetch user',
        status: HttpStatus.INTERNAL_SERVER_ERROR,
      };
    }
  }
  async getUsersbyQuery(search: string): Promise<CustomResponse> {
    try {
      const data = await this.supabase
        .from('users')
        .select(
          'email, created_at, metadata, is_banned, profile_description, user_id, profile_picture_url',
        )
        .or(`metadata->>full_name.ilike.%${search}%,email.ilike.%${search}%`)
        .or(`metadata->>role.is.null,metadata->>role.neq.coach`);

      return {
        data: data.data || [],
        error: false,
        message: 'User fetched successfully',
        status: HttpStatus.OK,
      };
    } catch (err) {
      console.log(err);
      return {
        data: null,
        error: true,
        message: 'Unable to fetch user',
        status: HttpStatus.INTERNAL_SERVER_ERROR,
      };
    }
  }

  async updateProfile(
    user_id: string,
    updateProfileDto: UpdateProfileDto,
    image: Express.Multer.File,
  ): Promise<CustomResponse> {
    try {
      // Fetch current data
      const [currentUserData, currentRawUserData] = await Promise.all([
        this.prisma.public_users.findFirst({
          where: { user_id },
          select: {
            metadata: true,
            profile_picture_url: true,
          },
        }),
        this.prisma.users.findFirst({
          where: { id: user_id },
          select: {
            raw_user_meta_data: true,
          },
        }),
      ]);

      const updateData: any = {};

      // Ensure current metadata is an object before spreading
      const currentMetadata =
        typeof currentUserData.metadata === 'object' &&
        currentUserData.metadata !== null
          ? currentUserData.metadata
          : {};

      // Update metadata if necessary
      if (updateProfileDto.full_name) {
        updateData.metadata = {
          ...currentMetadata,
          full_name: updateProfileDto.full_name,
        };
      }

      if (updateProfileDto.profile_description) {
        updateData.profile_description = updateProfileDto.profile_description;
      }

      if (updateProfileDto.email) {
        updateData.email = updateProfileDto.email;
      }

      // assigning a resolved promise to the variable ensures that even if no image exists imageUpdatePromise
      // is still a promise and Promise.all (comes later) does not give an error
      let imageUpdatePromise: Promise<any> = Promise.resolve();
      if (image) {
        // an immediately executing function that returns a promise, the operations start
        // happening immediately after function is called but we don't wait for their completion
        // and move on to raw user metadata update, this promise is then resolved later
        imageUpdatePromise = (async () => {
          const uniqueFileName = `${Date.now()}-${Math.random() * 100000}-${image.originalname}`;
          const upload = await this.dbService.uploadToStorage(
            'profile_pictures',
            image,
            {
              contentType: image.mimetype,
              upsert: false,
            },
            uniqueFileName,
          );
          const file_url = await this.dbService.getFileUrl(
            'profile_pictures',
            uniqueFileName,
            {},
          );
          if (currentUserData.profile_picture_url) {
            // Delete old picture
            await this.dbService.deleteFromStorage('profile_pictures', [
              extractFilename(currentUserData.profile_picture_url),
            ]);
          }
          await this.prisma.public_users.updateMany({
            where: {
              user_id,
            },
            data: {
              profile_picture_url: file_url.data.publicUrl,
            },
          });
        })();
      }

      let rawUserMetaDataUpdatePromise: Promise<any> = Promise.resolve();
      if (updateProfileDto.full_name) {
        const currentRawUserMetaData =
          typeof currentRawUserData.raw_user_meta_data === 'object' &&
          currentRawUserData.raw_user_meta_data !== null
            ? currentRawUserData.raw_user_meta_data
            : {};

        const newRawUserMetaData = {
          ...currentRawUserMetaData,
          full_name: updateProfileDto.full_name,
        };

        rawUserMetaDataUpdatePromise = this.prisma.users.update({
          where: { id: user_id },
          data: {
            raw_user_meta_data: newRawUserMetaData,
          },
        });
      }
      let emailUpdatePromise: Promise<any> = Promise.resolve();

      if (updateProfileDto.email) {
        emailUpdatePromise = this.prisma.users.update({
          where: { id: user_id },
          data: {
            email: updateProfileDto.email,
          },
        });
      }

      // Resolve all promises concurrently
      await Promise.all([
        Object.keys(updateData).length > 0
          ? this.prisma.public_users.updateMany({
              where: { user_id },
              data: updateData,
            })
          : Promise.resolve(),
        imageUpdatePromise,
        rawUserMetaDataUpdatePromise,
        emailUpdatePromise,
      ]);

      const new_data = await this.prisma.public_users.findFirst({
        where: { user_id },
      });

      return {
        data: new_data,
        error: false,
        message: 'User updated successfully',
        status: HttpStatus.OK,
      };
    } catch (err) {
      console.log(err);
      return {
        data: null,
        error: true,
        message: 'Unable to update metadata',
        status: HttpStatus.INTERNAL_SERVER_ERROR,
      };
    }
  }
}
