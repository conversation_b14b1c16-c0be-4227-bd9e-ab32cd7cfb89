import { ApiProperty } from '@nestjs/swagger';
import { IsOptional, IsNumber, IsString } from 'class-validator';

export class SubmitSurveyDto {
  @IsOptional()
  @IsNumber()
  @ApiProperty({ example: 4, description: 'Usability rating of the survey (1-5 scale)' })
  useabilityRating?: number | null;

  @IsOptional()
  @IsNumber()
  @ApiProperty({ example: 3, description: 'Overall experience rating (1-5 scale)' })
  overallExperienceRating?: number | null;

  @IsOptional()
  @IsString()
  @ApiProperty({ example: 'Great experience!', description: 'User feedback on features' })
  featuresDescription?: string | null;
}
