import PricingPlan from "@/components/shared/Pricing/PricingPlan";
import PricingPlanSkeleton from "@/components/shared/Pricing/PricingPlanSkeleton";
import { Button } from "@/components/ui/button";
import { Tabs, TabsContent, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { useToast } from "@/components/ui/use-toast";
import {
  useGetCurrentPlanQuery,
  useGetStripeDashboardQuery,
  useGetUserPlansQuery,
} from "@/redux/features/planApi";
import { useGetCoachPlansQuery } from "@/redux/features/coachApi";
import { ExternalLink, SplineIcon } from "lucide-react";
import { useSelector } from "react-redux";
import ScreenTopSection from "@/components/ScreenTopSection";
import { Sparkles } from "lucide-react";

const Settings = () => {
  const { toast } = useToast();
  const { session } = useSelector((state) => state.user);
  const isCoach = session?.user?.user_metadata?.role === "coach";

  const {
    data: userPlansData,
    error: userPlansError,
    isLoading: userPlansLoading,
  } = useGetUserPlansQuery(undefined, { skip: isCoach });

  const {
    data: coachPlansData,
    error: coachPlansError,
    isLoading: coachPlansLoading,
  } = useGetCoachPlansQuery(undefined, { skip: !isCoach });

  const {
    data: currentPlanData,
    error: currentPlanError,
    isLoading: currentPlanLoading,
    refetch: refetchCurrentPlan,
  } = useGetCurrentPlanQuery();

  const plansData = isCoach ? coachPlansData : userPlansData;
  const plansError = isCoach ? coachPlansError : userPlansError;
  const isLoading = isCoach ? coachPlansLoading : userPlansLoading;

  // const freePlan = {
  //   title: "Free Plan",
  //   price: "0",
  //   features: [
  //     "Sign up and watch others perform",
  //     "Upload your own audition videos",
  //   ],
  //   isActive: !currentPlanData?.data?.plan_id,
  //   plan_id: null,
  // };

  const paidPlans =
    plansData?.data?.map((plan) => ({
      title: plan.plan_name,
      price: plan.plan_price_monthly,
      features: plan.description ? plan.description.split(". ") : [],
      isActive: currentPlanData?.data?.plan_id === plan.plan_id,
      plan_id: plan.plan_id,
    })) || [];

  // const allPlans = isCoach ? paidPlans : [freePlan, ...paidPlans];
  const allPlans = isCoach ? paidPlans : [...paidPlans];

  const {
    data: stripeDashboardData,
    error: stripeDashboardError,
    isLoading: stripeDashboardLoading,
  } = useGetStripeDashboardQuery();

  const handlePlanCancelled = () => {
    refetchCurrentPlan();
    toast({
      title: "Plan Cancelled",
      description: "Your plan has been successfully cancelled.",
      variant: "success",
    });
  };

  const handlePlanError = (errorMessage) => {
    toast({
      title: "Error",
      description: errorMessage,
      variant: "destructive",
    });
  };

  return (
    <div className="flex justify-center flex-col items-center w-full bg-primary1 text-[#FAFAFA]">
      <div className="lg:w-[52%] w-full">
        <ScreenTopSection
          page="Settings"
          btnText={null}
          Icon={Sparkles}
        />

        <Tabs defaultValue="billing" className="w-full mt-4  max-sm:px-5">
          <TabsList className="bg-[#343434] rounded-md h-[48px] w-[120px] mb-6">
            <TabsTrigger
              value="billing"
              className="rounded-lg data-[state=active]:bg-primary1 data-[state=active]:text-text1 h-[38px] w-[110px]"
            >
              Billing
            </TabsTrigger>
          </TabsList>
          <TabsContent value="billing">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {isLoading || currentPlanLoading ? (
                <>
                  <PricingPlanSkeleton />
                  <PricingPlanSkeleton />
                </>
              ) : plansError || currentPlanError ? (
                <div className="col-span-2 text-center text-red-500">
                  Error loading plans. Please try again later.
                </div>
              ) : (
                allPlans.map((plan, index) => (
                  <PricingPlan
                    key={index}
                    {...plan}
                    onCancelSuccess={handlePlanCancelled}
                    onError={handlePlanError}
                  />
                ))
              )}
            </div>
            <div className="mt-12">
              <div className="bg-[#343434] rounded-lg p-4 flex justify-between items-center">
                <span className="text-white">
                  Manage your billing on Stripe dashboard
                </span>
                {stripeDashboardLoading ? (
                  <Button
                    disabled
                    className="bg-[#6D66FB] text-white px-4 py-2 rounded-lg flex items-center"
                  >
                    <SplineIcon className="animate" />
                  </Button>
                ) : stripeDashboardError ? (
                  <Button
                    disabled
                    className="bg-red-500 text-white px-4 py-2 rounded-lg flex items-center"
                  >
                    Error
                  </Button>
                ) : (
                  <Button
                    className="bg-[#6D66FB] text-white px-4 py-2 rounded-lg flex items-center"
                    onClick={() =>
                      window.open(stripeDashboardData?.data, "_blank")
                    }
                  >
                    Dashboard
                    <ExternalLink className="w-4 h-4 ml-2" />
                  </Button>
                )}
              </div>
            </div>
          </TabsContent>
          <TabsContent value="app">
            <div className="mt-6">
              <p>App settings </p>
            </div>
          </TabsContent>
        </Tabs>
      </div>
    </div>
  );
};

export default Settings;
