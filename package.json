{"name": "audition-room-backend", "version": "0.0.1", "description": "", "author": "", "private": true, "license": "UNLICENSED", "scripts": {"build": "nest build", "format": "prettier --write \"src/**/*.ts\" \"test/**/*.ts\"", "start": "nest start", "start:dev": "nest start --watch", "start:dev:log": "nest start --watch 2>&1 | tee logs/app.log", "start:debug": "nest start --debug --watch", "start:debug:log": "nest start --debug --watch 2>&1 | tee logs/app-debug.log", "start:staging": "cross-env NODE_ENV=staging node dist/src/main", "start:staging:log": "cross-env NODE_ENV=staging node dist/src/main 2>&1 | tee logs/app-staging.log", "start:prod": "cross-env NODE_ENV=production node dist/src/main", "start:prod:log": "cross-env NODE_ENV=production node dist/src/main 2>&1 | tee logs/app-prod.log", "logs:clean": "sed 's/\\x1b\\[[0-9;]*m//g' logs/app.log > logs/app-clean.log", "logs:tail": "tail -f logs/app.log", "logs:search": "grep -i", "lint": "eslint \"{src,apps,libs,test}/**/*.ts\" --fix", "test": "jest", "test:watch": "jest --watch", "test:cov": "jest --coverage", "test:debug": "node --inspect-brk -r tsconfig-paths/register -r ts-node/register node_modules/.bin/jest --runInBand", "test:e2e": "jest --config ./test/jest-e2e.json"}, "dependencies": {"@azure/openai": "^2.0.0", "@ffmpeg-installer/ffmpeg": "^1.1.0", "@langchain/community": "^0.2.28", "@langchain/core": "^0.2.27", "@langchain/openai": "^0.2.7", "@nestjs/axios": "^3.0.2", "@nestjs/cache-manager": "^2.2.2", "@nestjs/common": "^10.0.0", "@nestjs/config": "^3.2.3", "@nestjs/core": "^10.0.0", "@nestjs/mapped-types": "*", "@nestjs/passport": "^10.0.3", "@nestjs/platform-express": "^10.0.0", "@nestjs/platform-socket.io": "^10.3.10", "@nestjs/schedule": "^4.1.2", "@nestjs/swagger": "^7.4.0", "@nestjs/throttler": "^6.0.0", "@nestjs/websockets": "^10.3.10", "@prisma/client": "^5.16.1", "@sentry/nestjs": "^8.14.0", "@sentry/node": "^8.14.0", "@sentry/profiling-node": "^8.14.0", "@supabase/supabase-js": "^2.50.0", "@types/adm-zip": "^0.5.7", "adm-zip": "^0.5.16", "axios": "^1.7.2", "body-parser": "^2.2.0", "class-transformer": "^0.5.1", "class-validator": "^0.14.1", "compression": "^1.7.4", "cross-env": "^7.0.3", "csv-parser": "^3.2.0", "fluent-ffmpeg": "^2.1.3", "hume": "^0.11.1", "langchain": "^0.2.16", "openai": "^5.1.1", "passport-jwt": "^4.0.1", "prisma": "^5.16.1", "reflect-metadata": "^0.2.0", "rxjs": "^7.8.1", "stripe": "^16.2.0", "uuid": "^10.0.0"}, "devDependencies": {"@nestjs/cli": "^10.0.0", "@nestjs/schematics": "^10.0.0", "@nestjs/testing": "^10.0.0", "@types/body-parser": "^1.19.6", "@types/express": "^4.17.17", "@types/jest": "^29.5.2", "@types/multer": "^1.4.11", "@types/node": "^20.3.1", "@types/supertest": "^6.0.0", "@typescript-eslint/eslint-plugin": "^6.0.0", "@typescript-eslint/parser": "^6.0.0", "cross-env": "^7.0.3", "eslint": "^8.42.0", "eslint-config-prettier": "^9.0.0", "eslint-plugin-prettier": "^5.0.0", "jest": "^29.5.0", "prettier": "^3.0.0", "source-map-support": "^0.5.21", "stripe": "^16.2.0", "supertest": "^6.3.3", "ts-jest": "^29.1.0", "ts-loader": "^9.4.3", "ts-node": "^10.9.1", "tsconfig-paths": "^4.2.0", "typescript": "^5.1.3"}, "jest": {"moduleFileExtensions": ["js", "json", "ts"], "rootDir": "src", "testRegex": ".*\\.spec\\.ts$", "transform": {"^.+\\.(t|j)s$": "ts-jest"}, "collectCoverageFrom": ["**/*.(t|j)s"], "coverageDirectory": "../coverage", "testEnvironment": "node"}}