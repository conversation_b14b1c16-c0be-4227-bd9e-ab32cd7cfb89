import {
  <PERSON>,
  CardContent,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
} from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { useToast } from "@/components/ui/use-toast";
import supabase from "@/lib/supabase";
import { Label } from "@/components/ui/label";
import { useForm } from "react-hook-form";
import { Button } from "@/components/ui/button";
import { Link, useNavigate } from "react-router-dom";

const ResetPassword = () => {
  const {
    register,
    handleSubmit,
    formState: { errors },
    reset,
  } = useForm({
    defaultValues: {
      email: "",
    },
  });
  const { toast } = useToast();

  const handleSubmitEmail = async (data) => {
    const { email } = data;
    try {
      await supabase.auth.resetPasswordForEmail(email, {
        redirectTo: `https://auditionrooms.com/update-password`,
      });
      toast({ title: "Check your email for updating your password" });
      reset();
    } catch (error) {
      console.error(`Error resetting password: ${error.message}`);
      toast({ title: `Error resetting password: ${error.message}` });
    }
  };
const navigate = useNavigate()
  return (
    <section className="h-screen flex justify-center items-center bg-primary1">
      <Card className="relative mx-auto max-w-sm md:min-w-[400px] bg-secondary2 border-border1">
         <div
         onClick={()=>navigate(-1)}
                  // to="/"
                  className="cursor-pointer m-3  absolute left-0 top-2 "
                >
                    <svg  xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="w-6 h-6 mr-2 stroke-white">
                <path d="M19 12H5"></path>
                <path d="M12 19l-7-7 7-7"></path>
              </svg>
                </div>
        <Link to="/" className="w-full mt-3 bg-secondary2 flex items-center justify-center">
          <img src="/logo.png" alt="Logo" className="h-20 w-auto invert" />
        </Link>
        <CardHeader className="flex justify-center items-center">
          <CardTitle className="text-xl text-text1">Reset Password</CardTitle>
          <CardDescription className="text-text2">
            Enter your email to reset your password
          </CardDescription>
        </CardHeader>
        <CardContent>
          <form
            onSubmit={handleSubmit(handleSubmitEmail)}
            className="grid gap-4"
          >
            <div className="grid gap-2">
              <Label htmlFor="email" className="text-text1">
                Confirm Email
              </Label>
              <Input
                id="email"
                type="email"
                placeholder="<EMAIL>"
                {...register("email", { required: "Email is required" })}
                className="bg-otherBtn text-text1 border-border1 focus:border-hover1"
              />
              {errors.email && (
                <p className="text-close">{errors.email.message}</p>
              )}
            </div>
            <Button
              type="submit"
              className="w-full bg-hover1 hover:bg-navItemActive text-text1"
            >
              Reset Password
            </Button>
          </form>
        </CardContent>
      </Card>
    </section>
  );
};

export default ResetPassword;
