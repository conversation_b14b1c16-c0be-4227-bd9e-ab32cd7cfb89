import React, { useEffect, useState } from "react";
import { Link, useLocation } from "react-router-dom";
import { Button } from "./ui/button";

const ScreenTopSection = ({ page, link, btnText, Icon, onClick, isCoach }) => {
  const location = useLocation();
  const [isMobile, setIsMobile] = useState(false);
  const [isProfilePageOpen, setIsProfilePageOpen] = useState(false);

  useEffect(() => {
    const checkScreenSize = () => {
      setIsMobile(window.innerWidth < 1024);
    };
    checkScreenSize();
    window.addEventListener("resize", checkScreenSize);
    return () => window.removeEventListener("resize", checkScreenSize);
  }, []);

  useEffect(() => {
    if (location.pathname === "/profile" && isMobile) {
      setIsProfilePageOpen(true);
    } else {
      setIsProfilePageOpen(false);
    }
  }, [location.pathname, isMobile]);

  return (
    <div
      data-aos="fade-in"
      data-os-once="true"
      className="sticky lg:relative top-0 flex items-center bg-[#202020] lg:bg-transparent p-4 lg:p-0 lg:py-9 z-40"
    >
      <div className="w-1/3 block lg:hidden"></div>
      <div className="w-1/3 lg:w-1/2 text-center lg:text-left">
        <h1 className="text-xl lg:text-3xl font-extrabold">{page}</h1>
      </div>
      {btnText !== null &&
        <div
          className={`w-1/3 lg:w-1/2 ${
            isProfilePageOpen ? "h-10" : "h-auto"
          } text-right`}
        >
          {!isProfilePageOpen && !isCoach && (
            <Link to={link}>
              <Button className="lg:px-8 lg:py-2" onClick={onClick}>
                <div className="flex gap-2">
                  <Icon size={16} />
                  {isMobile ? "" : btnText}
                </div>
              </Button>
            </Link>
          )}
        </div>
      }
    </div>
  );
};

export default ScreenTopSection;
