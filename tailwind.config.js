import tailwindScrollbarHide from "tailwind-scrollbar-hide";
import tailwindAnimate from "tailwindcss-animate";

/** @type {import('tailwindcss').Config} */
export default {
  darkMode: ["class"],
  content: [
    "./pages/**/*.{js,jsx}",
    "./components/**/*.{js,jsx}",
    "./app/**/*.{js,jsx}",
    "./src/**/*.{js,jsx}",
  ],
  prefix: "",
  theme: {
    container: {
      center: true,
      padding: "2rem",
      screens: {
        "2xl": "1400px",
      },
    },
    extend: {
      boxShadow: {
        buttonShadow: "0 0 20px 1px rgba(109, 103, 255, 0.5)",
      },
      screens: {
        sxl: "425px",
        sx: "375px",
      },
      colors: {
        border: "hsl(var(--border))",
        input: "hsl(var(--input))",
        ring: "hsl(var(--ring))",
        background: "hsl(var(--background))",
        foreground: "hsl(var(--foreground))",
        primary: {
          DEFAULT: "hsl(var(--primary))",
          foreground: "hsl(var(--primary-foreground))",
        },
        secondary: {
          DEFAULT: "hsl(var(--secondary))",
          foreground: "hsl(var(--secondary-foreground))",
        },
        destructive: {
          DEFAULT: "hsl(var(--destructive))",
          foreground: "hsl(var(--destructive-foreground))",
        },
        muted: {
          DEFAULT: "hsl(var(--muted))",
          foreground: "hsl(var(--muted-foreground))",
        },
        accent: {
          DEFAULT: "hsl(var(--accent))",
          foreground: "hsl(var(--accent-foreground))",
        },
        popover: {
          DEFAULT: "hsl(var(--popover))",
          foreground: "hsl(var(--popover-foreground))",
        },
        card: {
          DEFAULT: "hsl(var(--card))",
          foreground: "hsl(var(--card-foreground))",
        },
        primary1: "#171717",
        secondary1: "#FAFAFA",
        secondary2: "#1F1F1F",
        select: "hsl(0 0% 0%)",
        text1: "#FFFFFF",
        hover1: "#6D66FB",
        hover2: "#252525",
        text2: "#EBEBEB",
        navItemActive: "#625BF6",
        close: "hsl(0, 84%, 60%)",
        chatSec: "hsl(0, 0%, 6%)",
        otherBtn: "#2C2C2C",
        border1: "#474747",
        blue1: "#BAB7FF",
        blue2: "#7B75FF",
        blue3: "#262626",
        blue4: "#383481",
        purple: "#7E78FF",
        gray1: "#808080",
        gray2: "#2B2B2B",
        // Enhanced gradient colors for better visual consistency
        gradientStart: "#7B75FF",
        gradientEnd: "#625BF6"
      },
      borderRadius: {
        lg: "var(--radius)",
        md: "calc(var(--radius) - 2px)",
        sm: "calc(var(--radius) - 4px)",
      },
      keyframes: {
        "accordion-down": {
          from: { height: "0" },
          to: { height: "var(--radix-accordion-content-height)" },
        },
        "accordion-up": {
          from: { height: "var(--radix-accordion-content-height)" },
          to: { height: "0" },
        },
        "smooth-appear": {
          "0%": { "opacity": "0" },
          "100%": { "opacity": "1" }
        }
      },
      animation: {
        "accordion-down": "accordion-down 0.2s ease-out",
        "accordion-up": "accordion-up 0.2s ease-out",
        "smooth-appear": "smooth-appear 1s ease"
      },
      transitionProperty: {
        'height': 'height',
        'max-height': 'max-height'
      }
    },
  },
  plugins: [tailwindAnimate, tailwindScrollbarHide],
};
