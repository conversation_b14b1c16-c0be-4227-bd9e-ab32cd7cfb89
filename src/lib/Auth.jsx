import { useDispatch, useSelector } from "react-redux";
import { useEffect } from "react";
import { setIsLoading, setSession, setRole } from "@/redux/features/userSlice";
import supabase from "./supabase";
import SessionLoading from "@/components/SessionLoading";

function Auth() {
  const dispatch = useDispatch();
  const isSessionLoading = useSelector((state) => state.user.isLoading);
  const currentUserId = useSelector((state) => state.user.session?.user?.id);

  useEffect(() => {
    dispatch(setIsLoading(true));
    const fetchSession = async () => {
      try {
        const { data, error } = await supabase.auth.getSession();
        if (error) {
          console.error("Error fetching session:", error.message);
          return;
        }
        if (data.session) {
          dispatch(setSession(data.session));
        } else {
          dispatch(setSession(null));
        }
        dispatch(setIsLoading(false));
      } catch (error) {
        console.error("Error fetching session:", error);
        dispatch(setIsLoading(false));
      }
    };
    fetchSession();
    const { data: authListener } = supabase.auth.onAuthStateChange(
      (event, session) => {
        if (event === "SIGNED_IN" || event === "SIGNED_OUT") {
          dispatch(setSession(session));
        }
      }
    );
    return () => {
      authListener.subscription.unsubscribe();
    };
  }, [dispatch]);

  useEffect(()=>{
    const fetchUserRole = async (currentUserId) => {
      if(currentUserId){
        const { data, error: fetchError } = await supabase
        .from('users') // Use the table where the metadata is stored (e.g., 'profiles' or 'users')
        .select('metadata') // Specify the metadata field you want to retrieve
        .eq('user_id', currentUserId) // Filter by the current user's ID
        .single(); // Ensures only one record is returned (useful if 'id' is unique)
        if (fetchError) {
          dispatch(setRole(null));
        } else {
          dispatch(setRole(data?.metadata?.role));
        }
      }
    };
    fetchUserRole(currentUserId);
  },[currentUserId, dispatch]);

  if (isSessionLoading) {
    return <SessionLoading />;
  }

  return null;
}

export default Auth;
