import React, { useState } from "react";
import { useNavigate } from "react-router-dom";
import { But<PERSON> } from "@/components/ui/button";
import { ArrowRight, Sparkles } from "lucide-react";
import MonologueSelection from "@/components/MonologueSelection";
import VideoRecorderDialog from "@/components/VideoRecorderDialog";
import RecordedVideoDialog from "@/components/RecordedVideoDialog";
import MonologueGenerationDialog from "@/components/MonologueGenerationDialog";
import MonologueEditDialog from "@/components/MonologueEditDialog";
import MonologueAddDialog from "@/components/MonologueAddDialog";
import MonologueCSVDialog from "@/components/MonologueCSVDialog";
import { useSelector } from "react-redux";
import { FaVideo } from "react-icons/fa6";

const Upload = ({ parentId = null, onClose, isReply = false }) => {
  const [selectedMonologue, setSelectedMonologue] = useState(null);
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [isRecordedVideoDialogOpen, setIsRecordedVideoDialogOpen] =
    useState(false);
  const [isGenerateDialogOpen, setIsGenerateDialogOpen] = useState(false);
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
  const [isAddDialogOpen, setIsAddDialogOpen] = useState(false);
  const [isCSVDialogOpen, setIsCSVDialogOpen] = useState(false);
  const [editingMonologue, setEditingMonologue] = useState(null);

  const [updateMonologueFunction, setUpdateMonologueFunction] = useState(null);
  const [videoSrc, setVideoSrc] = useState(null);
  const navigate = useNavigate();
  const { session } = useSelector((state) => state.user);
  const { role: userRole } = useSelector((state) => state.user);
  const isAdmin = userRole === "admin";
  const isCoach = session?.user?.user_metadata?.role === "coach";

  const handleContinue = () => {
    if (selectedMonologue) {
      setIsEditDialogOpen(false);
      setIsDialogOpen(true);
    }
  };

  const closeDialog = () => {
    setIsDialogOpen(false);
  };

  const handleViewRecording = (src) => {
    setVideoSrc(src);
    setIsDialogOpen(false);
    setIsRecordedVideoDialogOpen(true);
  };

  const closeRecordedVideoDialog = () => {
    setIsRecordedVideoDialogOpen(false);
    setVideoSrc(null);
  };

  const handleUploadComplete = (result) => {
    closeRecordedVideoDialog();
    if (onClose) {
      onClose();
    }
    navigate(parentId ? `/post/${parentId}` : "/");
  };

  const handleUseGeneratedMonologue = (monologue) => {
    setSelectedMonologue({
      content: monologue,
      monologue_id: "generated",
    });
    setIsDialogOpen(true); // Open the VideoRecorderDialog immediately
  };

  const handleEditMonologue = (monologue, updateFunction) => {
    setEditingMonologue(monologue);
    setUpdateMonologueFunction(() => updateFunction);
    setIsEditDialogOpen(true);
  };
  const handleSaveEditedMonologue = (editedMonologue) => {
    if (updateMonologueFunction) {
      updateMonologueFunction(editedMonologue);
    }
    if (
      selectedMonologue &&
      selectedMonologue.monologue_id === editedMonologue.monologue_id
    ) {
      setSelectedMonologue(editedMonologue);
    }
    setIsEditDialogOpen(false);
  };

  return (
    <>
      {!isCoach && (
        <div className="flex justify-center flex-col items-center w-full">
          <div className={`w-full lg:p-9 sm:p-6 p-4`}>
            <div className="flex justify-between items-center lg:mt-0 mt-20">
              <h1 className="text-3xl font-extrabold">
                {isReply ? "Retake" : "Upload"}
              </h1>
              {/* <Button
                className={`px-8 py-2 ${
                  !selectedMonologue
                    ? "disabled cursor-not-allowed opacity-50"
                    : ""
                }`}
                onClick={handleContinue}
                disabled={!selectedMonologue}
              >
                <div className="flex gap-2 items-center">
                  <FaVideo size={16} />
                  Record Video
                </div>
              </Button> */}
              <div className="flex justify-between items-right">
                {userRole && userRole === 'admin' && (
                  <Button
                    className="bg-hover1 mr-2"
                    onClick={() => setIsCSVDialogOpen(true)}
                  >
                    Import CSV
                  </Button>
                )}

                {userRole && userRole === 'admin' && (
                  <Button
                    className="bg-hover1"
                    onClick={() => setIsAddDialogOpen(true)}
                  >
                    + Add
                  </Button>
                )}
              </div>
            </div>
            <div className="flex justify-between items-center mt-8 gap-2">
              <h4 className="text-xl font-semibold">Select your monologue</h4>
              <Button
                className="bg-hover1"
                onClick={() => setIsGenerateDialogOpen(true)}
              >
                <span className="flex gap-2 items-center">
                  {/* Icon visible on all screen sizes */}
                  <Sparkles size={16} />

                  {/* Text hidden on small screens, visible on medium and larger screens */}
                  <span className="hidden md:inline">
                    AI Monologue Generator
                  </span>
                </span>
              </Button>
            </div>
            <MonologueSelection
              onSelect={setSelectedMonologue}
              onEdit={handleEditMonologue}
              isAdmin={isAdmin}
            />
          </div>
          <VideoRecorderDialog
            isOpen={isDialogOpen}
            onClose={closeDialog}
            monologue={selectedMonologue?.content}
            onViewRecording={handleViewRecording}
            parentId={parentId}
          />
          <RecordedVideoDialog
            isOpen={isRecordedVideoDialogOpen}
            onClose={closeRecordedVideoDialog}
            videoSrc={videoSrc}
            parentId={parentId}
            onUploadComplete={handleUploadComplete}
          />
          <MonologueGenerationDialog
            isOpen={isGenerateDialogOpen}
            onClose={() => setIsGenerateDialogOpen(false)}
            onUse={handleUseGeneratedMonologue}
          />
          <MonologueEditDialog
            isOpen={isEditDialogOpen}
            onClose={() => setIsEditDialogOpen(false)}
            onSave={handleSaveEditedMonologue}
            monologue={editingMonologue}
            handleContinue={handleContinue}
          />
          <MonologueAddDialog
            isOpen={isAddDialogOpen}
            onClose={() => setIsAddDialogOpen(false)}
          />
          <MonologueCSVDialog
            isOpen={isCSVDialogOpen}
            onClose={() => setIsCSVDialogOpen(false)}
          />
        </div>
      )}
    </>
  );
};

export default Upload;
