import {
  Body,
  Controller,
  Get,
  HttpException,
  HttpStatus,
  Patch,
  Post,
  Req,
  UseGuards,
} from '@nestjs/common';
import { AuthService } from './auth.service';
import { Roles } from './decorators/roles.decorator';
import { USER_ROLE } from '../types/users.enum';
import { UserGuard } from './guards/user.guard';
import { RolesGuard } from './guards/role.guard';
import { DatabaseService } from '../database/database.service';

@Controller('auth')
export class AuthController {
  constructor(
    private readonly authService: AuthService,
    private readonly dbService: DatabaseService,
  ) {}

  @Post('login')
  async login(@Body() body) {
    // just for testing purposes. Actual login signup stuff is on frontend
    return await this.authService.login(body.email, body.password);
  }

  @Post('add_admin')
  @Roles(USER_ROLE.ADMIN) // only an admin can add another admin
  @UseGuards(UserGuard, RolesGuard)
  async addAdmin(@Body() body) {
    return await this.authService.signup(body.email, body.password, {
      role: 'admin',
    });
  }

  @Patch('set_password')
  @Roles(USER_ROLE.ADMIN, USER_ROLE.COACH, USER_ROLE.NORMAL_USER)
  @UseGuards(UserGuard, RolesGuard)
  async setPassword(@Req() req, @Body() body) {
    const response = await this.authService.setPassword(
      req.user.id,
      body.password,
    );
    if (response.error) {
      throw new HttpException(response.message, response.status);
    }
    return response;
  }

  @Post('flush_events_buffer')
  @Roles(USER_ROLE.ADMIN)
  async flushEventBuffer() {
    return await this.dbService.flushEventBuffer();
  }

  @Post('send_waitlist_mail')
  @Roles(USER_ROLE.ADMIN)
  async sendWaitlistMail() {
    return await this.dbService.sendWaitlistMail();
  }

  @Get('unflushed_events')
  @Roles(USER_ROLE.ADMIN)
  async unflushedEvents() {
    return await this.dbService.eventBuffer.length;
  }

  @Post('update-password-prisma')
  async updatePasswordPrisma(@Body('email') email: string, @Body('newPassword') newPassword: string) {
    if (!email || !newPassword) {
      throw new HttpException('Email and newPassword are required', HttpStatus.BAD_REQUEST);
    }

    try {
      return await this.authService.updatePasswordPrisma(email, newPassword);
    } catch (error) {
      throw new HttpException(error.message, HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }
}
