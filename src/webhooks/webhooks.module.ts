import { Modu<PERSON> } from '@nestjs/common';
import { HumeWebhookController } from '../controllers/hume-webhook.controller';
import { ExpressionAnalysisModule } from '../expression-analysis/expression-analysis.module';
import { PostsModule } from '../posts/posts.module';
import { DatabaseModule } from '../database/database.module';

@Module({
  imports: [
    DatabaseModule, // Required for database access
    ExpressionAnalysisModule, // Provides HumeAiService and AzureOpenAiService
    PostsModule, // Provides PostsService
  ],
  controllers: [HumeWebhookController],
})
export class WebhooksModule {}
