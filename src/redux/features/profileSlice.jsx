import { createApi, fetchBaseQuery } from "@reduxjs/toolkit/query/react";
import supabase from "@/lib/supabase";

// Fetch the access token from Supabase
const fetchAccessToken = async () => {
  const { data, error } = await supabase.auth.getSession();
  if (error) throw new Error(error.message);
  return data.session.access_token;
};

// Define the API service
export const profileApi = createApi({
  reducerPath: "profileApi",
  baseQuery: fetchBaseQuery({
    baseUrl: import.meta.env.VITE_API_BASE_URL,
    prepareHeaders: async (headers) => {
      const accessToken = await fetchAccessToken();
      if (accessToken) {
        headers.set("Authorization", `Bearer ${accessToken}`);
      }
      return headers;
    },
  }),
  keepUnusedDataFor: 600,
  endpoints: (builder) => ({
    fetchUserProfile: builder.query({
      query: () => "/profile",
      transformResponse: (response) => response.data,
    }),
    fetchMultipleUserProfiles: builder.query({
      query: (userIds) => ({
        url: "/profile",
        params: { user_id: userIds.join(",") },
      }),
      transformResponse: (response) => response.data,
    }),
    fetchUserProfilesByQuery: builder.query({
      query: (searchQuery) => ({
        url: "/profile/by_query",
        params: { search: encodeURIComponent(searchQuery) },
      }),
      transformResponse: (response) => response.data,
    }),
    updateProfile: builder.mutation({
      query: (formData) => ({
        url: "/profile/update_profile",
        method: "PATCH",
        body: formData,
      }),
      transformResponse: (response) => response.data,
      invalidatesTags: ["Profile"], // Invalidate the 'Profile' tag after update
    }),
  }),
});

// Export the auto-generated hooks for each query and mutation
export const {
  useFetchUserProfileQuery,
  useFetchMultipleUserProfilesQuery,
  useFetchUserProfilesByQueryQuery,
  useUpdateProfileMutation,
} = profileApi;

// Optionally, you can still have a slice to manage local state or additional actions
import { createSlice } from "@reduxjs/toolkit";

const profileSlice = createSlice({
  name: "userProfile",
  initialState: {
    profile: null,
  },
  reducers: {
    updateProfileLocally: (state, action) => {
      state.profile = { ...state.profile, ...action.payload };
    },
  },
});

export const { updateProfileLocally } = profileSlice.actions;

export default profileSlice.reducer;
