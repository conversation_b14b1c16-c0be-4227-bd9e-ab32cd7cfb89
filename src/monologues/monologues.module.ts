import { Module } from '@nestjs/common';
import { MonologuesService } from './monologues.service';
import { MonologuesController } from './monologues.controller';
import { DatabaseModule } from '../database/database.module';
// import { BotModule } from '../bot/bot.module'; // Temporarily disabled

@Module({
  imports: [DatabaseModule], // BotModule temporarily removed
  controllers: [MonologuesController],
  providers: [MonologuesService],
})
export class MonologuesModule {}
