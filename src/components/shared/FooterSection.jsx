import React from "react";
import {
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  F<PERSON><PERSON><PERSON><PERSON>ram,
  FaLinkedinIn,
} from "react-icons/fa";
import { NavLink } from "react-router-dom";
import LogoImg from '../../assets/images/logo.png';

const FooterSection = () => {
  const quickLinks = [
    { name: 'About Us', path: '/about-us' },
    { name: 'Features', path: '/features' },
    { name: 'Pricing', path: '/pricing' },
    { name: 'Contact', path: '/contact-us' }
  ]
  return (
    <footer className="bg-secondary2 text-text1 pt-12 px-4">
      <div className="w-full px-2 lg:px-4 md:px-4 sm:px-4">
        <div className="grid grid-cols-1 md:grid-cols-4 gap-8 mb-8">
          <div className="space-y-4 col-span-2">
            <h3 className="text-2xl font-bold">
              <img src={LogoImg} alt="" />
            </h3>
            <p className="text-sm text-gray1">AuditionRooms is a groundbreaking platform designed specifically for aspiring non-union actors,
              whether they have zero experience or decades of practice, to showcase their talent and grow
              through peer feedback.
            </p>
          </div>
          <div className="col-span-2 sm:col-span-1 md:col-span-1 lg:col-span-1">
            <h4 className="text-xl font-semibold mb-4">Quick Links</h4>
            <ul className="flex flex-wrap">
              {quickLinks.map(
                (link, index) => (
                  <li key={index} className="text-gray1 hover:text-hover1 transition-colors duration-200 mr-3 mb-2">
                    <NavLink to={link.path} className={({ isActive }) => isActive ? 'text-hover1' : ''}>{link.name}</NavLink>
                  </li>
                )
              )}
            </ul>
          </div>
          <div className="col-span-2 sm:col-span-1 md:col-span-1 lg:col-span-1">
            <h4 className="text-xl font-semibold mb-4">Connect</h4>
            <div className="flex flex-wrap">
              {[FaFacebookF, FaTwitter, FaInstagram, FaLinkedinIn].map(
                (Icon, index) => (
                  <a
                    key={index}
                    href="#"
                    className="bg-primary1 text-text1 p-2 rounded-full hover:bg-hover1 transition-all duration-300 mr-6 mb-4"
                    aria-label={`Social media link ${index + 1}`}
                  >
                    <Icon size={20} />
                  </a>
                )
              )}
            </div>
          </div>
        </div>
        <div className="border-t border-border1 py-4 text-center text-gray1">
          <p>
            &copy; {new Date().getFullYear()} Audition Rooms. All rights reserved.
          </p>
        </div>
      </div>
    </footer>
  );
};

export default FooterSection;
