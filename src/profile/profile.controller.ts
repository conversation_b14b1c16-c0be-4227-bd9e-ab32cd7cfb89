import {
  Body,
  Controller,
  Get,
  HttpException,
  Patch,
  Post,
  Query,
  Req,
  UploadedFile,
  UseGuards,
  UseInterceptors,
} from '@nestjs/common';
import { ProfileService } from './profile.service';
import { UserGuard } from '../auth/guards/user.guard';
import { FileInterceptor } from '@nestjs/platform-express';
import { UpdateProfileDto } from './dto/update-profile.dto';
import { USER_ROLE } from '../types/users.enum';

@Controller('profile')
export class ProfileController {
  constructor(private readonly profileService: ProfileService) {}

  
  @Get()
  @UseGuards(UserGuard) // get user by id, could be merged into by query endpoint
  async getUser(@Req() req, @Query('user_id') user_id?: string) {
    const resp = await this.profileService.getUser(
      user_id ? user_id : req.user.id,
      user_id ? false : true,
      req.user.public_user_data[0].metadata.role == USER_ROLE.COACH
        ? true
        : false,
    );
    if (resp.error) {
      throw new HttpException(resp.message, resp.status);
    }
    return resp;
  }

  @Get('by_query')
  @UseGuards(UserGuard)
  async getUsersbyQuery(@Req() req, @Query('search') search?: string) {
    // get users by query where it matches the email or full name
    const resp = await this.profileService.getUsersbyQuery(search);
    if (resp.error) {
      throw new HttpException(resp.message, resp.status);
    }
    return resp;
  }

  @Patch('update_profile')
  @UseGuards(UserGuard)
  @UseInterceptors(FileInterceptor('file'))
  async updateProfile(
    @Req() req,
    @Body() updateProfileDto: UpdateProfileDto,
    @UploadedFile() image: Express.Multer.File,
  ) {
    const resp = await this.profileService.updateProfile(
      req.user.id,
      updateProfileDto,
      image,
    );
    if (resp.error) {
      throw new HttpException(resp.message, resp.status);
    }
    return resp;
  }
}
