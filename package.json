{"name": "audition-room", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite --host", "build": "vite build", "lint": "eslint . --ext js,jsx --report-unused-disable-directives --max-warnings 0", "preview": "vite preview"}, "dependencies": {"@fluentui/react-rating": "^9.0.28", "@heroui/pagination": "^2.2.14", "@hookform/resolvers": "^3.9.0", "@radix-ui/react-alert-dialog": "^1.1.1", "@radix-ui/react-avatar": "^1.1.0", "@radix-ui/react-checkbox": "^1.1.1", "@radix-ui/react-dialog": "^1.1.1", "@radix-ui/react-dropdown-menu": "^2.1.1", "@radix-ui/react-hover-card": "^1.1.1", "@radix-ui/react-label": "^2.1.0", "@radix-ui/react-progress": "^1.1.7", "@radix-ui/react-scroll-area": "^1.1.0", "@radix-ui/react-select": "^2.1.1", "@radix-ui/react-slider": "^1.2.0", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-switch": "^1.1.0", "@radix-ui/react-tabs": "^1.1.0", "@radix-ui/react-toast": "^1.2.1", "@radix-ui/react-tooltip": "^1.1.2", "@reduxjs/toolkit": "^2.2.6", "@remixicon/react": "^4.6.0", "@supabase/supabase-js": "^2.44.2", "@tanstack/react-query": "^5.51.24", "@types/p5": "^1.7.6", "@types/papaparse": "^5.3.16", "aos": "^2.3.4", "class-variance-authority": "^0.7.0", "clsx": "^2.1.1", "esbuild-plugin-react-virtualized": "^1.0.4", "framer-motion": "^11.5.4", "lodash": "^4.17.21", "lucide-react": "^0.400.0", "p5": "^2.0.2", "papaparse": "^5.5.3", "react": "^18.3.1", "react-dom": "^18.3.1", "react-dropzone": "^14.2.3", "react-helmet-async": "^2.0.5", "react-hook-form": "^7.52.1", "react-hotkeys-hook": "^4.5.0", "react-icons": "^5.3.0", "react-p5": "^1.4.1", "react-p5-wrapper": "^4.1.2", "react-player": "^2.16.0", "react-redux": "^9.1.2", "react-render-if-visible": "^2.1.1", "react-router-dom": "^6.24.1", "react-share": "^5.2.0", "react-virtualized": "^9.22.5", "react-virtualized-auto-sizer": "^1.0.24", "react-webcam": "^7.2.0", "react-window": "^1.8.10", "recharts": "^2.15.3", "recordrtc": "^5.6.2", "socket.io-client": "^4.7.5", "tailwind-merge": "^2.6.0", "tailwind-scrollbar-hide": "^1.1.7", "tailwind-variants": "^1.0.0", "zod": "^3.23.8"}, "devDependencies": {"@types/react": "^18.3.3", "@types/react-dom": "^18.3.0", "@vitejs/plugin-react": "^4.3.1", "autoprefixer": "^10.4.19", "eslint": "^8.57.0", "eslint-plugin-react": "^7.34.2", "eslint-plugin-react-hooks": "^4.6.2", "eslint-plugin-react-refresh": "^0.4.7", "postcss": "^8.4.39", "tailwindcss": "^3.4.10", "tailwindcss-animate": "^1.0.7", "vite": "^5.3.1"}, "optionalDependencies": {"@rollup/rollup-linux-x64-gnu": "^4.19.1"}}