import React, { Fragment, useEffect, useRef, useState } from "react";
import { Dialog, DialogClose, DialogContent, DialogTitle, DialogTrigger } from "./ui/dialog";
import { Button } from "./ui/button";
import {
  MessageSquare,
  Send,
  CornerDownRight,
  ChevronUp,
  ChevronDown,
  MessageCircleMore,
  Ellipsis,
  Loader2,
  X,
} from "lucide-react";
import { Avatar, AvatarFallback, AvatarImage } from "./ui/avatar";
import { Input } from "./ui/input";
import Post from "./shared/Feed/Post";
import { useDispatch, useSelector } from "react-redux";
import { closeDialog, openDialog } from "@/redux/features/dialogSlice";
import {
  useAddCommentMutation,
  useDeleteCommentMutation,
  useGetCommentsQuery,
} from "@/redux/features/commentSlice";
import { formatTimeAgo } from "@/lib/utils";
import { Skeleton } from "./ui/skeleton";
import { toast, useToast } from "./ui/use-toast";
import { useNavigate } from "react-router-dom";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuTrigger,
} from "./ui/dropdown-menu";
import { DropdownMenuItem } from "@radix-ui/react-dropdown-menu";
import { useFetchUserProfileQuery } from "@/redux/features/profileSlice";

const Comment = ({
  comment,
  postId,
  inputRef,
  isReplying,
  setNewReply,
  setIsReplying,
  setNewComment,
  level = 0,
  isFreePlan,
}) => {
  const dispatch = useDispatch();
  const [replyText, setReplyText] = useState("");
  const [isExpanded, setIsExpanded] = useState(false);
  const [loading, setLoading] = useState(false);
  const [addComment] = useAddCommentMutation();
  const [deleteComment, { isLoading: isDeleting }] = useDeleteCommentMutation();
  const userId = useSelector((state) => state?.user?.session?.user?.id);

  const handleReply = () => {
    if (replyText.trim()) {
      dispatch(
        addComment({
          postId,
          commentText: replyText,
          parentId: comment.comment_id,
        })
      );
      setReplyText("");
      setIsReplying(false);
    }
  };

  const handleDeleteComment = async (comment_id) => {
    try {
      setLoading(true);
      const user_id = userId;
      if (!user_id) {
        throw new Error("User not authenticated");
      }
      await deleteComment({ comment_id, user_id, post_id: postId }).unwrap();
      toast({
        title: "Comment deleted",
        description: "Your comment has been successfully deleted.",
        variant: "success",
      });
    } catch (error) {
      console.error("Failed to delete comment: ", error);
      toast({
        title: "Error",
        description: "Failed to delete comment. Please try again.",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  const replyCount = comment.replies ? comment.replies.length : 0;

  const handleReplyClick = () => {
    if (isFreePlan) {
      toast({
        title: "Upgrade Required",
        description: "Please upgrade your plan to reply to comments.",
        variant: "destructive",
      });
      return;
    }
    setIsReplying(true);
    const mention = `@${comment.user_metadata.full_name} `;
    setNewReply(mention);
    setNewComment("");
    inputRef.current.focus();
  };

  return (
    <li
      className={`mb-4 text-text1 ${
        level > 0 ? "ml-20 pl-4 border-l-2 border-gray-300" : ""
      }`}
    >
      <div className="flex gap-4 items-start">
        {level > 0 && (
          <CornerDownRight className="text-gray-400 mt-2" size={16} />
        )}
        <Avatar>
          <AvatarImage
            src={comment?.user_metadata?.profile_picture}
            alt="Profile picture"
          />
          <AvatarFallback>
            {comment?.user_metadata?.full_name?.[0] || "U"}
          </AvatarFallback>
        </Avatar>

        <div className="flex flex-col w-full">
          <div className="flex items-center justify-between w-full">
            <div className="flex flex-col gap-1">
              <p className="font-semibold">
                {comment?.user_metadata?.full_name}
              </p>
              <p className="text-sm">{comment.comment}</p>
              <span className="text-xs text-text2">
                {formatTimeAgo(comment.created_at)}
              </span>
            </div>
            {userId === comment.user_id && (
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button
                    variant="ghost"
                    className="h-8 w-8 p-0 hover:bg-transparent hover:text-white"
                    disabled={isDeleting}
                  >
                    {isDeleting ? (
                      <Loader2 className="h-4 w-4 animate-spin" />
                    ) : (
                      <Ellipsis />
                    )}
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent
                  align="end"
                  className="bg-transparent min-w-12 border-gray-700"
                >
                  <DropdownMenuItem className="bg-primary1 text-white border-primary1">
                    <button
                      className="p-1 text-xs flex items-center"
                      onClick={() => handleDeleteComment(comment.comment_id)}
                      disabled={isDeleting}
                    >
                      {isDeleting && (
                        <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                      )}
                      Delete
                    </button>
                  </DropdownMenuItem>
                </DropdownMenuContent>
              </DropdownMenu>
            )}
          </div>

          <div className="mt-2 flex items-center gap-2">
            {level === 0 && (
              <button
                onClick={handleReplyClick}
                variant="default"
                size="sm"
                className="bg-transparent underline text-text1 text-[12px]"
                disabled={isFreePlan}
              >
                Reply
              </button>
            )}
            {level === 0 && replyCount > 0 && (
              <button
                onClick={() => setIsExpanded(!isExpanded)}
                variant="default"
                size="sm"
                className="flex items-center justify-center gap-1 bg-transparent underline text-text1 text-[12px]"
              >
                {isExpanded ? (
                  <ChevronUp size={14} />
                ) : (
                  <ChevronDown size={14} />
                )}
                {replyCount === 1 ? "Reply" : "Replies"} ({replyCount})
              </button>
            )}
          </div>

          {isReplying && (
            <div className="mt-2 flex items-center gap-2">
              <Input
                type="text"
                value={replyText}
                onChange={(e) => setReplyText(e.target.value)}
                placeholder="Write a reply..."
                className="flex-grow bg-primary1 text-secondary1"
              />
              <Button onClick={handleReply} size="sm">
                Send
              </Button>
              <Button
                onClick={() => setIsReplying(false)}
                variant="default"
                size="sm"
              >
                Cancel
              </Button>
            </div>
          )}
        </div>
      </div>

      {isExpanded &&
        level === 0 &&
        comment.replies &&
        comment.replies.length > 0 && (
          <ul className="mt-4">
            {comment.replies.map((reply) => (
              <Comment
                key={reply.comment_id}
                comment={reply}
                postId={postId}
                level={level + 1}
              />
            ))}
          </ul>
        )}
    </li>
  );
};

const PostOpenCommentsDialog = ({ postId, isFreePlan }) => {
  const navigate = useNavigate();
  const dispatch = useDispatch();

  const [newComment, setNewComment] = useState("");
  const [newReply, setNewReply] = useState("");
  const [replyingTo, setReplyingTo] = useState(null);
  const [isReplying, setIsReplying] = useState(false);

  const { data: profile } = useFetchUserProfileQuery();
  const currentUserProfile = profile;
  const { toast } = useToast();
  const [isPhoneOpen, setIsPhoneOpen] = useState(false);

  // For focusing the input field
  const inputRef = useRef(null);

  // For mobile window
  useEffect(() => {
    const checkScreenSize = () => {
      setIsPhoneOpen(window.innerWidth <= 625);
    };

    checkScreenSize();
    window.addEventListener("resize", checkScreenSize);

    return () => window.removeEventListener("resize", checkScreenSize);
  }, []);

  const { data: comments, isLoading, isFetching } = useGetCommentsQuery(postId);


  const [
    addComment,
    { isLoading: isAddingComment, isError: isBadComment, isSuccess },
  ] = useAddCommentMutation();

  const isDialogOpen = useSelector((state) => state.dialog.isDialogOpen);

  const userId = useSelector((state) => state?.user?.session?.user?.id);

  const handleAddComment = async () => {
     if (isFreePlan) {
       toast({
         title: "Upgrade Required",
         description: "Please upgrade your plan to comment on posts.",
         variant: "destructive",
       });
       return;
     }
   if (newComment.trim()) {
     setIsReplying(false);
     let commentText = newComment;
     let parentId = null;

     if (replyingTo) {
       const replyTag = `@${replyingTo.fullName}`;
       if (commentText.startsWith(replyTag)) {
         commentText = commentText.slice(replyTag.length);
       }
       parentId = replyingTo.commentId;
     }

     try {
       await addComment({ postId, commentText, parentId }).unwrap();
       setNewComment("");
       setReplyingTo(null);
       toast({
         title: "Comment added",
         description: "Your comment has been successfully added.",
         variant: "success",
       });
     } catch (error) {
       toast({
         title: "Error",
         description: "Failed to add comment. Please try again.",
         variant: "destructive",
       });
     }
   }
 };

  useEffect(() => {
    if (isSuccess) {
      setNewComment("");
      setReplyingTo(null);
    }
    if (isBadComment) {
      toast({
        variant: "destructive",
        title: "Error",
        description: "This comment is against our policy. Please try again.",
      });
    }
  }, [isSuccess, isBadComment, toast]);

  const handleNewCommentChange = (e) => {
    setNewComment(e.target.value);
    // if (replyingTo && !e.target.value.startsWith(`@${replyingTo.fullName}`)) {
    //   // if (replyingTo) {
    //   setReplyingTo(null);
    // }
  };

  const handleKeyDown = (e) => {
    if (e.key === "Backspace" && newComment === "") {
      setIsReplying(false);
    }

    if (e.key === "Enter") {
      handleAddComment();
    }
  };

  return (
    <Dialog
      onOpenChange={(isOpen) => {
        if (isOpen) {
          dispatch(openDialog());
        } else {
          dispatch(closeDialog());
        }
      }}
    >
      {React.createElement(
        !isDialogOpen ? DialogTrigger : Fragment,
        !isDialogOpen ? { asChild: true } : {},

        <Button className="px-4 py-2" disabled={isFreePlan}>
          <div className="flex justify-center items-center gap-2">
            <MessageSquare size={16} />
            <span className="hidden sm:inline ">Comment</span>
          </div>
        </Button>
      )}
      <DialogTitle>
        <DialogContent className="bg-primary1 border-none overflow-y-auto gap-2 scrollbar-hide lg:h-[95%] lg:min-w-[50%] min-w-[80%] h-screen sm:h-[80%]">
          {!isPhoneOpen &&
          <DialogClose className="absolute top-1 right-1 cursor-pointer">
             <X className="text-white" size={20} />
          </DialogClose>
          }
          {!isPhoneOpen && <Post postId={postId} />}
          
          {isPhoneOpen && (
            <div className="sticky top-0 flex items-center py-4 bg-primary1 z-50">
              <div className="w-1/3 flex items-center">
                <DialogClose asChild>
                  <button className="text-white">
                    {/* <ArrowLeft size={25} /> */}
                    <svg
                      width="25"
                      height="25"
                      viewBox="0 0 15 15"
                      fill="none"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <path
                        d="M6.85355 3.14645C7.04882 3.34171 7.04882 3.65829 6.85355 3.85355L3.70711 7H12.5C12.7761 7 13 7.22386 13 7.5C13 7.77614 12.7761 8 12.5 8H3.70711L6.85355 11.1464C7.04882 11.3417 7.04882 11.6583 6.85355 11.8536C6.65829 12.0488 6.34171 12.0488 6.14645 11.8536L2.14645 7.85355C1.95118 7.65829 1.95118 7.34171 2.14645 7.14645L6.14645 3.14645C6.34171 2.95118 6.65829 2.95118 6.85355 3.14645Z"
                        fill="currentColor"
                        fillRule="evenodd"
                        clipRule="evenodd"
                      ></path>
                    </svg>
                  </button>
                </DialogClose>
              </div>
              <div className="w-1/3 text-center">
                <h3 className="text-white text-lg font-bold">Comments</h3>
              </div>
              <div className="w-1/3"></div>
            </div>
          )}

          {/* Comments and No Comments (if empty) */}
          <div
            className={`md:px-4 flex flex-col ${
              comments?.length <= 4 ? "h-[80vh]" : "h-auto"
            } sm:h-auto`}
          >
            {isLoading ? (
              Array(9)
                .fill()
                .map((_, index) => (
                  <div
                    key={index}
                    className="flex items-center space-x-4 mb-4 w-full"
                  >
                    <Skeleton className="h-12 w-12 rounded-full" count={10} />
                    <div className="space-y-2">
                      <Skeleton className="h-4 w-[250px]" />
                      <Skeleton className="h-4 w-[200px]" />
                    </div>
                  </div>
                ))
            ) : comments?.length != 0 ? (
              <ul className={`mb-8 flex-1 overflow-y-auto`}>
                {comments?.map((comment) => (
                  <Comment
                    key={comment.comment_id}
                    inputRef={inputRef}
                    comment={comment}
                    postId={postId}
                    setNewReply={setNewReply}
                    setIsReplying={setIsReplying}
                    setNewComment={(text) => {
                      setNewComment(text);
                      setReplyingTo({
                        fullName: comment?.user_metadata?.full_name,
                        commentId: comment?.comment_id,
                      });
                    }}
                    isFreePlan={isFreePlan}
                  />
                ))}
              </ul>
            ) : (
              <p className="flex justify-center items-start gap-2 text-white mb-4 h-[80vh] md:h-[10vh]">
                <MessageCircleMore /> No Comments
              </p>
            )}
          </div>

          {/* Comment box */}
          <div className="sticky bottom-0 flex m-[-24px] md:m-[-40px] items-center  border-t-4 border-otherBtn px-4 md:px-8 lg:px-12 py-4 bg-primary1 gap-4">
            <Avatar>
              <AvatarImage
                src={currentUserProfile?.profile_picture_url}
                alt="Profile picture"
              />
              <AvatarFallback>
                {currentUserProfile?.full_name?.[0] || "U"}
              </AvatarFallback>
            </Avatar>
            {isAddingComment ? (
              <Skeleton className="h-10 w-full" />
            ) : (
              <div className="flex bg-otherBtn w-full rounded-md justify-center items-center px-1">
                {isReplying && (
                  <span className="bg-hover1 text-secondary1 px-1 text-[14px]">
                    {newReply}
                  </span>
                )}
                <Input
                  type="text"
                  ref={inputRef}
                  value={newComment}
                  onChange={handleNewCommentChange}
                  onKeyDown={handleKeyDown}
                  className={`w-full px-2 py-6 rounded-l bg-transparent text-secondary1 border-none outline-none focus-visible:ring-0 focus-visible:ring-ring focus-visible:ring-offset-0 placeholder:text-text2 text-[14px] flex-1`}
                  placeholder={
                    isFreePlan
                      ? "Upgrade to comment"
                      : isReplying
                      ? ""
                      : "Add a comment..."
                  }
                  disabled={isFreePlan}
                />
                <Button onClick={handleAddComment} className="bg-text1">
                  <Send size={16} />
                </Button>
              </div>
            )}
          </div>
        </DialogContent>
      </DialogTitle>
    </Dialog>
  );
};

export default PostOpenCommentsDialog;
