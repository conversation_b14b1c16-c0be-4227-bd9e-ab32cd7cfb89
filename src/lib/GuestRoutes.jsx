// ProtectedRoutes Component
import { Outlet, Navigate, useLocation } from "react-router-dom";
import { useSelector } from "react-redux";
import Loading from "@/components/Loading";


const GuestRoutes = () => {
  const { session, isLoading } = useSelector((state) => state.user);
  const isAuthenticated = session?.user;
  const location = useLocation();

  // Allow access to set-password and signin routes regardless of authentication status
  if (
    location.pathname === "/set-password"
  ) {
    return <Outlet />;
  }

  if (isLoading) {
    return (
      <div>
        <Loading />
      </div>
    );
  }

  return !isAuthenticated ? <Outlet /> : <Navigate to="/" />;
};

export default GuestRoutes;
