import { useCallback, useState } from "react";
import { useNavigate } from "react-router-dom";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { useToast } from "@/components/ui/use-toast";
import { Eye, EyeOff } from "lucide-react";
import { useSetCoachPasswordMutation } from "@/redux/features/coachApi";
import { useDispatch } from "react-redux";
import { clearSession } from "@/redux/features/userSlice";

const CoachSetPassword = () => {
  const [password, setPassword] = useState("");
  const [confirmPassword, setConfirmPassword] = useState("");
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  const navigate = useNavigate();
  const { toast } = useToast();

  const [setCoachPassword, { isLoading }] = useSetCoachPasswordMutation();

  const dispatch = useDispatch();

  const handleSubmit = async (e) => {
    e.preventDefault();
    if (password !== confirmPassword) {
      toast({
        title: "Error",
        description: "Passwords don't match",
        variant: "destructive",
      });
      return;
    }

    try {
      const response = await setCoachPassword(password).unwrap();
  

      if (response.status === 200) {
        toast({
          title: "Success",
          description: response.message || "Password set successfully!",
        });

        try {
          dispatch(clearSession());
         
        } catch (clearError) {
          console.error("Error clearing session:", clearError);
        }

        navigate("/signin");
      } else {
        throw new Error(response.message || "Failed to set password");
      }
    } catch (error) {
      console.error("Error in handleSubmit:", error);
      toast({
        title: "Error",
        description:
          error.message ||
          "An unexpected error occurred while setting the password",
        variant: "destructive",
      });
    }
  };

  return (
    <div className="min-h-screen flex items-center justify-center bg-primary1 p-4">
      <div className="bg-secondary2 border border-border1 p-8 rounded-lg shadow-lg w-full max-w-md">
        <h2 className="text-3xl font-bold mb-2 text-text1">
          Coach Account Setup
        </h2>
        <p className="text-text2 mb-6">
          Set your password to complete your account setup.
        </p>
        <form onSubmit={handleSubmit} className="space-y-4">
          <div className="relative">
            <Input
              type={showPassword ? "text" : "password"}
              placeholder="New Password"
              value={password}
              onChange={(e) => setPassword(e.target.value)}
              className="w-full bg-otherBtn text-text1 border-border1 pr-10 placeholder:text-text2"
            />
            <button
              type="button"
              onClick={() => setShowPassword(!showPassword)}
              className="absolute inset-y-0 right-0 pr-3 flex items-center text-text2 hover:text-text1"
            >
              {showPassword ? <EyeOff size={20} /> : <Eye size={20} />}
            </button>
          </div>
          <div className="relative">
            <Input
              type={showConfirmPassword ? "text" : "password"}
              placeholder="Confirm Password"
              value={confirmPassword}
              onChange={(e) => setConfirmPassword(e.target.value)}
              className="w-full bg-otherBtn text-text1 border-border1 pr-10 placeholder:text-text2"
            />
            <button
              type="button"
              onClick={() => setShowConfirmPassword(!showConfirmPassword)}
              className="absolute inset-y-0 right-0 pr-3 flex items-center text-text2 hover:text-text1"
            >
              {showConfirmPassword ? <EyeOff size={20} /> : <Eye size={20} />}
            </button>
          </div>
          <Button
            type="submit"
            disabled={isLoading}
            className="w-full bg-navItemActive hover:bg-hover1 text-text1 py-2 rounded transition duration-200"
          >
            {isLoading ? "Setting Password..." : "Set Password"}
          </Button>
        </form>
      </div>
    </div>
  );
};

export default CoachSetPassword;
