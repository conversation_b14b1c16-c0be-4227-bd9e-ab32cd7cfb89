import { Button } from "@/components/ui/button";
import { MoveR<PERSON>, <PERSON><PERSON><PERSON>he<PERSON> } from "lucide-react";
import { useNavigate } from "react-router-dom";
import { FaAward } from 'react-icons/fa6'
import bannerImg from '../../../assets/images/home-banner-new.png';
// import { Input } from "@/components/ui/input";

const HeroSection = () => {
  const scrollToHowItWorks = () => {
    const howItWorksSection = document.getElementById("how-it-works");
    if (howItWorksSection) {
      howItWorksSection.scrollIntoView({ behavior: "smooth" });
    }
  };

  const navigate = useNavigate()
  // const [promoCode, setPromoCode] = useState(Array(4).fill(""));
  // const inputRefs = useRef([]);

  // const handleChange = (index, value) => {
  //   if (value.length <= 1) {
  //     const newPromoCode = [...promoCode];
  //     newPromoCode[index] = value.toUpperCase();
  //     setPromoCode(newPromoCode);

  //     // Move to next input if value is entered
  //     if (value && index < 3) {
  //       inputRefs.current[index + 1]?.focus();
  //     }
  //   }
  // };

  // const handleKeyDown = (index, e) => {
  //   if (e.key === "Backspace" && !promoCode[index] && index > 0) {
  //     inputRefs.current[index - 1]?.focus();
  //   }
  // };

  const handleNavigate = () => {
    navigate("/signin")
  }
  return (
    <section id="home" className="relative text-white h-[600px] flex items-center overflow-hidden">
      <div
        className="absolute inset-0 w-full h-full bg-cover bg-no-repeat bg-center"
        style={{
          backgroundImage: `linear-gradient(to left, rgba(0,0,0,0.8) 10%, transparent 80%), url(${bannerImg})`,
          backgroundSize: 'cover',
          backgroundPosition: 'center',
          transform: 'scaleX(-1)',
        }}
      ></div>
      <div className="relative lg:px-10 px-5 flex items-center h-full w-full"> 
        <div className="flex flex-col lg:flex-row items-start justify-between gap-12">
          <div className="space-y-4">
            <h1 className="text-3xl sm:text-3xl md:text-4xl lg:text-7xl font-bold leading-tight">
              <span className="text-navItemActive">Your Stage,</span><br /> Your Way - Virtually!
            </h1>
            <p className="text-l xxl:text-3xl text-gray-300 max-w-lg pb-5">
              Step into the spotlight with Audition Rooms—your ultimate Virtual Audition Platform designed for all actors.
            </p>
            <div className="flex gap-4 flex-col sm:flex-row">
              <Button
                className="bg-navItemActive hover:bg-navItemActive/90 text-white py-3 px-6 rounded-lg h-14"
                onClick={handleNavigate}
              >
                Get Started
                <MoveRight className="ml-2 h-5 w-5" />
              </Button>
              <Button
                variant="outline"
                className="bg-transparent text-white border-white hover:bg-white hover:text-primary1 py-3 px-6 rounded-lg h-14"
                onClick={scrollToHowItWorks}
              >
                Learn More
              </Button>
            </div>

            {/* <Button
              className=" text-white font-bold py-3 px-6 rounded-lg text-lg mb-6"
              onClick={() => console.log("Get Started clicked")}
            >
              Get Started for Free
            </Button>
            <div className="mb-6">
              <label className="block text-[#8B8B9B] mb-2">
                Enter promo code:
              </label>
              <div className="flex space-x-2">
                {promoCode.map((digit, index) => (
                  <Input
                    key={index}
                    ref={(el) => (inputRefs.current[index] = el)}
                    className="w-12 h-12 text-center bg-primary1 border-2 border-[#333355] text-white rounded-lg focus-visible:ring-0 focus-visible:ring-offset-0 focus-visible:border-navItemActive"
                    type="text"
                    maxLength={1}
                    value={digit}
                    onChange={(e) => handleChange(index, e.target.value)}
                    onKeyDown={(e) => handleKeyDown(index, e)}
                  />
                ))}
              </div>
            </div>
            <Button
              className=" font-semibold py-2 px-4 rounded-md"
              onClick={() =>
                console.log(`Applied promo code: ${promoCode.join("")}`)
              }
            >
              Apply Code
            </Button>
            <p className="text-sm text-[#8B8B9B] mt-4">
              Sign up by October 31 (Halloween) to use the site for free!
            </p> */}
            <div className="flex items-center space-x-4">
              <div className="flex items-center space-x-2">
                <FaAward size={45} className="text-navItemActive text-xl"></FaAward>
                <p className="text-lg  sm:text-lg max-w-[350px] text-gray-300 py-6">
                  Become a member and unlock a world of possibilities!
                </p>
              </div>
            </div>
          </div>
      </div>
      </div>
    </section>
  );
};

export default HeroSection;
