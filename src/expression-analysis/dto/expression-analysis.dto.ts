import { IsString, IsOptional, <PERSON>N<PERSON>ber } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';

export class ExpressionAnalysisResponseDto {
  @ApiProperty({ description: 'Post ID' })
  @IsString()
  post_id: string;

  @ApiProperty({ description: 'Analysis status', enum: ['analyzing', 'completed', 'failed'] })
  @IsString()
  analysis_status: string;

  @ApiProperty({ description: 'HumeAI analysis results', required: false })
  @IsOptional()
  hume_analysis?: any;

  @ApiProperty({ description: 'AI-generated insights', required: false })
  @IsOptional()
  ai_insights?: any;

  @ApiProperty({ description: 'Post creation date' })
  created_at: Date;

  @ApiProperty({ description: 'Whether the post has a video' })
  has_video: boolean;
}

export class AnalysisHistoryDto {
  @ApiProperty({ description: 'Post ID' })
  @IsString()
  post_id: string;

  @ApiProperty({ description: 'Analysis status' })
  @IsString()
  status: string;

  @ApiProperty({ description: 'Post creation date' })
  created_at: Date;

  @ApiProperty({ description: 'Content preview' })
  @IsOptional()
  @IsString()
  content_preview?: string;

  @ApiProperty({ description: 'Whether insights are available' })
  has_insights: boolean;

  @ApiProperty({ description: 'Quick summary from insights' })
  @IsOptional()
  @IsString()
  quick_summary?: string;
}

export class HealthCheckDto {
  @ApiProperty({ description: 'HumeAI service status' })
  hume_ai: boolean;

  @ApiProperty({ description: 'Azure OpenAI service status' })
  azure_openai: boolean;

  @ApiProperty({ description: 'Overall system health' })
  overall: boolean;
}
