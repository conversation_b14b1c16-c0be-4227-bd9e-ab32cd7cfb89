
import supabase from "@/lib/supabase";
import { createApi, fetchBaseQuery } from "@reduxjs/toolkit/query/react";

const fetchAccessToken = async () => {
  const { data, error } = await supabase.auth.getSession();
  if (error) throw new Error(error.message);
  return data.session.access_token;
};

const buildCommentTree = (comments) => {
  const commentMap = {};
  const rootComments = [];

  comments.forEach((comment) => {
    commentMap[comment.comment_id] = { ...comment, replies: [] };
  });

  comments.forEach((comment) => {
    if (comment.parent_id) {
      const parent = commentMap[comment.parent_id];
      if (parent) {
        parent.replies.push(commentMap[comment.comment_id]);
      } else {
        rootComments.push(commentMap[comment.comment_id]);
      }
    } else {
      rootComments.push(commentMap[comment.comment_id]);
    }
  });

  return rootComments;
};

export const commentApi = createApi({
  reducerPath: "commentApi",
  baseQuery: fetchBaseQuery({
    baseUrl: import.meta.env.VITE_API_BASE_URL,
    prepareHeaders: async (headers) => {
      const token = await fetchAccessToken();
      headers.set("Authorization", `Bearer ${token}`);
      headers.set("Content-Type", "application/json");
      return headers;
    },
  }),
  keepUnusedDataFor: 600,
  tagTypes: ["Comments"],
  endpoints: (builder) => ({
    getComments: builder.query({
      query: (postId) => `posts/comments/${postId}`,
      transformResponse: (response) => buildCommentTree(response.data),
      providesTags: (result, error, postId) => [
        { type: "Comments", id: postId },
      ],
    }),
    addComment: builder.mutation({
      query: ({ postId, commentText, parentId = null }) => ({
        url: "/posts/add_comment",
        method: "POST",
        body: { post_id: postId, comment: commentText, parent_id: parentId },
      }),
      invalidatesTags: (result, error, { postId }) => [
        { type: "Comments", id: postId },
      ],
    }),
    deleteComment: builder.mutation({
      query: ({ comment_id, user_id }) => ({
        url: `/posts/delete_comment/${comment_id}`,
        method: "DELETE",
        body: { user_id },
      }),
      invalidatesTags: (result, error, { post_id }) => [
        { type: "Comments", id: post_id },
      ],
    }),
  }),
});

export const {
  useGetCommentsQuery,
  useAddCommentMutation,
  useDeleteCommentMutation,
} = commentApi;