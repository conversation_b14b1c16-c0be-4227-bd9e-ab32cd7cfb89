import { Skeleton } from "@/components/ui/skeleton";

const PricingPlanSkeleton = () => {
	return (
    <div className="rounded-lg p-8 flex flex-col h-full ">
      <div className="flex justify-between items-center mb-4">
        <Skeleton className="h-40 w-1/2" />
      </div>
      <Skeleton className="h-8 w-1/3 mb-6" />
      <ul className="flex-grow mb-6">
        {[1, 2, 3].map((_, index) => (
          <li key={index} className="mb-3 flex items-center">
            <Skeleton className="w-4 h-4 rounded-full mr-2" />
            <Skeleton className="h-4 w-3/4" />
          </li>
        ))}
      </ul>
      <Skeleton className="h-10 w-full" />
    </div>
  );
}
export default PricingPlanSkeleton