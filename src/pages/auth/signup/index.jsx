import React, { useState, useEffect } from "react";
import { useF<PERSON>, Controller } from "react-hook-form";
import { Link, useSearchParams,useNavigate } from "react-router-dom";
import {
  EyeIcon,
  EyeOffIcon,
  LockIcon,
  MailIcon,
  UserIcon,
} from "lucide-react";
import { useToast } from "@/components/ui/use-toast";
import supabase from "@/lib/supabase";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Checkbox } from "@/components/ui/checkbox";
import TermsAndConditionsDialog from "@/components/TermsAndConditionsDialog";

const SignUp = () => {
  const [isLoading, setIsLoading] = useState(false);
  const [isCheckingEmail, setIsCheckingEmail] = useState(false);
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  const [isFormComplete, setIsFormComplete] = useState(false);
  const [searchParams] = useSearchParams();
  const to = searchParams.get("to") || "/signin";
  const { toast } = useToast();

  const {
    register,
    handleSubmit,
    control,
    formState: { errors, isValid },
    reset,
    getValues,
    trigger,
    watch,
    setError,
    clearErrors,
  } = useForm({
    mode: "onChange",
    defaultValues: {
      fullName: "",
      email: "",
      password: "",
      confirmPassword: "",
      gender: "",
      over18: false,
      terms: false,
    },
  });

  const formValues = watch();

  // Check if email is already registered
  const checkEmailExists = async (email) => {
    if (!email || errors.email?.type === "pattern") return false;

    setIsCheckingEmail(true);
    try {
      const { data, error } = await supabase.auth.signInWithOtp({
        email,
        options: {
          shouldCreateUser: false,
        },
      });

      if (!error) {
        setError("email", {
          type: "manual",
          message: "This email is already registered. Please sign in instead.",
        });
        return true;
      }

      if (error.message.includes("Email not found")) {
        clearErrors("email");
        return false;
      }

      console.error("Email check error:", error);
      return false;
    } catch (error) {
      console.error("Email verification error:", error);
      return false;
    } finally {
      setIsCheckingEmail(false);
    }
  };

  // Effect to check if all fields are filled and valid
  useEffect(() => {
    const isAllFieldsFilled =
      formValues.fullName &&
      formValues.email &&
      formValues.password &&
      formValues.confirmPassword &&
      formValues.gender &&
      formValues.over18 &&
      formValues.terms &&
      Object.keys(errors).length === 0;

    setIsFormComplete(isAllFieldsFilled);
  }, [formValues, errors]);

  const handleSignUp = async (data) => {
    // Final email check before submission
    const emailExists = await checkEmailExists(data.email);
    if (emailExists) {
      toast({
        title: "Email Already Registered",
        description: "Please sign in instead or use a different email address.",
        variant: "destructive",
      });
      return;
    }

    if (!data.over18 || !data.terms) {
      toast({
        title: "Required Checkboxes",
        description:
          "Please confirm your age and accept the terms & conditions to continue.",
        variant: "destructive",
      });
      return;
    }

    if (data.password !== data.confirmPassword) {
      toast({
        title: "Password Mismatch",
        description: "Your passwords do not match. Please try again.",
        variant: "destructive",
      });
      return;
    }

    setIsLoading(true);
    try {
      const { error } = await supabase.auth.signUp({
        email: data.email,
        password: data.password,
        options: {
          emailRedirectTo: window.location.origin + to,
          data: {
            full_name: data.fullName,
            gender: data.gender,
          },
        },
      });

      if (error) throw error;

      toast({
        title: "Registration Successful",
        description: "Please check your email to verify your account.",
        variant: "success",
      });
      reset();
    } catch (error) {
      toast({
        title: "Signup Failed",
        description: error.message,
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };
const navigate = useNavigate()
  return (
    <div className="min-h-screen bg-gradient-to-br from-primary1 to-secondary2 flex items-center justify-center p-4">
      <Card className="w-full relative max-w-md bg-secondary2/80 backdrop-blur-md border-border1 shadow-xl">
      <div
         onClick={()=>navigate(-1)}
                  // to="/"
                  className="cursor-pointer m-3  absolute left-0 top-2 "
                >
                    <svg  xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="w-6 h-6 mr-2 stroke-white">
                <path d="M19 12H5"></path>
                <path d="M12 19l-7-7 7-7"></path>
              </svg>
                </div>
        <Link
          to="/"
          className="max-w-max m-auto h-16 bg-secondary2 flex items-center justify-center"
        >
          <img src="/logo.png" alt="Logo" className="mt-8 h-20 w-auto invert" />
        </Link>

        <CardHeader>
          <CardTitle className="text-3xl font-bold text-text1 text-center">
            Create an Account
          </CardTitle>
          <CardDescription className="text-text2 text-center">
            Enter your details to sign up
          </CardDescription>
        </CardHeader>

        <CardContent className="p-6">
          <form onSubmit={handleSubmit(handleSignUp)} className="space-y-4">
            {/* Full Name Field */}
            <div className="space-y-2">
              <Label htmlFor="fullName" className="text-text1">
                Full Name
              </Label>
              <div className="relative">
                <Input
                  id="fullName"
                  type="text"
                  placeholder="John Doe"
                  className="pl-10 bg-otherBtn border-border1 text-text1 placeholder:text-text2"
                  {...register("fullName", {
                    required: "Full name is required",
                    pattern: {
                      value: /^[A-Za-z]+(?: [A-Za-z]+)+$/,
                      message: "Please enter a valid name",
                    },
                    minLength: {
                      value: 2,
                      message: "Name must be at least 2 characters",
                    },
                  })}
                />
                <UserIcon
                  className="absolute left-3 top-1/2 -translate-y-1/2 text-text2"
                  size={18}
                />
              </div>
              {errors.fullName && (
                <p className="text-close text-sm">{errors.fullName.message}</p>
              )}
            </div>

            {/* Email Field */}
            <div className="space-y-2">
              <Label htmlFor="email" className="text-text1">
                Email
              </Label>
              <div className="relative">
                <Input
                  id="email"
                  type="email"
                  placeholder="<EMAIL>"
                  className="pl-10 bg-otherBtn border-border1 text-text1 placeholder:text-text2"
                  {...register("email", {
                    required: "Email is required",
                    pattern: {
                      value: /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/,
                      message: "Please enter a valid email address",
                    },
                    onBlur: async (e) => {
                      if (e.target.value && !errors.email) {
                        await checkEmailExists(e.target.value);
                      }
                    },
                  })}
                />
                <MailIcon
                  className="absolute left-3 top-1/2 -translate-y-1/2 text-text2"
                  size={18}
                />
                {isCheckingEmail && (
                  <div className="absolute right-3 top-1/2 -translate-y-1/2">
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-hover1"></div>
                  </div>
                )}
              </div>
              {errors.email && (
                <p className="text-close text-sm">{errors.email.message}</p>
              )}
            </div>

            {/* Password Field */}
            <div className="space-y-2">
              <Label htmlFor="password" className="text-text1">
                Password
              </Label>
              <div className="relative">
                <Input
                  id="password"
                  type={showPassword ? "text" : "password"}
                  placeholder="********"
                  className="pl-10 pr-10 bg-otherBtn border-border1 text-text1 placeholder:text-text2"
                  {...register("password", {
                    required: "Password is required",
                    minLength: {
                      value: 8,
                      message: "Password must be at least 8 characters long",
                    },
                    pattern: {
                      value:
                        /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]{8,}$/,
                      message:
                        "Password must contain at least one uppercase letter, one lowercase letter, one number, and one special character",
                    },
                  })}
                />
                <LockIcon
                  className="absolute left-3 top-1/2 -translate-y-1/2 text-text2"
                  size={18}
                />
                <button
                  type="button"
                  onClick={() => setShowPassword(!showPassword)}
                  className="absolute right-3 top-1/2 -translate-y-1/2 text-text2 hover:text-text1"
                >
                  {showPassword ? (
                    <EyeOffIcon size={18} />
                  ) : (
                    <EyeIcon size={18} />
                  )}
                </button>
              </div>
              {errors.password && (
                <p className="text-close text-sm">{errors.password.message}</p>
              )}
            </div>

            {/* Confirm Password Field */}
            <div className="space-y-2">
              <Label htmlFor="confirmPassword" className="text-text1">
                Confirm Password
              </Label>
              <div className="relative">
                <Input
                  id="confirmPassword"
                  type={showConfirmPassword ? "text" : "password"}
                  placeholder="********"
                  className="pl-10 pr-10 bg-otherBtn border-border1 text-text1 placeholder:text-text2"
                  {...register("confirmPassword", {
                    required: "Please confirm your password",
                    validate: (value) =>
                      value === getValues("password") ||
                      "Passwords do not match",
                  })}
                />
                <LockIcon
                  className="absolute left-3 top-1/2 -translate-y-1/2 text-text2"
                  size={18}
                />
                <button
                  type="button"
                  onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                  className="absolute right-3 top-1/2 -translate-y-1/2 text-text2 hover:text-text1"
                >
                  {showConfirmPassword ? (
                    <EyeOffIcon size={18} />
                  ) : (
                    <EyeIcon size={18} />
                  )}
                </button>
              </div>
              {errors.confirmPassword && (
                <p className="text-close text-sm">
                  {errors.confirmPassword.message}
                </p>
              )}
            </div>

            {/* Gender Field */}
            <div className="space-y-2">
              <Label htmlFor="gender" className="text-text1">
                Gender
              </Label>
              <Controller
                name="gender"
                control={control}
                rules={{ required: "Please select your gender" }}
                render={({ field }) => (
                  <Select
                    onValueChange={field.onChange}
                    value={field.value}
                    defaultValue={field.value}
                  >
                    <SelectTrigger className="w-full bg-otherBtn border-border1 text-text1">
                      <SelectValue placeholder="Select your gender" />
                    </SelectTrigger>
                    <SelectContent className="bg-otherBtn border-border1 text-text1">
                      <SelectItem value="male">Male</SelectItem>
                      <SelectItem value="female">Female</SelectItem>
                      <SelectItem value="other">Other</SelectItem>
                      <SelectItem value="prefer-not-to-say">
                        Prefer not to say
                      </SelectItem>
                    </SelectContent>
                  </Select>
                )}
              />
              {errors.gender && (
                <p className="text-close text-sm">{errors.gender.message}</p>
              )}
            </div>

            {/* Age Verification Checkbox */}
            <div className="flex items-center space-x-2">
              <Controller
                name="over18"
                control={control}
                render={({ field }) => (
                  <Checkbox
                    id="over18"
                    checked={field.value}
                    onCheckedChange={field.onChange}
                    className="border-text2 data-[state=checked]:bg-hover1 data-[state=checked]:border-hover1"
                  />
                )}
              />
              <Label htmlFor="over18" className="text-text1 text-sm">
                I confirm that I am over 18 years old
              </Label>
            </div>

            {/* Terms and Conditions Checkbox */}
            <div className="flex items-center space-x-2">
              <Controller
                name="terms"
                control={control}
                render={({ field }) => (
                  <Checkbox
                    id="terms"
                    checked={field.value}
                    onCheckedChange={field.onChange}
                    className="border-text2 data-[state=checked]:bg-hover1 data-[state=checked]:border-hover1"
                  />
                )}
              />
              <Label htmlFor="terms" className="text-text1 text-sm">
                I accept the{" "}
                <TermsAndConditionsDialog>
                  <span className="text-hover1 hover:underline cursor-pointer">
                    terms and conditions
                  </span>
                </TermsAndConditionsDialog>
              </Label>
            </div>

            {/* Submit Button */}
            <Button
              type="submit"
              className="w-full bg-hover1 text-white hover:bg-navItemActive transition-colors"
              disabled={!isFormComplete || isLoading || isCheckingEmail}
            >
              {isLoading
                ? "Creating account..."
                : isCheckingEmail
                ? "Checking email..."
                : "Sign Up"}
            </Button>
          </form>
        </CardContent>

        <CardFooter className="flex flex-col space-y-4">
          <div className="text-center text-text2">
            Already have an account?{" "}
            <Link
              to="/signin"
              className="text-hover1 hover:underline transition-all duration-300"
            >
              Sign in
            </Link>
          </div>
          {/* <Link to="/coach-apply" className="w-full">
            <Button
              variant="outline"
              className="w-full bg-otherBtn text-text1 hover:text-text1 hover:bg-hover2 border-border1 transition-all duration-300 ease-in-out"
            >
              Sign up as a Coach
            </Button>
          </Link> */}
        </CardFooter>
      </Card>
    </div>
  );
};

export default SignUp;