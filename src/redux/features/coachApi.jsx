import { createApi, fetchBaseQuery } from "@reduxjs/toolkit/query/react";
import { fetchAccessToken } from "./chatSlice";

export const coachApi = createApi({
  reducerPath: "coachApi",
  baseQuery: fetchBaseQuery({
    baseUrl: import.meta.env.VITE_API_BASE_URL,
    prepareHeaders: async (headers, { endpoint }) => {
      if (endpoint !== "applyCoach") {
        const token = await fetchAccessToken();
        if (token) {
          headers.set("authorization", `Bearer ${token}`);
        }
      }
      return headers;
    },
  }),
  keepUnusedDataFor: 600,
  endpoints: (builder) => ({
    applyCoach: builder.mutation({
      query: (coachData) => ({
        url: "/coaches/apply",
        method: "POST",
        body: coachData,
      }),
    }),
    setCoachPassword: builder.mutation({
      query: (password) => ({
        url: "/auth/set_password",
        method: "PATCH",
        body: { password },
      }),
    }),
    getCoachPlans: builder.query({
      query: () => "/plans/get_plans_coach",
    }),
    getCoaches: builder.query({
      query: () => "/coaches/all",
      transformResponse: (response) => response.data,
    }),
    updateCoachStatus: builder.mutation({
      query: (new_status) => ({
        url: "/coaches/update_status",
        method: "PATCH",
        body: { new_status },
      }),
    }),
  }),
});

export const {
  useApplyCoachMutation,
  useSetCoachPasswordMutation,
  useGetCoachPlansQuery,
  useGetCoachesQuery,
  useUpdateCoachStatusMutation,
} = coachApi;
