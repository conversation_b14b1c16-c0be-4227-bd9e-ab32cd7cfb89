
import { Button } from "@/components/ui/button";
import ProfileImg from '../../../assets/images/profile.png'
import Profile2Img from '../../../assets/images/profile2.png'
import WaveImg from '../../../assets/images/wave.png'
import { Gem } from "lucide-react";

const Profile = ({ image, name, desc }) => {
  return (
    <>
      <div className="rounded-2xl h-64 flex flex-col justify-center items-center mb-10">
        <img src={image} alt="profile" className="w-40 h-56 rounded-2xl overflow-hidden" />
        <h3 className="text-2xl font-extrabold text-text1 py-2">{name}</h3>
        <div className="w-[50px] h-[3px] mb-1 bg-navItemActive" />
        <p className="text-xs text-text1 py-1">{desc}</p>
      </div>
    </>
  );
}

const CoacheSection = () => {
  return (
    <div className="h-auto pt-10">
      <div className="text-2xl font-bold text-text1 flex flex-col items-center justify-center">
        <div className="flex items-center py-2">
          <Gem className="mr-2" /> <span className="text-3xl font-bold text-text1">Learn from Acting Pros</span>
        </div>
        <p className="text-sm font-normal w-[60%] text-center">Behind every great performance is an exceptional coach. Our platform connects you with experienced acting professionals who can guide you, challenge you, and help you master the art of acting.</p>
      </div>
      <div className="container grid grid-cols-7 max-md:grid-cols-2 text-center py-10">
        {[1, 2].map(coach => (
          <Profile key={coach} image={coach % 2 == 0 ? Profile2Img : ProfileImg} name={coach % 2 == 0 ? "Emily" : "John Wik"} desc="Pop Music Coach" />
        ))}
      </div>
    </div>
  );
};

export default CoacheSection;
