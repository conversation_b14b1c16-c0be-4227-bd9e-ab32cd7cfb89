import supabase from "@/lib/supabase";
import { createSlice } from "@reduxjs/toolkit";

export const fetchAccessToken = async () => {
  const { data, error } = await supabase.auth.getSession();
  if (error) throw new Error(error.message);
  return data.session.access_token;
};

const chatSlice = createSlice({
  name: "chats",
  initialState: {
    chats: [],
    messages: {},
    onlineUsers: {},
    currentChat: null,
  },
  reducers: {
    setMessages: (state, action) => {
      const { chatId, messages } = action.payload;
      state.messages[chatId] = messages;
    },
    addMessage: (state, action) => {
      const { chatId, message } = action.payload;
      if (!state.messages[chatId]) {
        state.messages[chatId] = [];
      }
      // Check if the message already exists to prevent duplication
      const messageExists = state.messages[chatId].some(
        (msg) => msg.id === message.id
      );
      if (!messageExists) {
        state.messages[chatId].push(message);
      }
    },
    updateMessage: (state, action) => {
      const { chatId, messageId, updates } = action.payload;
      const messageIndex = state.messages[chatId].findIndex(
        (msg) => msg.id === messageId
      );
      if (messageIndex !== -1) {
        state.messages[chatId][messageIndex] = {
          ...state.messages[chatId][messageIndex],
          ...updates,
        };
      }
    },
    removeMessage: (state, action) => {
      const { chatId, messageId } = action.payload;
      state.messages[chatId] = state.messages[chatId].filter(
        (msg) => msg.id !== messageId
      );
    },
    markMessageAsSeen: (state, action) => {
      const { chatId, messageId } = action.payload;
      const messageIndex = state.messages[chatId].findIndex(
        (msg) => msg.id === messageId
      );
      if (messageIndex !== -1) {
        state.messages[chatId][messageIndex].seen = true;
      }
    },
    updateOnlineStatus: (state, action) => {
      state.onlineUsers = { ...state.onlineUsers, ...action.payload };
    },
    updateChatVisibility: (state, action) => {
      const { chatId, userId, isVisible } = action.payload;
      const chatIndex = state.chats.findIndex((chat) => chat.id === chatId);
      if (chatIndex !== -1) {
        if (userId === state.chats[chatIndex].user_1) {
          state.chats[chatIndex].deleted_by_user_1 = !isVisible;
        } else {
          state.chats[chatIndex].deleted_by_user_2 = !isVisible;
        }
      }
    },
    setCurrentChat: (state, action) => {
      state.currentChat = action.payload;
    },
  },
});

export const {
  setMessages,
  addMessage,
  updateMessage,
  removeMessage,
  markMessageAsSeen,
  updateOnlineStatus,
  updateChatVisibility,
  setCurrentChat,
} = chatSlice.actions;

export default chatSlice.reducer;
