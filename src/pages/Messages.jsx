import React, { useState } from "react";
import { useParams } from "react-router-dom";
import MessagesList from "@/components/shared/Messages/MessagesList";
import ScreenTopSection from "@/components/ScreenTopSection";
import { Plus, X } from "lucide-react";
import SearchUsersForStartingChat from "@/components/shared/Messages/SearchUsersForStartingChat";
import { motion, AnimatePresence } from "framer-motion";
import ChatSection from "@/components/shared/Messages/ChatSection";

const Messages = () => {
  const { chatId } = useParams();
  const [showSearch, setShowSearch] = useState(false);

  const toggleSearch = () => {
    setShowSearch((prevState) => !prevState);
  };

  return (
    <div className="flex flex-col justify-center items-center w-full">
      <div className="lg:w-[52%] w-full">
        <ScreenTopSection
          page="Messages"
          btnText={showSearch ? "Close" : "New Chat"}
          Icon={showSearch ? X : Plus}
          onClick={toggleSearch}
        />
        <div className="flex">
          <div className={`${chatId ? "lg:w-1/3" : "w-full"} flex flex-col`}>
            <AnimatePresence>
              {showSearch && (
                <motion.div
                  initial={{ opacity: 0, height: 0 }}
                  animate={{ opacity: 1, height: "auto" }}
                  exit={{ opacity: 0, height: 0 }}
                  transition={{ duration: 0.3 }}
                  className="w-full mb-4"
                >
                  <SearchUsersForStartingChat setShowSearch={setShowSearch} />
                </motion.div>
              )}
            </AnimatePresence>
            <MessagesList />
          </div>
          {chatId && (
            <div className="lg:w-2/3 w-full">
              <ChatSection />
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default Messages;
