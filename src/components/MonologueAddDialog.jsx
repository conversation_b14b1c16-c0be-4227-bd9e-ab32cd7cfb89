import { useEffect } from "react";
import { <PERSON><PERSON>, DialogContent, DialogTitle } from "@/components/ui/dialog";
import { useForm, Controller } from "react-hook-form";
import { Button } from "@/components/ui/button";
import { X } from "lucide-react";
import { Textarea } from "./ui/textarea";
import {
    Card,
    CardContent
  } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
    Select,
    SelectContent,
    SelectItem,
    SelectTrigger,
    SelectValue,
  } from "@/components/ui/select";
import { useInsertMonologueMutation } from "@/redux/features/postSlice";
import { useToast } from "@/components/ui/use-toast";
// eslint-disable-next-line react/prop-types
const MonologueAddDialog = ({ isOpen, onClose}) => {

    const { toast } = useToast();

    const {
        control,
        register,
        handleSubmit,
        reset,
        formState: { errors },
    } = useForm({
        defaultValues: {
            title: "",
            character: "",
            type: "",
            gender: "",
            monologue: ""
        },
    });

    useEffect(()=>{
        reset();
    },[reset]);
    
    const [insertMonologue, { isLoading }] = useInsertMonologueMutation();

    const handleOnClose = (event) => {
        event.preventDefault(); // Prevents backdrop click from closing the dialog
        onClose();
    };

    const onSubmit = async(data) => {
        try{
            const postData = [{
                title: data.title,
                character: data.character,
                category: data.type,
                gender: data.gender,
                content: data.monologue
            }];
            const resData = await insertMonologue(postData).unwrap();
            if(resData && resData.status == 200){
                toast({
                    title: "Monologues",
                    description: "Your CSV monologues has been successfully submitted!",
                    variant: "success",
                });
            }else{
                toast({
                    title: "Monologue",
                    description: "Something went wrong!",
                    variant: "failed",
                });
            }
        }catch(err){
            console.error("Failed to apply:", err);
            toast({
                title: "Submission Error",
                description:
                err.data?.message ||
                "An error occurred while submitting your application.",
                variant: "destructive",
            });
        }
        reset();
        onClose();
    };

    return (
        <Dialog open={isOpen} onOpenChange={(open) => open && onClose()}>
            <DialogContent className="max-w-3xl bg-primary1 text-text1 border-text2 p-5 discord-scrollbar">
                <DialogTitle>Add Monologue</DialogTitle>
                    <div className=" bg-primary1 text-text1 flex items-center justify-center p-4">
                        <Card className="w-full bg-secondary2 backdrop-blur-md max-w-6xl border-border1 shadow-lg rounded-xl">
                            <CardContent className="block gap-2">
                                <form onSubmit={handleSubmit(onSubmit)}>
                                    <div className="w-full space-y-4">
                                        <div className="space-y-2">
                                            <Label
                                                htmlFor="title"
                                                className="text-lg font-medium block text-text1"
                                            >
                                                Title
                                            </Label>
                                            <Input
                                                id="title"
                                                type="text"
                                                placeholder="Title"
                                                className="bg-white/5 border-border1 text-text1 rounded-lg focus:ring-2 focus:ring-hover1 transition-all placeholder:text-text2"
                                                {...register("title", { required: "Title is required" })}
                                            />
                                            {errors.title && (
                                                <p className="text-close text-sm mt-1">
                                                    {errors.title.message}
                                                </p>
                                            )}
                                        </div>
                                        <div className="space-y-2">
                                            <Label
                                                htmlFor="character"
                                                className="text-lg font-medium block text-text1"
                                            >
                                                Character
                                            </Label>
                                            <Input
                                                id="character"
                                                type="text"
                                                placeholder="Character"
                                                className="bg-white/5 border-border1 text-text1 rounded-lg focus:ring-2 focus:ring-hover1 transition-all placeholder:text-text2"
                                                {...register("character", { required: "Character is required" })}
                                            />
                                            {errors.character && (
                                                <p className="text-close text-sm mt-1">
                                                    {errors.character.message}
                                                </p>
                                            )}
                                        </div>
                                        <div className="space-y-2  ">
                                            <Label
                                                htmlFor="type"
                                                className="text-lg font-medium block text-text1"
                                            >
                                            Type
                                            </Label>
                                            <Controller
                                                name="type"
                                                control={control}
                                                render={({ field }) => (
                                                    <Select 
                                                        onValueChange={field.onChange} 
                                                        value={field.value}
                                                    >
                                                        <SelectTrigger className="bg-white/5 border-border1 text-text1 rounded-lg focus:ring-0 focus:ring-hover1 transition-all">
                                                            <SelectValue placeholder="Select type" />
                                                        </SelectTrigger>
                                                        <SelectContent className="bg-otherBtn border-border1 text-text1">
                                                            <SelectItem value="comic">Comin</SelectItem>
                                                            <SelectItem value="Female">Dramatic</SelectItem>
                                                        </SelectContent>
                                                    </Select>
                                                )}
                                                {...register("type", { required: "Type is required" })}
                                            />
                                            {errors.type && (
                                                <p className="text-close text-sm mt-1">
                                                    {errors.type.message}
                                                </p>
                                            )}
                                        </div>
                                        <div className="space-y-2">
                                            <Label
                                                htmlFor="gender"
                                                className="text-lg font-medium block text-text1"
                                            >
                                            Gender
                                            </Label>
                                            <Controller
                                                name="gender"
                                                control={control}
                                                render={({ field }) => (
                                                    <Select 
                                                        onValueChange={field.onChange} 
                                                        value={field.value}
                                                    >
                                                        <SelectTrigger className="bg-white/5 border-border1 text-text1 rounded-lg focus:ring-0 focus:ring-hover1 transition-all">
                                                            <SelectValue placeholder="Select gender" />
                                                        </SelectTrigger>
                                                        <SelectContent className="bg-otherBtn border-border1 text-text1">
                                                            <SelectItem value="Male">Male</SelectItem>
                                                            <SelectItem value="Female">Female</SelectItem>
                                                            <SelectItem value="Other">Other</SelectItem>
                                                        </SelectContent>
                                                    </Select>
                                                )}
                                                {...register("gender", { required: "Gender is required" })}
                                            />
                                            {errors.gender && (
                                                <p className="text-close text-sm mt-1">
                                                    {errors.gender.message}
                                                </p>
                                            )}
                                        </div>
                                        <div className="space-y-2 ">
                                            <Label
                                                htmlFor="monologue"
                                                className="text-lg font-medium block text-text1"
                                            >
                                                Monologue
                                            </Label>
                                            <Textarea
                                                className="flex text-white min-h-[80px] border-input text-sm ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 resize-none w-full mt-4 bg-secondary2 border rounded-lg px-4 py-2 placeholder:text-text2"
                                                rows={5}
                                                name="monologue"
                                                {...register("monologue", { required: "Monologue is required" })}
                                            />
                                            {errors.monologue && (
                                                <p className="text-close text-sm mt-1">
                                                    {errors.monologue.message}
                                                </p>
                                            )}
                                        </div>
                                    </div>
                                    <div className="flex justify-end mt-2 gap-2">
                                        <Button
                                            className="bg-close shadow-none hover:bg-close/80"
                                            onClick={handleOnClose}
                                            disabled={isLoading}
                                        >
                                            <span className="flex gap-2 items-center">
                                            <X size={16} /> Close
                                            </span>
                                        </Button>
                                        <Button 
                                            type="submit"
                                            disabled={isLoading}
                                        >
                                            Save
                                        </Button>
                                    </div>
                                </form>
                            </CardContent>
                        </Card>
                    </div>
            </DialogContent>
        </Dialog>
    );
};

export default MonologueAddDialog;
