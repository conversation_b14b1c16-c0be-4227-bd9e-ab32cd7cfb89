export enum USER_ROLE {
  NORMAL_USER = 'normal_user',
  COACH = 'coach',
  ADMIN = 'admin',
}

export enum COACH_STATUS {
  'Available' = 'AVAILABLE',
  'Unavailable' = 'UNAVAILABLE',
  'available' = 'AVAILABLE',
  'unavailable' = 'UNAVAILABLE',
  'AVAILABLE' = 'AVAILABLE',
  'UNAVAIL<PERSON><PERSON>' = 'UNAVAILABLE',
}

export enum COACH_PLANS {
  COACH_PREMIUM = 'Coach Premium Plan',
}

export enum USER_PLANS {
  USER_PREMIUM = 'Pro User',
}

export enum PLANS_TYPE {
  USER_PLAN = 1,
  COACH_PLAN,
}

export enum PLAN_STATUS {
  INACTIVE,
  ACTIVE,
}

export enum COACH_APPLICATION_STATUS {
  ACCEPTED = 'Accepted',
  PENDING = 'Pending',
  REJECTED = 'Rejected',
  REMOVED = 'Removed',
}
