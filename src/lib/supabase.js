import { createClient } from "@supabase/supabase-js";

const supabase = createClient(
  import.meta.env.VITE_SUPABASE_URL,
  import.meta.env.VITE_SUPABASE_ANON,
  {
    realtime: {
      params: {
        eventsPerSecond: 10,
      },
    },
  }
);

export const setupPresence = (userId, callback) => {
  const channel = supabase.channel("online-users", {
    config: {
      presence: {
        key: userId,
      },
    },
  });

  channel
    .on("presence", { event: "sync" }, () => {
      const newState = channel.presenceState();
      callback(newState);
    })
    .on("presence", { event: "join" }, ({ key, newPresences }) => {
      callback({ [key]: newPresences[0] });
    })
    .on("presence", { event: "leave" }, ({ key }) => {
      callback({ [key]: null });
    })
    .subscribe(async (status) => {
      if (status === "SUBSCRIBED") {
        await channel.track({ online_at: new Date().toISOString() });
      }
    });

  return channel;
};
export const subscribeToChats = (callback) => {
  return supabase
    .channel("public:chats")
    .on(
      "postgres_changes",
      { event: "*", schema: "public", table: "chats" },
      (payload) => callback(payload)
    )
    .subscribe();
};
export const subscribeToMessages = (chatId, callback) => {
  return supabase
    .channel(`messages:chat_id=eq.${chatId}`)
    .on(
      "postgres_changes",
      {
        event: "INSERT",
        schema: "public",
        table: "messages",
        filter: `chat_id=eq.${chatId}`,
      },
      (payload) => callback(payload)
    )
    .subscribe();


};

// Subscribe to post analysis status changes
export const subscribeToPostAnalysis = (postId, callback) => {
  console.log('Setting up Supabase subscription for post_id:', postId, 'type:', typeof postId);

  // Test with no filter first to see if we get ANY updates
  const channel = supabase
    .channel(`post-analysis:${postId}`)
    .on(
      "postgres_changes",
      {
        event: "UPDATE",
        schema: "public",
        table: "posts",
        // Temporarily remove filter to test if we get any updates
      },
      (payload) => {
        console.log('🔥 ANY POST UPDATE received:', {
          post_id: payload.new.post_id,
          target_post_id: postId,
          is_target_post: payload.new.post_id == postId,
          old_status: payload.old?.analysis_status,
          new_status: payload.new.analysis_status,
          old_insights: payload.old?.ai_insights ? 'has_data' : 'null',
          new_insights: payload.new.ai_insights ? 'has_data' : 'null'
        });

        // Only trigger callback if this is the post we care about
        if (payload.new.post_id == postId) {
          console.log('🎯 This is our target post! Triggering callback');
          callback(payload);
        } else {
          console.log('📝 Different post, ignoring');
        }
      }
    )
    .subscribe((status) => {
      console.log('Supabase subscription status for post', postId, ':', status);
    });

  return channel;
};


export default supabase;