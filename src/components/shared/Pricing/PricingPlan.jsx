import { Button } from "@/components/ui/button";
import {
  useBuyPlanMutation,
  useCancelPlanMutation,
} from "@/redux/features/planApi";
import { CircleCheckBig } from "lucide-react";
import { useState } from "react";

const PricingPlan = ({
  isActive,
  title,
  price,
  features,
  onCancelSuccess,
  plan_id,
  onError,
}) => {
  const [cancelPlan, { isLoading: isCancelling }] = useCancelPlanMutation();
  const [buyPlan, { isLoading: isBuying }] = useBuyPlanMutation();

  const handleBuyPlan = async () => {
    if (!plan_id) {
      onError("Cannot upgrade to this plan. Please try another.");
      return;
    }
    try {
      const response = await buyPlan(plan_id).unwrap();
      // Redirect to Stripe checkout
      window.location.href = response.data;
    } catch (error) {
      onError("Failed to initiate plan purchase. Please try again.");
    }
  };

  const handleCancelPlan = async () => {
    try {
      await cancelPlan().unwrap();
      if (onCancelSuccess) {
        onCancelSuccess();
      }
    } catch (error) {
      onError("Failed to cancel plan. Please try again.");
    }
  };

  // const isFreeplan = price === "0";
  const isFreeplan = false;

  return (
    <div
      className={`rounded-lg p-8 flex flex-col h-full ${
        isActive ? "bg-navItemActive" : "bg-[#343434]"
      }`}
    >
      <div className="flex justify-between items-center mb-4">
        <h3
          className={`text-xl font-bold ${
            isActive ? "text-white" : "text-[#FAFAFA]"
          }`}
        >
          {title}
        </h3>
        {isActive && (
          <span className="text-xs bg-white text-navItemActive px-2 py-1 rounded">
            active
          </span>
        )}
      </div>
      <p
        className={`text-3xl font-bold mb-6 ${
          isActive ? "text-white" : "text-[#FAFAFA]"
        }`}
      >
        ${price}/mo
      </p>
      <ul className="flex-grow mb-6">
        {features.map((feature, index) => (
          <li
            key={index}
            className={`mb-3 flex items-center ${
              isActive ? "text-white" : "text-white"
            }`}
          >
            <CircleCheckBig className="w-5 h-5 mr-3 flex-shrink-0 mt-0.5" />
            {feature}
          </li>
        ))}
      </ul>

      {isFreeplan ? (
        isActive ? (
          <Button
            disabled
            className="w-full py-3 rounded-lg font-semibold bg-[#383481] text-white cursor-not-allowed"
          >
            Current Plan
          </Button>
        ) : (
          <Button
            disabled
            className="w-full py-3 rounded-lg font-semibold bg-[#383481] text-white cursor-not-allowed"
          >
            Default Plan
          </Button>
        )
      ) : (
        <Button
          onClick={isActive ? handleCancelPlan : handleBuyPlan}
          disabled={(isActive && isCancelling) || (!isActive && isBuying)}
          className={`w-full py-3 rounded-lg font-semibold ${
            isActive
              ? "bg-white text-primary1 hover:bg-[#383481] hover:text-white"
              : "bg-navItemActive text-white"
          }`}
        >
          {isActive
            ? isCancelling
              ? "Cancelling..."
              : "Cancel"
            : isBuying
            ? "Processing..."
            : "Upgrade"}
        </Button>
      )}
    </div>
  );
};

export default PricingPlan;
