import { Button } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { useToast } from "@/components/ui/use-toast";
import supabase from "@/lib/supabase";
import { Label } from "@/components/ui/label";
import { EyeIcon, EyeOff } from "lucide-react";
import { useState, useEffect } from "react";
import { useForm } from "react-hook-form";
import { useNavigate } from "react-router-dom";
 
const UpdatePassword = () => {
  const {
    register,
    handleSubmit,
    formState: { errors },
    watch,
  } = useForm({
    mode: "onChange",
    defaultValues: {
      password: "",
      confirmPassword: "",
    },
  });
  const { toast } = useToast();
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  const [isFormComplete, setIsFormComplete] = useState(false);
  const navigate = useNavigate();
 
  const togglePasswordVisibility = () => {
    setShowPassword(!showPassword);
  };
 
  const toggleConfirmPasswordVisibility = () => {
    setShowConfirmPassword(!showConfirmPassword);
  };
 
  const formValues = watch();
    useEffect(() => {
      const isAllFieldsFilled =
        formValues.password &&
        formValues.confirmPassword &&
        Object.keys(errors).length === 0;
 
      setIsFormComplete(isAllFieldsFilled);
    }, [formValues, errors]);
 
   const handleUpdatePassword = async (data) => {
    const urlParams = new URLSearchParams(window.location.search);
    const accessToken = urlParams.get("access_token");
    const email = urlParams.get("email");
 
    if (data.password !== data.confirmPassword) {
       toast({ title: "Passwords do not match!", variant: "destructive", });
      return;
    }
 
    if (!accessToken || !email) {
      toast({ title: "No reset token or email found. Redirecting to login.", variant: "destructive", });
      console.error("No reset token or email found. Redirecting to login.");
      navigate("/signin");
      return;
    }
 
    const { error: verifyError } = await supabase.auth.verifyOtp({
      type: "recovery",
      token: accessToken,
      email: email,
    });
 
    if (verifyError) {
      toast({ title: `Invalid or expired token: ${verifyError.message}`, variant: "destructive", });
      navigate("/signin");
      return;
    }
    const newPassword = data.password;
    const { error: updateError } = await supabase.auth.updateUser({
      password: newPassword,
    });
 
    if (updateError) {
      toast({ title: `Failed to update password: ${updateError.message}`, variant: "destructive", });
      return;
    }
 
    toast({ title: "Password updated successfully!" });
    navigate("/signin");
 
  };
 
 
  return (
    <section className="h-screen flex justify-center items-center bg-primary1">
      <Card className="mx-auto max-w-sm md:min-w-[400px] bg-secondary2 border-border1">
        <CardHeader>
          <CardTitle className="text-xl text-text1">Update Password</CardTitle>
          <CardDescription className="text-text2">
            Please update your password
          </CardDescription>
        </CardHeader>
 
        <CardContent>
          <form
            onSubmit={handleSubmit(handleUpdatePassword)}
            className="grid gap-4"
          >
            <div className="grid gap-2">
              <Label htmlFor="password" className="text-text1">
                New Password
              </Label>
              <div className="relative">
                <Input
                  id="password"
                  type={showPassword ? "text" : "password"}
                  {...register("password", {
                    required: "Password is required",
                    minLength: {
                      value: 8,
                      message: "Password must be at least 8 characters long",
                    },
                    pattern: {
                      value:
                        /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]{8,}$/,
                      message:
                        "Password must contain at least one uppercase letter, one lowercase letter, one number, and one special character",
                    },
                  })}
                  className="bg-otherBtn text-text1 border-border1 focus:border-hover1 pr-10"
                />
                <button
                  type="button"
                  onClick={togglePasswordVisibility}
                  className="absolute inset-y-0 right-0 flex items-center px-2 text-text2"
                >
                  {showPassword ? <EyeIcon size={18} /> : <EyeOff size={18} />}
                </button>
              </div>
              {errors.password && (
                <p className="text-close">{errors.password.message}</p>
              )}
            </div>
            <div className="grid gap-2">
              <Label htmlFor="confirmPassword" className="text-text1">
                Confirm New Password
              </Label>
              <div className="relative">
                <Input
                  id="confirmPassword"
                  type={showConfirmPassword ? "text" : "password"}
                  {...register("confirmPassword", {
                    required: "Confirm Password is required",
                  })}
                  className="bg-otherBtn text-text1 border-border1 focus:border-hover1 pr-10"
                />
                <button
                  type="button"
                  onClick={toggleConfirmPasswordVisibility}
                  className="absolute inset-y-0 right-0 flex items-center px-2 text-text2"
                >
                  {showConfirmPassword ? (
                    <EyeIcon size={18} />
                  ) : (
                    <EyeOff size={18} />
                  )}
                </button>
              </div>
              {errors.confirmPassword && (
                <p className="text-close">{errors.confirmPassword.message}</p>
              )}
            </div>
 
            <Button
              type="submit"
              className="w-full bg-hover1 hover:bg-navItemActive text-text1"
            >
              Update Password
            </Button>
          </form>
        </CardContent>
      </Card>
    </section>
  );
};
 
export default UpdatePassword;