import {
  CanActivate,
  ExecutionContext,
  HttpException,
  HttpStatus,
  Injectable,
} from '@nestjs/common';
import { Reflector } from '@nestjs/core';
import { COACH_PLANS, USER_PLANS } from '../../types/users.enum';
import { PLANS_KEY } from '../decorators/plans.decorator';

@Injectable()
export class PlansGuard implements CanActivate {
  constructor(private reflector: Reflector) {}

  async canActivate(context: ExecutionContext): Promise<any> {
    // get required plams from metadata of request (injected by plans decorator)
    const requiredPlans = this.reflector.getAllAndOverride<
      USER_PLANS[] | COACH_PLANS[]
    >(PLANS_KEY, [context.getHandler(), context.getClass()]);
    if (!requiredPlans) {
      return true;
    }
    const { user } = context.switchToHttp().getRequest();
    if (
      requiredPlans.some((plan) =>
        user.public_user_data[0]?.plan_in_use?.plan_name.includes(plan),
      )
    ) {
      return true;
    } else {
      throw new HttpException(
        'You need to be subscribed to the pro plan to use this feature.',
        HttpStatus.UNAUTHORIZED,
      );
    }
  }
}
