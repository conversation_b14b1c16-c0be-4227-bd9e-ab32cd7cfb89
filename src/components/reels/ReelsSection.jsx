import { Scroll<PERSON><PERSON>, <PERSON><PERSON>B<PERSON> } from "@/components/ui/scroll-area";
import videoSample2 from "../../assets/videos/beautiful nature.mp4";

export const reels = [
  {
    reelsID: "1",
    reelsSrc: videoSample2,
  },
  {
    reelsID: "2",
    reelsSrc: videoSample2,
  },
  {
    reelsID: "3",
    reelsSrc: videoSample2,
  },
];

const ReelsSection = () => {
  return (
    <div className="container mx-auto px-4 my-8">
      <h2 className="text-lg md:text-xl lg:text-2xl font-semibold mb-4">
        Reels
      </h2>
      <ScrollArea className="w-full whitespace-nowrap rounded-md border border-gray-700">
        <div className="flex w-max space-x-4 p-4">
          {reels.map((item) => (
            <figure key={item.reelsID} className="shrink-0">
              <div className="overflow-hidden">
                <video
                  className="w-48 sm:w-60 md:w-72 lg:w-80 xl:w-96 rounded-md object-cover"
                  controls
                  autoPlay
                  muted
                  loop
                >
                  <source src={item.reelsSrc} type="video/mp4" />
                  Your browser does not support the video tag.
                </video>
              </div>
            </figure>
          ))}
        </div>
        <ScrollBar orientation="horizontal" />
      </ScrollArea>
    </div>
  );
};

export default ReelsSection;
