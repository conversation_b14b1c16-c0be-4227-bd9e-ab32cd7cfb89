import { createApi, fetchBaseQuery } from "@reduxjs/toolkit/query/react";
import { fetchAccessToken } from "./chatSlice";

const chatApi = createApi({
  reducerPath: "chatApi",
  baseQuery: fetchBaseQuery({
    baseUrl: import.meta.env.VITE_API_BASE_URL,
    prepareHeaders: async (headers, { getState, endpoint }) => {
      if (endpoint != 'getBotResponse') {
        const state = getState();
        const accessToken = await fetchAccessToken(state);
        headers.set("Authorization", `Bearer ${accessToken}`);
      }
      return headers;
    },
  }),
  keepUnusedDataFor: 600,
  tagTypes: ["Chats", "Messages", "Users"],
  endpoints: (builder) => ({
    getChats: builder.query({
      query: () => "chats",
      providesTags: ["Chats"],
      transformResponse: (response) => response.data,
    }),
    getChatById: builder.query({
      query: ({ chatId, page_no }) => `chats/${chatId}?page_no=${page_no}`,
      providesTags: (result, error, arg) => [
        { type: "Messages", id: arg.chatId },
      ],
      //       query: (chatId) => `chats/${chatId}`,
      // providesTags: (result, error, arg) => [{ type: "Messages", id: arg }],
      transformResponse: (response) => response.data,
    }),
    createChat: builder.mutation({
      query: (userId) => ({
        url: "chats/create_chat",
        method: "POST",
        body: { user_id: userId },
      }),
      invalidatesTags: ["Chats"],
    }),
    deleteChat: builder.mutation({
      query: ({ chatId, userId }) => ({
        url: `chats/delete_chat/${chatId}`,
        method: "DELETE",
        body: { user_id: userId },
      }),
      invalidatesTags: (result, error, { chatId }) => [
        { type: "Chats", id: chatId },
      ],
    }),

    searchUsers: builder.query({
      query: (searchQuery) =>
        `profile/by_query?search=${encodeURIComponent(searchQuery)}`,
      providesTags: ["Users"],
    }),
    getBotResponse: builder.mutation({
      query: ({ message, conv_history }) => ({
        url: "bot/question",
        method: "POST",
        body: { question: message, conversation_history: [...conv_history] },
        responseHandler: "text",
      }),
    }),
  }),
});

export const {
  useGetChatsQuery,
  useGetChatByIdQuery,
  useCreateChatMutation,
  useDeleteChatMutation,
  useSearchUsersQuery,
  useGetBotResponseMutation,
} = chatApi;

export default chatApi;
