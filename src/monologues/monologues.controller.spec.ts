import { Test, TestingModule } from '@nestjs/testing';
import { MonologuesController } from './monologues.controller';
import { MonologuesService } from './monologues.service';

describe('MonologuesController', () => {
  let controller: MonologuesController;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [MonologuesController],
      providers: [MonologuesService],
    }).compile();

    controller = module.get<MonologuesController>(MonologuesController);
  });

  it('should be defined', () => {
    expect(controller).toBeDefined();
  });
});
