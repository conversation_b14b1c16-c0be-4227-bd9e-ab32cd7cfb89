import { HttpStatus, Injectable } from '@nestjs/common';
import { PrismaClient } from '@prisma/client';
import { DatabaseService } from '../database/database.service';
import { CustomResponse } from '../utils/CustomResponse';
import { SendMessageDto } from './dto/send-message-dto';
import { MarkasReadDto, OnlineStatusDto } from './dto/mark-read-dto';
import { SupabaseClient } from '@supabase/supabase-js';

@Injectable()
export class ChatsService {
  private prisma: PrismaClient;
  private supabase: SupabaseClient;

  constructor(private readonly dbService: DatabaseService) {
    this.prisma = dbService.getClient();
    this.supabase = dbService.getSupabaseClient();
  }

  async getChats(user_id: string): Promise<CustomResponse> {
    try {
      const chats = await this.prisma.chats.findMany({
        where: {
          OR: [{ user_1: user_id }, { user_2: user_id }], // find any chats which involve the fetching user
        },
        include: {
          users_chats_user_1Tousers: {
            include: {
              users: true,
            },
          },
          users_chats_user_2Tousers: {
            include: {
              users: true,
            },
          },
          messages: {
            select: {
              content: true,
              created_at: true,
            },
            orderBy: {
              created_at: 'desc',
            },
            take: 1,
          },
        },
      });

      // filter out chats deleted by user and remove extra info
      const filteredChats = chats.reduce((result, chat) => {
        let isUser1 = chat.user_1 === user_id;
        let isUser2 = chat.user_2 === user_id;

        if (
          (isUser1 && !chat.deleted_by_user_1) ||
          (isUser2 && !chat.deleted_by_user_2)
        ) {
          const latestMessage =
            chat.messages.length > 0 ? chat.messages[0] : null;
          const userMetaData = isUser1
            ? chat.users_chats_user_2Tousers.raw_user_meta_data
            : chat.users_chats_user_1Tousers.raw_user_meta_data;
          const userProfile = isUser1
            ? chat.users_chats_user_2Tousers.users[0]
            : chat.users_chats_user_1Tousers.users[0];

          result.push({
            latest_message: latestMessage
              ? {
                  message: latestMessage.content,
                  sent_at: latestMessage.created_at,
                }
              : {},
            id: chat.id,
            user_1: chat.user_1,
            user_2: chat.user_2,
            user_data: {
              full_name: userMetaData['full_name'],
              profile_picture_url: userProfile.profile_picture_url,
              id: isUser1 ? chat.user_2 : chat.user_1,
            },
          });
        }
        return result;
      }, []);

      return {
        data: filteredChats,
        error: false,
        message: 'Chats fetched',
        status: HttpStatus.OK,
      };
    } catch (err) {
      console.log(err);
      return {
        data: null,
        error: true,
        message: 'Error fetching chats',
        status: HttpStatus.INTERNAL_SERVER_ERROR,
      };
    }
  }

  async createChat(
    creator_id: string,
    other_user_id: string,
  ): Promise<CustomResponse> {
    try {
      const existing_chat = await this.prisma.chats.findFirst({
        where: {
          OR: [
            {
              user_1: creator_id,
              user_2: other_user_id,
            },
            {
              user_2: creator_id,
              user_1: other_user_id,
            },
          ],
        },
      });
      if (existing_chat) {
        return {
          data: existing_chat,
          error: false,
          message: 'Chat created',
          status: HttpStatus.OK,
        };
      }

      const chat_create = await this.prisma.chats.create({
        data: {
          user_1: creator_id,
          user_2: other_user_id,
        },
      });

      return {
        data: chat_create,
        error: false,
        message: 'Chat created',
        status: HttpStatus.OK,
      };
    } catch (err) {
      console.log(err);
      return {
        data: null,
        error: true,
        message: 'Error creating chat',
        status: HttpStatus.INTERNAL_SERVER_ERROR,
      };
    }
  }

  async deleteChat(user_id: string, chat_id: number): Promise<CustomResponse> {
    try {
      const chat_fetch = await this.supabase
        .from('chats')
        .select('*')
        .eq('id', chat_id);

      // make sure user is one of the users involved in chat
      if (
        chat_fetch.data[0].user_1 != user_id &&
        chat_fetch.data[0].user_2 != user_id
      ) {
        return {
          data: null,
          error: true,
          message: 'Chat cannot be deleted',
          status: HttpStatus.BAD_REQUEST,
        };
      }

      // if one of the users deletes chat, the other users chat shouldn't be deleted
      // so mark it as deleted for the user and use a trigger to delete chats where
      // both users have marked them as deleted
      let update = {};
      chat_fetch.data[0].user_1 == user_id
        ? (update = { deleted_by_user_1: true })
        : (update = { deleted_by_user_2: true });

      const update_resp = await this.supabase
        .from('chats')
        .update(update)
        .eq('id', chat_id);

      let message_update = {};
      chat_fetch.data[0].user_1 == user_id
        ? (message_update = { visible_to_user_1: false })
        : (message_update = { visible_to_user_2: false });

      await this.supabase
        .from('messages')
        .update(message_update)
        .eq('chat_id', chat_id);

      return {
        data: update_resp.data,
        error: false,
        message: 'Chat deleted',
        status: HttpStatus.OK,
      };
    } catch (err) {
      console.log(err);
      return {
        data: null,
        error: true,
        message: 'Error deleting chat',
        status: HttpStatus.INTERNAL_SERVER_ERROR,
      };
    }
  }

  async sendMessage(sendMessageDto: SendMessageDto): Promise<CustomResponse> {
    try {
      const user = (
        await this.dbService.getUserbyToken(sendMessageDto.sender_jwt)
      ).data.user.id;
      const chat = await this.prisma.chats.findFirstOrThrow({
        // make sure this user is allowed to insert message in this chat
        where: {
          id: sendMessageDto.chat_id,
          OR: [
            {
              user_1: user,
            },
            {
              user_2: user,
            },
          ],
        },
      });
      if (chat.deleted_by_user_1 || chat.deleted_by_user_2) {
        await this.prisma.chats.updateMany({
          where: {
            id: chat.id,
          },
          data: {
            deleted_by_user_1: false,
            deleted_by_user_2: false,
          },
        });
      }
      await this.prisma.messages.create({
        data: {
          content: sendMessageDto.content,
          chat_id: sendMessageDto.chat_id,
          sender_id: user,
        },
      });
      return {
        data: null,
        error: false,
        message: 'Message sent',
        status: HttpStatus.OK,
      };
    } catch (err) {
      console.log(err);
      return {
        data: null,
        error: true,
        message: 'Error sending message',
        status: HttpStatus.INTERNAL_SERVER_ERROR,
      };
    }
  }

  async markAsRead(markasReadDto: MarkasReadDto): Promise<CustomResponse> {
    try {
      // to circumvent not having a websocket guard that automatically extracts
      // a token and injects user.
      const user = (
        await this.dbService.getUserbyToken(markasReadDto.sender_jwt)
      ).data.user.id;
      await this.prisma.messages.updateMany({
        where: {
          chat_id: markasReadDto.chat_id,
          sender_id: {
            not: user,
          },
        },
        data: {
          seen: true,
        },
      });
      return {
        data: null,
        error: false,
        message: 'Messages marked as read',
        status: HttpStatus.OK,
      };
    } catch (err) {
      console.log(err);
      return {
        data: null,
        error: true,
        message: 'Error reading messages',
        status: HttpStatus.INTERNAL_SERVER_ERROR,
      };
    }
  }

  async onlineStatus(
    onlineStatusDto: OnlineStatusDto,
  ): Promise<CustomResponse> {
    try {
      // find most recently updated user session, may not be very accurate measure of online status
      // a much more accurate approach would be to send heartbeats from the frontend and having a
      // dedicated online status in the db, but this would increase db/backend workload
      const last_session_update = await this.prisma.sessions.findFirst({
        where: {
          user_id: onlineStatusDto.user_id,
        },
        orderBy: {
          updated_at: 'desc',
        },
      });
      const sessionTime = new Date(last_session_update.updated_at);
      const currentTime = new Date(Date.now());
      const timeDifference = currentTime.getTime() - sessionTime.getTime();
      const differenceInHours = timeDifference / (1000 * 60 * 60);
      if (Math.abs(differenceInHours) <= 1) {
        return {
          data: null,
          error: false,
          message: 'User is online',
          status: HttpStatus.OK,
        };
      } else {
        console.log('Offline');
        return {
          data: null,
          error: true,
          message: 'User is offline',
          status: HttpStatus.OK,
        };
      }
    } catch (err) {
      return {
        data: null,
        error: true,
        message: 'Error reading messages',
        status: HttpStatus.INTERNAL_SERVER_ERROR,
      };
    }
  }
  async getChatMessages(
    user_id: string,
    chat_id: number,
    page_no: number,
  ): Promise<CustomResponse> {
    try {
      const messages = await this.prisma.messages.findMany({
        where: {
          chat_id,
          AND: [
            {
              OR: [
                {
                  chats: {
                    user_1: user_id,
                    deleted_by_user_1: false,
                  },
                },
                {
                  chats: {
                    user_2: user_id,
                    deleted_by_user_2: false,
                  },
                },
              ],
            },
            {
              OR: [
                {
                  visible_to_user_1: true,
                  chats: {
                    user_1: user_id,
                  },
                },
                {
                  visible_to_user_2: true,
                  chats: {
                    user_2: user_id,
                  },
                },
              ],
            },
          ],
        },
        orderBy: {
          created_at: 'desc',
        },
        skip: (page_no - 1) * 10,
        take: 20,
      });
      return {
        data: messages,
        error: false,
        message: 'Messages fetched',
        status: HttpStatus.OK,
      };
    } catch (err) {
      console.log(err);
      return {
        data: null,
        error: true,
        message: 'Error fetching messages',
        status: HttpStatus.INTERNAL_SERVER_ERROR,
      };
    }
  }
}
