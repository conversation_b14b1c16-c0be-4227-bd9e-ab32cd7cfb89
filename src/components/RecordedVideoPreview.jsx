import { useState, useEffect } from "react";
import { Upload } from "lucide-react";
import { Button } from "./ui/button";
import { Textarea } from "./ui/textarea";
import { useAddPostMutation } from "../redux/features/postSlice";
import { useNavigate } from "react-router-dom";
import { Label } from "./ui/label";
import { Switch } from "./ui/switch";
import { Skeleton } from "./ui/skeleton";

const RecordedVideoPreview = ({
  videoSrc,
  parentId,
  onUploadComplete,
}) => {
  const [description, setDescription] = useState("");
  const [isPrivate, setIsPrivate] = useState(false);
  const [addPost, { isLoading, error }] = useAddPostMutation();
  const [isVideoLoading, setIsVideoLoading] = useState(true);

  useEffect(() => {
    if (videoSrc) {
      const video = document.createElement("video");
      video.src = videoSrc;
      video.onloadeddata = () => {
        setIsVideoLoading(false);
      };
    }
  }, [videoSrc]);

  const handlePublish = async () => {
    if (!videoSrc) {
      alert("No video to publish");
      return;
    }
    try {
      const response = await fetch(videoSrc);
      const blob = await response.blob();
      const uniqueFilename = `recorded_video_${Date.now()}_${Math.random()
        .toString(36)
        .substr(2, 9)}.webm`;
      const file = new File([blob], uniqueFilename, { type: "video/webm" });
      const formData = new FormData();
      formData.append("content", description);
      formData.append("file", file);
      formData.append("is_private", isPrivate);
      if (parentId) {
        const parsedParentId = parseInt(parentId, 10);
        if (!isNaN(parsedParentId)) {
          formData.append("parent_id", parsedParentId);
        } else {
          console.error("Invalid parent_id:", parentId);
        }
      }
      const result = await addPost(formData).unwrap();
      if (result) {
        onUploadComplete(result);
      }
    } catch (err) {
      console.error("Failed to publish post:", err);
    }
  };

  return (
    <div className="container mx-auto p-4">
      <h1 className="text-2xl text-text1 mb-4">Video Preview</h1>
      <Textarea
        className="w-full max-w-md p-2 border bg-primary1 text-text1 rounded mb-4"
        placeholder="Enter video description..."
        value={description}
        onChange={(e) => setDescription(e.target.value)}
      />
      {isVideoLoading ? (
        <Skeleton className="w-full max-w-md h-64 mb-4" />
      ) : videoSrc ? (
        <video src={videoSrc} controls className="w-full max-w-md mb-4"></video>
      ) : (
        <p>No video recorded.</p>
      )}
      {videoSrc && !isVideoLoading && (
        <>
          <div className="flex gap-4">
            <Button onClick={handlePublish} disabled={isLoading}>
              <div className="flex gap-2">
                <Upload size={16} />
                {isLoading ? "Publishing..." : "Publish"}
              </div>
            </Button>
            <div className="flex items-center space-x-2">
              <Label htmlFor="private-post" className="text-text1">
                Private Post
              </Label>
              <Switch
                id="private-post"
                checked={isPrivate}
                onCheckedChange={setIsPrivate}
              />
            </div>
          </div>
        </>
      )}
      {error && (
        <p className="text-red-500 mt-2">
          {typeof error === "object" ? JSON.stringify(error) : error}
        </p>
      )}
    </div>
  );
};

export default RecordedVideoPreview;
