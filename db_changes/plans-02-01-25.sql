ALTER TABLE public.plans
ALTER COLUMN plan_price_monthly TYPE NUMERIC,
ALTER COLUMN plan_price_annual TYPE NUMERIC;

ALTER TABLE IF EXISTS public.plans
    ADD COLUMN type smallint DEFAULT 1;

ALTER TABLE IF EXISTS public.plans
    ADD COLUMN status smallint DEFAULT 1;

-- updated existing coach plan type to 2
UPDATE public.plans SET
type = '2'::smallint WHERE
plan_id = '21';

ALTER TABLE IF EXISTS public.plans
    ADD COLUMN expire_date date;