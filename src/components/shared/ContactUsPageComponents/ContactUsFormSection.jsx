import React, { useState } from "react";
import { MapPin, MoveRight } from "lucide-react";
import { Mail } from "lucide-react";
import { Phone } from "lucide-react";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Controller, useForm } from "react-hook-form";
import { useSubmitContactUsDataMutation } from "@/redux/features/contactUsSlice";

// import Images
import Earth2Img from '../../../assets/images/contact-form.png'
import { Button } from "@/components/ui/button";

const ContactUsFormSection = () => {
  const [SubmitContactUsData] = useSubmitContactUsDataMutation();
  const [successMessage, setSuccessMessage] = useState("");

  const { control, handleSubmit, formState: { errors }, reset } = useForm({
    defaultValues: {
      fullName: "",
      phone: "",
      email: "",
      message: "",
    },
  });

const onContactSubmit = async (data) => {
    try {
      const response = await SubmitContactUsData(data).unwrap();
      
      if (response.status === 200) {
        setSuccessMessage("Your form has been submitted successfully!");          
        reset();
        
        setTimeout(() => {
          setSuccessMessage("");
        }, 5000);
      } else {
        console.error("Validation failed: Unexpected response", response);
      }
      
    } catch (error) {
      console.error("Submission failed", error);
    }
};

//const onContactSubmit = (data) => {
  //console.log('form data', data);
 // SubmitContactUsData(data).unwrap();
//};

 return (
    <>
      <div className="bg-primary1 py-10 text-text1">
        <div className="flex justify-center items-center flex-col">
          <h1 className="p-5 text-3xl font-extrabold text-center flex justify-center items-center">Get in Touch with AuditionRooms</h1>
          <p className=" p-5 w-11/12 text-center">
            We're here to help your acting journey take center stage. Have a question, need assistance, or want to learn more? Reach out to us today!
          </p>
        </div>

        <div className="flex justify-center">
          <div className="w-[90%] rounded-3xl px-4  py-4 lg:py-16 lg:px-10  md:py-12  md:px-8   sm:px-8 sm:py-8   bg-gray2">
            <div className="flex flex-col sm:flex-row">
              <div className="w-full sm:w-[70%] lg:px-5 md:px-4  sm:px-3">
                <form onSubmit={handleSubmit(onContactSubmit)}>
                  <div className="py-2">
                    <p className="text-gray1">Full Name</p>
                    <Controller
                      name="fullName"
                      control={control}
                      rules={{ required: "Full Name is required" }}
                      render={({ field }) => (
                        <Input
                          {...field}
                          className="text-xs lg:text-sm text-text1 mt-2 bg-otherBtn border border-border1"
                        />
                      )}
                    />
                    {errors.fullName && (
                      <p className="text-red-500 text-sm py-2">
                        {errors.fullName.message}
                      </p>
                    )}
                  </div>
                  <div className="text-gray1 py-2">
                    <p className="">Phone</p>
                    <Controller
                      name="phone"
                      control={control}
                      rules={{ required: "Phone is required" }}
                      render={({ field }) => (
                        <Input
                          {...field}
                          className="text-xs lg:text-sm text-text1 mt-2 bg-otherBtn border border-border1"
                        />
                      )}
                    />
                    {errors.phone && (
                      <p className="text-red-500 text-sm py-2">
                        {errors.phone.message}
                      </p>
                    )}
                  </div>
                  <div className="text-gray1 py-2">
                    <p className="">Email</p>
                    <Controller
                      name="email"
                      control={control}
                      rules={{ 
                        required: "Email is required",
                        pattern: {
                          value: /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/,
                          message: "Enter a valid email address",
                        },
                      }}
                      render={({ field }) => (
                        <Input
                          {...field}
                          className="text-xs lg:text-sm text-text1 mt-2 bg-otherBtn border border-border1"
                        />
                      )}
                    />
                    {errors.email && (
                      <p className="text-red-500 text-sm py-2">
                        {errors.email.message}
                      </p>
                    )}
                  </div>
                  <div className="text-gray1 py-2">
                    <p className="pb-2">Message</p>
                    <Controller
                      name="message"
                      control={control}
                      rules={{ required: "Message is required" }}
                      render={({ field }) => (
                        <Textarea
                          {...field}
                          className="text-xs lg:text-sm text-text1 mt-2 bg-otherBtn border border-border1"
                          rows={5}
                          maxLength={500}
                        />
                      )}
                    />
                    {errors.message && (
                      <p className="text-red-500 text-sm py-2">
                        {errors.message.message}
                      </p>
                    )}
                  </div>
                  <div className="pt-8">
                    <Button
                      type="submit"
                      className="bg-navItemActive hover:bg-navItemActive/90 text-white py-3 px-6 rounded-lg h-14"
                    >
                      Submit <MoveRight className="ml-2 h-5 w-5" />
                    </Button>
                  </div>

                </form>
                 {/* Success Message */}
                 {successMessage && (
                      <div className="mb-4 mt-5 text-green-600 bg-green-100 p-3 rounded-md text-center">
                        {successMessage}
                      </div>
                    )}
              </div>
              <div className="w-full sm:w-[30%] space-y-2">
                <div className="flex gap-2  my-5">
                  {/* <div className="text-navItemActive  ">
                    <MapPin size={20} />
                  </div>
                  <div className="text-text2 text-sm">Audition Room Sdn Bhd center A-1-06, Tower A, lorem coach Park, 47301</div> */}
                </div>
                <div className="flex gap-2">
                  <div className="text-navItemActive  ">
                    <Mail size={20} />
                  </div>
                  <div className="text-text2 text-sm "><EMAIL></div>
                </div>
                {/* <div className="flex gap-2">
                  <div className="text-navItemActive  ">
                    <Phone size={20} />
                  </div>
                  <div className="text-text2 text-sm">************</div>
                </div> */}
                <div className="p-2">
                  <img src={Earth2Img} alt="earth image" className="rounded-3xl h-52 w-[100%] object-cover" />
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </>
  );
}

export default ContactUsFormSection;