import React, { useEffect, useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import {
  Dialog,
  DialogClose,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { useToast } from "@/components/ui/use-toast";
import { X } from "lucide-react";
import { Skeleton } from "@/components/ui/skeleton";
import { useGetShareableLinkQuery } from "@/redux/features/postSlice";
import { FacebookShareButton, TwitterShareButton, LinkedinShareButton } from 'react-share';
import { FacebookIcon, XIcon, LinkedinIcon } from 'react-share';

const PostShareableLinkDialog = ({
  postId,
  viewToken,
  children,
  isFreePlan,
}) => {
  const [isOpen, setIsOpen] = useState(false);
  const [showShareIcons, setShowShareIcons] = useState(false);
  const { toast } = useToast();
  const {
    data: shareableLink,
    isLoading,
    error,
  } = useGetShareableLinkQuery(
    { postId, viewToken },
    { skip: !isOpen || isFreePlan }
  );
  const link = shareableLink?.data;

  const handleGetShareableLink = (e) => {
    e.preventDefault();
    e.stopPropagation();

    if (isFreePlan) {
      toast({
        title: "Upgrade Required",
        description:
          "Sharing is available for Pro plan users. Upgrade to access.",
        duration: 3000,
      });
      return;
    }
    setIsOpen(true);
  };

  const handleCopyLink = () => {
    navigator.clipboard.writeText(link);
    toast({
      title: "Success",
      description: "Link copied to clipboard!",
    });
  };

  const handleClose = () => {
    setIsOpen(false);
  };
  
  const handleConfirmShare = () => {
    setShowShareIcons(true);
  };

  useEffect(() => {
    if (error) {
      toast({
        title: "Error",
        description: "Failed to get shareable link. Please try again.",
        variant: "destructive",
      });
      setIsOpen(false);
    }
  }, [error, toast]);

  return (
    <Dialog open={isOpen} onOpenChange={setIsOpen}>
      <DialogTrigger asChild>
        <div onClick={handleGetShareableLink}>{children}</div>
      </DialogTrigger>
      {!isFreePlan && (
        <DialogContent className="xl:w-[28%] w-[80%] h-auto bg-primary1 rounded-md text-text1 flex justify-center items-center flex-col p-6">
          <DialogClose
            className="absolute right-4 top-4 rounded-sm opacity-70 transition-opacity hover:opacity-100 focus:outline-none"
            onClick={handleClose}
          >
            <X className="h-4 w-4" />
            <span className="sr-only">Close</span>
          </DialogClose>
          <DialogHeader>
            <DialogTitle>{showShareIcons ? "Share with:" : "Are you ready to share this post?"}</DialogTitle>
            <DialogDescription className="text-text1">
              {showShareIcons ? (
                "You can now share this post with your network through your preferred social media platforms."
              ) : (
                "This content may be suitable for an 18+ audience. Are you sure you want to share it?"
              )}
            </DialogDescription>
          </DialogHeader>

          {!showShareIcons ? (
            <div className="flex gap-4 mt-4">
              <Button onClick={handleConfirmShare} className="bg-navItemActive text-white px-4 py-2 rounded-lg">
                I Agree
              </Button>
              <Button onClick={handleClose} className="bg-red-500 text-white px-4 py-2 rounded-lg">
                No
              </Button>
            </div>
          ) : (
            <div className="flex items-center justify-center space-x-2 xl:w-[66%] md:w-[70%] w-full">
              {isLoading ? (
                <Skeleton className="h-10 w-full" />
              ) : (
                <>
                  <FacebookShareButton url={link}>
                    <FacebookIcon size={32} round />
                  </FacebookShareButton>
                  <TwitterShareButton url={link}>
                    <XIcon size={32} round />
                  </TwitterShareButton>
                  <LinkedinShareButton url={link}>
                    <LinkedinIcon size={32} round />
                  </LinkedinShareButton>
                </>
              )}
            </div>
          )}
        </DialogContent>
      )}
    </Dialog>
  );
};

export default PostShareableLinkDialog;
