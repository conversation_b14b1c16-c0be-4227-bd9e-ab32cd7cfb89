import React from "react";
import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, Share2 } from "lucide-react";
import CTAReuseable from "./CTAReuseable";
import TalentSection from "../TalentSection";

import bannerImg from '../../../assets/images/book 1.png';
import pencilImg from '../../../assets/images/pencil-1.png';
import userRobot from '../../../assets/images/user-robot-1.png';
import unionImg from '../../../assets/images/Union.png';

const StatCard = ({ icon, title, desc, separator = true }) => (
  <div className="relative">
    <div className="relative text-text1 py-10 pr-5 h-[80%] text-center">
      {separator && (
        <div className="max-md:hidden absolute h-[150px] w-[1px] rounded right-1 bg-gradient-to-b from-blue1/70 to-navItemActive/20 " />
      )}
      <div className="flex justify-center">
        {/* <Icon size={30} className="mb-1"></Icon> */}
        <img src={icon} />
      </div>
      <div className={`text-3xl py-2 text-nowrap font-extrabold bg-gradient-to-r from-blue2/100 to-blue1/100 bg-clip-text text-transparent`}>{title}</div>
      <div className="text-sm text-[#A0A0A0]">{desc}</div>
    </div>
  </div>
);

const MonologueSection = () => {
  return (
    <section className="bg-primary1 pt-10 pb-20">
      <div className="mx-auto px-4">
        {/* Section Header */}
        <div className="text-center mb-10">
          <div className="flex items-center justify-center mb-1">
            <h2 className="text-3xl font-bold text-text1">
              Perfect Monologues, Perfectly You
            </h2>
          </div>
          <p className="text-lg text-text1 max-w-2xl mx-auto">Choose from our extensive library or create your own with AI assistance.</p>
        </div>

        {/* Stats Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8 rounded-3xl  lg:mx-10 md:mx-10  sm:mx-10  mx-0 lg:px-10 px-5 bg-gradient-to-b max-lg:from-navItemActive/40 from-navItemActive/40 via-transparent to-primary1">
          <StatCard
            icon={bannerImg}
            title="500+ Choices"
            label="Curated Monologues"
            desc="Explore a vast library of monologues ranging from timeless classics to modern-day pieces, tailored for men & women."
          />
          <StatCard
            icon={pencilImg}
            title="Customizable"
            label="Customize Monologues"
            desc="Transform any monologue into a personalized masterpiece that reflects your unique style and strengths."
          />
          <StatCard
            icon={userRobot}
            title="AI Assistance"
            label="Generate Monologues"
            desc="Leverage the power of AI to generate custom monologues designed to suit your acting needs."
          />
          <StatCard
            icon={unionImg}
            title="Social Reach"
            label="Generate Monologues"
            desc="Share your performances on social platforms or within our network to expand your visibility and connect with industry experts."
            separator={false}
          />
        </div>
      </div>
    </section>
  );
};

export default MonologueSection;
