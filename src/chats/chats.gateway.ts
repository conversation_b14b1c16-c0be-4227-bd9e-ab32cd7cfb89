import {
  MessageBody,
  SubscribeMessage,
  WebSocketGateway,
} from '@nestjs/websockets';
import { ChatsService } from './chats.service';
import { Body, HttpException, UseGuards, ValidationPipe } from '@nestjs/common';
import { SendMessageDto } from './dto/send-message-dto';
import { MarkasReadDto, OnlineStatusDto } from './dto/mark-read-dto';
import { Throttle, ThrottlerGuard } from '@nestjs/throttler';
import { WsThrottlerGuard } from '../auth/guards/wsthrottler.guard';

@WebSocketGateway({
  cors: {
    origin: '*',
    methods: ['GET', 'POST'],
    allowedHeaders: ['Content-Type', 'Authorization'],
    credentials: true,
  },
})
export class ChatsGateway {
  constructor(private readonly chatsService: ChatsService) {}

  @SubscribeMessage('send_message')
  @UseGuards(WsThrottlerGuard)
  async SendMessage(
    @MessageBody(new ValidationPipe()) sendMessageDto: SendMessageDto,
  ) {
    const resp = await this.chatsService.sendMessage(sendMessageDto);
    return { event: 'send_message_response', data: resp }; // listening to this event is not necessary in the frontend, supabase channel wil give data itself
  }

  @SubscribeMessage('mark_as_read')
  async MarkasRead(
    @MessageBody(new ValidationPipe()) markasReadDto: MarkasReadDto,
  ) {
    const resp = await this.chatsService.markAsRead(markasReadDto);
    return { event: 'mark_as_read_response', data: resp };
  }

  @SubscribeMessage('online_status')
  async OnlineStatus(
    @MessageBody(new ValidationPipe()) onlineStatusDto: OnlineStatusDto,
  ) {
    const resp = await this.chatsService.onlineStatus(onlineStatusDto);
    return { event: 'online_status_response', data: resp };
  }
}
