import { Injectable, Logger } from '@nestjs/common';
import { HumeClient } from 'hume';
import * as fs from 'fs';
import * as path from 'path';
import * as AdmZip from 'adm-zip';


export interface HumeWebhookPayload {
  job_id: string;
  status: 'COMPLETED' | 'FAILED' | 'IN_PROGRESS';
  predictions?: any[];
}

export interface HumeAnalysisResult {
  job_id: string;
  status: 'analyzing' | 'completed' | 'failed';
  raw_csv_content?: string; // Just the raw CSV content as string
  raw_response?: any;
}

@Injectable()
export class HumeAiService {
  private readonly logger = new Logger(HumeAiService.name);
  private readonly client: HumeClient;

  constructor() {
    this.client = new HumeClient({ 
      apiKey: process.env.HUME_API_KEY,
      secretKey: process.env.HUME_SECRET_KEY 
    });
  }

  /**
   * Start expression measurement job for a video URL
   */
  async startAnalysis(videoUrl: string): Promise<{ job_id: string }> {
    try {
      this.logger.log(`Starting HumeAI analysis for video: ${videoUrl}`);

      const callbackUrl = `${process.env.APP_BASE_URL}/api/hume/webhook`;

      const response = await this.client.expressionMeasurement.batch.startInferenceJob({
        urls: [videoUrl],
        callbackUrl: callbackUrl, // Webhook endpoint
        models: {
          face: {}, // Facial expression analysis
          prosody: {}, // Voice/speech analysis
        }
      });

      this.logger.log(`HumeAI job started with ID: ${response.jobId}, webhook: ${callbackUrl}`);
      return { job_id: response.jobId };
    } catch (error) {
      this.logger.error(`Failed to start HumeAI analysis: ${error.message}`, error.stack);
      throw new Error(`HumeAI analysis failed: ${error.message}`);
    }
  }

  /**
   * Get job status and results (simplified - no polling needed with webhooks)
   */
  async getJobResults(jobId: string): Promise<HumeAnalysisResult> {
    try {
      this.logger.log(`Fetching HumeAI status for job: ${jobId}`);

      const response = await this.client.expressionMeasurement.batch.getJobDetails(jobId);

      if (response.state.status === 'COMPLETED') {
        // Download and extract raw CSV content
        const rawCsvContent = await this.downloadAndExtractCSVContent(jobId);

        return {
          job_id: jobId,
          status: 'completed',
          raw_csv_content: rawCsvContent,
          raw_response: response
        };
      } else if (response.state.status === 'FAILED') {
        return {
          job_id: jobId,
          status: 'failed',
          raw_response: response
        };
      } else {
        return {
          job_id: jobId,
          status: 'analyzing', // All non-completed/failed states are analyzing
          raw_response: response
        };
      }
    } catch (error) {
      this.logger.error(`Failed to get HumeAI job results: ${error.message}`, error.stack);
      throw new Error(`Failed to get HumeAI results: ${error.message}`);
    }
  }



  /**
   * Process webhook callback from HumeAI
   */
  async processWebhookCallback(payload: HumeWebhookPayload): Promise<HumeAnalysisResult> {
    try {
      this.logger.log(`Processing HumeAI webhook for job: ${payload.job_id}, status: ${payload.status}`);

      if (payload.status === 'COMPLETED') {
        // Download and extract raw CSV content
        const rawCsvContent = await this.downloadAndExtractCSVContent(payload.job_id);

        return {
          job_id: payload.job_id,
          status: 'completed',
          raw_csv_content: rawCsvContent,
          raw_response: payload
        };
      } else if (payload.status === 'FAILED') {
        return {
          job_id: payload.job_id,
          status: 'failed',
          raw_response: payload
        };
      } else {
        return {
          job_id: payload.job_id,
          status: 'analyzing',
          raw_response: payload
        };
      }
    } catch (error) {
      this.logger.error(`Failed to process webhook callback: ${error.message}`, error.stack);
      throw new Error(`Webhook processing failed: ${error.message}`);
    }
  }

  /**
   * Download and extract raw CSV content from completed job
   */
  async downloadAndExtractCSVContent(jobId: string): Promise<string> {
    try {
      this.logger.log(`Downloading CSV artifacts for job: ${jobId}`);

      // Get job artifacts as stream.Readable
      const artifactsStream = await this.client.expressionMeasurement.batch.getJobArtifacts(jobId);

      // Create temp directory for extraction
      const tempDir = path.join(process.cwd(), 'temp', `hume-${jobId}`);
      if (!fs.existsSync(tempDir)) {
        fs.mkdirSync(tempDir, { recursive: true });
      }

      // Save zip file from stream
      const zipPath = path.join(tempDir, 'artifacts.zip');

      // Properly handle the stream.Readable
      await new Promise<void>((resolve, reject) => {
        const writeStream = fs.createWriteStream(zipPath);

        artifactsStream.pipe(writeStream);

        artifactsStream.on('error', (error) => {
          this.logger.error(`Stream error: ${error.message}`);
          reject(error);
        });

        writeStream.on('error', (error) => {
          this.logger.error(`Write stream error: ${error.message}`);
          reject(error);
        });

        writeStream.on('finish', () => {
          this.logger.log(`Successfully downloaded artifacts to: ${zipPath}`);
          resolve();
        });
      });

      // Extract zip file
      const zip = new AdmZip(zipPath);
      zip.extractAllTo(tempDir, true);

      // Find and read all CSV files, combine their content
      const csvContent = await this.extractAllCSVContent(tempDir);

      return csvContent;
    } catch (error) {
      this.logger.error(`Failed to download/extract CSV content: ${error.message}`, error.stack);
      throw new Error(`CSV extraction failed: ${error.message}`);
    }
  }

  /**
   * Extract all CSV content as raw text from HumeAI artifacts
   */
  private async extractAllCSVContent(extractDir: string): Promise<string> {
    let allCsvContent = '';

    try {
      this.logger.log(`Scanning directory: ${extractDir}`);

      // Recursively find all CSV files
      const csvFiles = this.findCsvFilesRecursively(extractDir);
      this.logger.log(`Found ${csvFiles.length} CSV files: ${csvFiles.map(f => path.basename(f)).join(', ')}`);

      for (const csvFilePath of csvFiles) {
        const fileName = path.basename(csvFilePath);
        this.logger.log(`Reading CSV file: ${csvFilePath}`);

        try {
          const fileContent = fs.readFileSync(csvFilePath, 'utf-8');
          allCsvContent += `\n\n=== ${fileName} ===\n${fileContent}`;
          this.logger.log(`Successfully read ${fileName}, content length: ${fileContent.length}`);
        } catch (readError) {
          this.logger.error(`Failed to read CSV file ${csvFilePath}: ${readError.message}`);
        }
      }

      if (!allCsvContent.trim()) {
        this.logger.warn('No CSV content found in extracted directory');
        // Log the full directory structure for debugging
        this.logDirectoryStructure(extractDir, 0);
      }

      return allCsvContent.trim();
    } catch (error) {
      this.logger.error(`Failed to extract CSV content: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Recursively find all CSV files in a directory
   */
  private findCsvFilesRecursively(dirPath: string): string[] {
    const csvFiles: string[] = [];

    try {
      const items = fs.readdirSync(dirPath);

      for (const item of items) {
        const itemPath = path.join(dirPath, item);

        // Skip the artifacts.zip file
        if (item === 'artifacts.zip') {
          continue;
        }

        const stat = fs.statSync(itemPath);

        if (stat.isDirectory()) {
          // Recursively search subdirectories
          csvFiles.push(...this.findCsvFilesRecursively(itemPath));
        } else if (item.endsWith('.csv')) {
          // Found a CSV file
          csvFiles.push(itemPath);
        }
      }
    } catch (error) {
      this.logger.error(`Error scanning directory ${dirPath}: ${error.message}`);
    }

    return csvFiles;
  }

  /**
   * Helper method to log directory structure for debugging
   */
  private logDirectoryStructure(dirPath: string, depth: number): void {
    const indent = '  '.repeat(depth);
    try {
      const items = fs.readdirSync(dirPath);
      this.logger.log(`${indent}${path.basename(dirPath)}/`);

      for (const item of items) {
        const itemPath = path.join(dirPath, item);
        const stat = fs.statSync(itemPath);

        if (stat.isDirectory() && depth < 3) { // Limit depth to avoid infinite recursion
          this.logDirectoryStructure(itemPath, depth + 1);
        } else {
          this.logger.log(`${indent}  ${item}`);
        }
      }
    } catch (error) {
      this.logger.error(`Error reading directory ${dirPath}: ${error.message}`);
    }
  }



  /**
   * Cleanup temporary files
   */
  private cleanupTempFiles(tempDir: string): void {
    try {
      if (fs.existsSync(tempDir)) {
        fs.rmSync(tempDir, { recursive: true, force: true });
      }
    } catch (error) {
      this.logger.warn(`Failed to cleanup temp files: ${error.message}`);
    }
  }

  /**
   * Check if HumeAI service is available
   */
  async healthCheck(): Promise<boolean> {
    try {
      // Try to list jobs to check if API is accessible
      await this.client.expressionMeasurement.batch.listJobs();
      return true;
    } catch (error) {
      this.logger.error(`HumeAI health check failed: ${error.message}`);
      return false;
    }
  }
}
