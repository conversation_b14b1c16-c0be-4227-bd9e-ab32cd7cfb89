import {
  Body,
  Controller,
  HttpException,
  Post,
  UploadedFile,
  UseGuards,
  UseInterceptors,
} from '@nestjs/common';
import { BotService } from './bot.service';
import { UserGuard } from '../auth/guards/user.guard';
import { RolesGuard } from '../auth/guards/role.guard';
import { Roles } from '../auth/decorators/roles.decorator';
import { USER_ROLE } from '../types/users.enum';
import { FileInterceptor } from '@nestjs/platform-express';

@Controller('bot')
export class BotController {
  constructor(private readonly botService: BotService) {}

  @Post('populate_vector_store')
  @Roles(USER_ROLE.ADMIN)
  @UseGuards(UserGuard, RolesGuard)
  @UseInterceptors(FileInterceptor('file'))
  async populateVectorStore(@UploadedFile() file: Express.Multer.File) {
    const response = await this.botService.populateVectorStore(file);
    if (response.error) {
      throw new HttpException(response.message, response.status);
    }
    return response;
  }

  @Post('question')
  // @UseGuards(UserGuard)
  async chat(@Body() body) {
    return await this.botService.chat(body.question, body.conversation_history);
  }
}
