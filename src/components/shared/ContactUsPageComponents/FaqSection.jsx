import { MoveRight } from 'lucide-react';
import { useState } from 'react';

const DropDownSection = ({ question, answer }) => {
  const [isOpen, setIsOpen] = useState(false);

  const toggleSection = () => {
    setIsOpen(!isOpen);
  }
  return (
    <div
      style={{ maxHeight: isOpen ? '1000px' : '100px' }}
      className={`py-4 px-4 mx-10 my-5 cursor-pointer transition-max-height ease-in-out duration-500 rounded-xl border-blue1 ${isOpen ? `border border-blue1 bg-gradient-to-b from-black/80 to-navItemActive/20` : 'border-b rounded-b-none mb-3'}`}
      onClick={toggleSection}
    >
      <div className='flex justify-between items-center'>
        <h1 className='text-sm md:text-lg font-semibold'>{question}</h1>
        <div className='relative'>
          <MoveRight size={35} className={`text-navItemActive transition-all  ${isOpen ? '-rotate-45' : 'rotate-0'}`} />
        </div>
      </div>
      <div className={`py-3 ${isOpen ? 'block' : 'hidden'}`}>
        <p className='md:text-md'>{answer}</p>
      </div>
    </div>
  )
}

const FaqSection = () => {
  const faqList = [
    {
      id: 'q1',
      ques: 'How can I contact AuditionRooms for support?',
      ans: 'You can reach out to us through the Contact AuditionRooms form available on this page. For additional inquiries about our features, visit the Features Page.'
    },
    {
      id: 'q2',
      ques: 'What is the best way to contact AuditionRooms for assistance?',
      ans: 'The fastest way to get in touch is via our Customer Service for Audition Platform Inquiries section. Use the form below or email <NAME_EMAIL>.'
    },
    {
      id: 'q3',
      ques: 'Where can I learn more about AuditionRooms features and tools?',
      ans: 'Visit our Features Page for a detailed look at the tools and services we offer to help actors of all experiences.'
    },
    {
      id: 'q4',
      ques: 'Do you offer customer service for audition platform inquiries?',
      ans: 'Yes, we have a dedicated team to assist the entire acting community. Check out our About Us page to learn about our mission and services.'
    },
    {
      id: 'q5',
      ques: 'How can I submit auditions online using AuditionRooms?',
      ans: 'Visit the How It Works section to learn about the step-by-step process of submitting auditions through our platform.'
    },
    {
      id: 'q6',
      ques: 'Is AuditionRooms available globally?',
      ans: 'Yes, we are proud to provide our services worldwide. Learn more on our Pricing age.'
    }
  ]
  return (
    <section className="text-text1 py-10">
      <div className="text-center">
        <h1 className="text-3xl font-extrabold pb-1">Your Questions Answered</h1>
        <p className='px-5'>Find the information you need or explore our platform for more insights.</p>
      </div>
      {
        faqList.map(faq =>
          <DropDownSection key={faq.id} question={faq.ques} answer={faq.ans} />
        )
      }
    </section>
  );
}

export default FaqSection;