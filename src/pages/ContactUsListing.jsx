import { useState, useEffect } from 'react';
import { useGetAllContactsQuery } from "@/redux/features/contactUsSlice";
import { Table, TableHeader, TableBody, TableRow, TableHead, TableCell } from "@/components/ui/table";
import { Pagination } from '@heroui/pagination';
import { Skeleton } from '@/components/ui/skeleton';

const ContactUsListing = () => {
    const [currentPage, setCurrentPage] = useState(1);
    const itemsPerPage = 10;
    const [totalPages, setTotalPages] = useState(0);
    const { data, error, isLoading, refetch } = useGetAllContactsQuery({ page: currentPage, limit: itemsPerPage });
    const [contactList, setContactList] = useState([]);

    useEffect(() => {
        if (data && data?.data) {
            setContactList(data?.data);
        }

        if (data?.count) {
            const lTotalPages = Math.ceil(data.count / itemsPerPage);
            setTotalPages(lTotalPages);
        }
    }, [data]);

    useEffect(() => {
        refetch();
    }, [currentPage, refetch]);

    const handlePageChange = (pageNumber) => {
        setCurrentPage(pageNumber);
        window.scrollTo({ top: 0, behavior: 'smooth' });
    }

    if (isLoading) {
        return <ContactUsSkeleton />;
    }

    if (error) {
        return <div className="text-red-500 text-center text-xl">Error loading contact-us data</div>;
    }

    return (
        <div className="flex flex-col justify-center items-center w-full min-h-screen bg-black py-10">
            <div className="lg:w-[90%] w-full bg-gray-900 shadow-lg rounded-lg p-8 flex flex-col items-center">
                <h1 className="text-4xl font-bold text-center mb-8 text-white">Contacts-Us Listing</h1>
                <Table className="overflow-x-auto">
                    <TableHeader>
                        <TableRow className="hover:bg-navItemActive">
                            <TableHead>Name</TableHead>
                            <TableHead>Email</TableHead>
                            <TableHead>Phone</TableHead>
                            <TableHead>Message</TableHead>
                            <TableHead>Submitted At</TableHead>
                        </TableRow>
                    </TableHeader>
                    <TableBody>
                        {contactList && contactList.map((contact) => (
                            <TableRow key={contact.id}>
                                <TableCell>{contact.full_name}</TableCell>
                                <TableCell>{contact.email}</TableCell>
                                <TableCell>{contact.phone}</TableCell>
                                <TableCell>{contact.message}</TableCell>
                                <TableCell>{new Date(contact.created_at).toLocaleString()}</TableCell>
                            </TableRow>
                        ))}
                    </TableBody>
                </Table>
            </div>
            <Pagination
                loop
                initialPage={currentPage}
                total={totalPages}
                onChange={handlePageChange}
                page={currentPage}
            />
        </div>
    );
};

export default ContactUsListing;

export const ContactUsSkeleton = () => (
    <>
        <div className="flex flex-col justify-center items-center w-full min-h-screen bg-black py-10">
            <div className="lg:w-[90%] w-full bg-gray-900 shadow-lg rounded-lg p-8 flex flex-col items-center flex-grow">
                <h1 className="text-4xl font-bold text-center mb-8 text-white">Loading Contact Us Data...</h1>
                <div className="w-full flex-grow">
                    <Table className="overflow-x-auto w-full">
                        <TableHeader>
                            <TableRow>
                                <TableHead>Name</TableHead>
                                <TableHead>Email</TableHead>
                                <TableHead>Phone</TableHead>
                                <TableHead>Message</TableHead>
                                <TableHead>Submitted At</TableHead>
                            </TableRow>
                        </TableHeader>
                        <TableBody>
                            {[...Array(12)].map((_, index) => (
                                <TableRow key={index}>
                                    <TableCell><Skeleton className="h-4 w-[100px]" /></TableCell>
                                    <TableCell><Skeleton className="h-4 w-[100px]" /></TableCell>
                                    <TableCell><Skeleton className="h-4 w-[100px]" /></TableCell>
                                    <TableCell><Skeleton className="h-4 w-[100px]" /></TableCell>
                                    <TableCell><Skeleton className="h-4 w-[100px]" /></TableCell>
                                </TableRow>
                            ))}
                        </TableBody>
                    </Table>
                </div>
            </div>
        </div>
    </>
);