import { ApiProperty } from '@nestjs/swagger';
import { IsOptional, IsString, IsEmail } from 'class-validator';

export class SubmitContactUsDto {
  @IsOptional()
  @IsString()
  @ApiProperty({ example: '<PERSON>', description: 'Full name of the person contacting' })
  fullName?: string;

  @IsEmail()
  @ApiProperty({ example: '<EMAIL>', description: 'Email address of the contact' })
  email: string;

  @IsString()
  @ApiProperty({ example: '+1234567890', description: 'Phone number of the contact' })
  phone: string;

  @IsOptional()
  @IsString()
  @ApiProperty({ example: 'I have a question about your service.', description: 'Message from the contact' })
  message?: string;
}
