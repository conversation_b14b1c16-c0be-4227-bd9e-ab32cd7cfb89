import { useState, useRef, useEffect } from "react";
import Webcam from "react-webcam";
import { Button } from "./ui/button";
import {
  CircleX,
  NotebookText,
  Pause,
  Undo2,
  Video,
  VideoOff,
  View,
  Maximize,
  Minimize,
} from "lucide-react";
import { <PERSON>lide<PERSON> } from "./ui/slider";
import { useHotkeys } from "react-hotkeys-hook";
import RecordRTC from "recordrtc";

const VideoRecorder = ({
  monologue,
  closeDialog,
  onViewRecording,
  parentId,
}) => {
  const DEFAULT_TRANSLATE_Y = 90;
  const [speed, setSpeed] = useState(1);
  const [isPaused, setIsPaused] = useState(false);
  const [recording, setRecording] = useState(false);
  const [videoSrc, setVideoSrc] = useState(null);
  const [capturedChunks, setCapturedChunks] = useState([]);
  const [startTime, setStartTime] = useState(null);
  const [currentTransform, setCurrentTransform] = useState(DEFAULT_TRANSLATE_Y);
  const [isWebcamReady, setIsWebcamReady] = useState(false);
  const [isFullScreen, setIsFullScreen] = useState(false);
  const [isPiPAvailable, setIsPiPAvailable] = useState(false);
  const [isPiPActive, setIsPiPActive] = useState(false);
  const [showPiPButton, setShowPiPButton] = useState(false);
  const [countdown, setCountdown] = useState(3);
  const [showCountdown, setShowCountdown] = useState(false);
  const [isVideoReady, setIsVideoReady] = useState(false);
  const [isMobile, setIsMobile] = useState(false);
  const [mimeType, setMimeType] = useState(null);

  const monologueRef = useRef(null);
  const webcamRef = useRef(null);
  const mediaRecorderRef = useRef(null);
  const componentRef = useRef(null);

  const mimeTypes = [
    "video/webm;codecs=vp8",
    "video/webm;codecs=vp9",
    "video/webm;codecs=opus",
    "video/webm",
    "video/mp4",
    "video/ogg",
  ];

  const getSupportedMimeType = () => {
    // Safari prefers mp4
    const isSafari = /^((?!chrome|android).)*safari/i.test(navigator.userAgent);
    
    if (isSafari) {
      const type = "video/mp4";
      setMimeType(type);
      return type;
    }

    for (const mimeType of mimeTypes) {
      if (MediaRecorder.isTypeSupported(mimeType)) {
        console.log({mimeType});
        setMimeType(mimeType);
        return mimeType;
      }
    }
    return null;
  };

  const isFirefox =
    typeof window !== "undefined" &&
    window.navigator.userAgent.toLowerCase().includes("firefox");

  useEffect(() => {
    if (typeof MediaRecorder === "undefined") {
      console.error("MediaRecorder is not supported in this browser");
    } else {
      const supportedMimeType = getSupportedMimeType();
      if (!supportedMimeType) {
        console.error("No supported MIME type found");
      } else {
        console.log(`Supported MIME type: ${supportedMimeType}`);
      }
    }
  }, []);

  useEffect(() => {
    let animationFrameId;
    let start;

    const scrollText = (timestamp) => {
      if (!start) start = timestamp;
      const elapsed = timestamp - start;

      if (!isPaused) {
        const newTransform = currentTransform - (elapsed / 1000) * speed;
        monologueRef.current.style.transform = `translateY(${newTransform}%)`;
        setCurrentTransform(newTransform);
      }

      start = timestamp;
      animationFrameId = requestAnimationFrame(scrollText);
    };

    if (startTime === null && !isPaused) {
      setStartTime(performance.now());
    }

    if (!isPaused) {
      animationFrameId = requestAnimationFrame(scrollText);
    }

    return () => cancelAnimationFrame(animationFrameId);
  }, [speed, isPaused, currentTransform, startTime]);

  useEffect(() => {
    if (capturedChunks.length > 0 && !recording) {
      const blob = new Blob(capturedChunks, { type: mimeType || 'video/webm' });
      console.log("Created blob with type:", mimeType, "size:", blob.size);
      
      // For Safari, we might need to convert the blob
      if (mimeType === 'video/mp4') {
        const reader = new FileReader();
        reader.onload = () => {
          const newBlob = new Blob([reader.result], { type: mimeType });
          const url = URL.createObjectURL(newBlob);
          console.log("Created URL for MP4:", url);
          setVideoSrc(url);
          setIsVideoReady(true);
        };
        reader.readAsArrayBuffer(blob);
      } else {
        const url = URL.createObjectURL(blob);
        console.log("Created URL for WebM:", url);
        setVideoSrc(url);
        setIsVideoReady(true);
      }
    }
  }, [capturedChunks, recording, mimeType]);

  useEffect(() => {
    setIsPiPAvailable(document.pictureInPictureEnabled);
  }, []);

  useEffect(() => {
    const handleFullScreenChange = () => {
      setIsFullScreen(!!document.fullscreenElement);
    };
    handleFullScreenChange();
    toggleFullScreen();

    const handlePiPChange = () => {
      setIsPiPActive(!!document.pictureInPictureElement);
    };

    document.addEventListener("fullscreenchange", handleFullScreenChange);
    document.addEventListener("enterpictureinpicture", handlePiPChange);
    document.addEventListener("leavepictureinpicture", handlePiPChange);

    return () => {
      document.removeEventListener("fullscreenchange", handleFullScreenChange);
      document.removeEventListener("enterpictureinpicture", handlePiPChange);
      document.removeEventListener("leavepictureinpicture", handlePiPChange);
    };
  }, []);

  useEffect(() => {
    const checkMobile = () => {
      // Check by screen size
      const isMobileBySize = window.innerWidth <= 768;
      
      // Check by user agent
      const isMobileByAgent = ('ontouchstart' in window) || 
        (navigator?.maxTouchPoints > 0) || 
        /android|iphone|ipad|ipod/i.test(navigator.userAgent);

      // Set as mobile if either condition is true
      setIsMobile(isMobileBySize || isMobileByAgent);
    };
    
    checkMobile();
    window.addEventListener('resize', checkMobile);
    
    return () => window.removeEventListener('resize', checkMobile);
  }, []);

  const handlePause = () => {
    setIsPaused(!isPaused);
  };

  const handleReset = () => {
    monologueRef.current.style.transform = `translateY(${DEFAULT_TRANSLATE_Y}%)`;
    setCurrentTransform(DEFAULT_TRANSLATE_Y);
    setIsPaused(true);
    setStartTime(null);
  };

  const handleStartRecording = () => {
    setRecording(true);
    setCapturedChunks([]);
    const stream = webcamRef.current.stream;

    if (!stream) {
      console.error("No webcam stream available");
      return;
    }

    try {
      // Safari specific handling
      const isSafari = /^((?!chrome|android).)*safari/i.test(navigator.userAgent);
      
      const options = {
        type: 'video',
        mimeType: getSupportedMimeType(),
        recorderType: RecordRTC.MediaStreamRecorder,
        disableLogs: false,
        numberOfAudioChannels: 2,
        // Safari specific options
        ...(isSafari && {
          videoBitsPerSecond: 128000,
          frameInterval: 90,
          video: {
            width: { min: 640, ideal: 1280, max: 1920 },
            height: { min: 480, ideal: 720, max: 1080 }
          }
        })
      };

      // Create a new stream clone for Safari
      if (isSafari) {
        const videoTrack = stream.getVideoTracks()[0];
        const audioTrack = stream.getAudioTracks()[0];
        const newStream = new MediaStream([videoTrack, audioTrack].filter(Boolean));
        mediaRecorderRef.current = new RecordRTC(newStream, options);
      } else {
        mediaRecorderRef.current = new RecordRTC(stream, options);
      }
      
      mediaRecorderRef.current.startRecording();

    } catch (error) {
      console.error("Error creating MediaRecorder:", error);
      console.error("Error name:", error.name);
      console.error("Error message:", error.message);
    }
  };

  const handleStopRecording = () => {
    if (mediaRecorderRef.current) {
      mediaRecorderRef.current.stopRecording(() => {
        const blob = mediaRecorderRef.current.getBlob();
        console.log("Got blob from RecordRTC:", blob.type, blob.size);
        setCapturedChunks([blob]);
        
        // Clean up the recorder
        mediaRecorderRef.current = null;
      });
    } else {
      console.warn("RecordRTC instance not found");
    }
    setRecording(false);
  };

  const handleResponsiveStartRecording = () => {
    setShowCountdown(true);
    let count = 3;
    setCountdown(count);

    const countdownInterval = setInterval(() => {
      count--;
      setCountdown(count);

      if (count === 0) {
        clearInterval(countdownInterval);
        setShowCountdown(false);
        handleStartRecording();
        setIsPaused(false);
      }
    }, 1000);
  };

  const handleResponsiveStopRecording = () => {
    handleStopRecording();
    setIsPaused(true);
    const checkVideoReady = setInterval(() => {
      if (isVideoReady) {
        clearInterval(checkVideoReady);
        if (parentId) {
          onViewRecording(videoSrc, parentId);
        } else {
          onViewRecording(videoSrc);
        }
      }
    }, 100);
  };
  const toggleFullScreen = () => {
    if (!document.fullscreenElement) {
      componentRef.current.requestFullscreen().catch((err) => {
        console.error(
          `Error attempting to enable full-screen mode: ${err.message}`
        );
      });
    } else {
      document.exitFullscreen();
    }
  };

  const togglePiP = async () => {
    if (!webcamRef.current.video) return;

    try {
      if (document.pictureInPictureElement) {
        await document.exitPictureInPicture();
      } else {
        await webcamRef.current.video.requestPictureInPicture();
      }
    } catch (error) {
      console.error("Failed to enter/exit Picture-in-Picture mode:", error);
    }
  };

  // Hotkeys
  useHotkeys("space", () => handlePause(), { preventDefault: true });
  useHotkeys("r", () => handleReset(), { preventDefault: true });
  useHotkeys("s", () => handleResponsiveStartRecording(), {
    preventDefault: true,
  });
  useHotkeys("x", () => handleResponsiveStopRecording(), {
    preventDefault: true,
  });
  useHotkeys("f", () => toggleFullScreen(), { preventDefault: true });
  useHotkeys("p", () => togglePiP(), { preventDefault: true });

  return (
    <div
      ref={componentRef}
      className={`relative ${isFullScreen
        ? "fixed inset-0 bg-primary1 z-50 p-4 overflow-auto flex flex-col md:flex-row"
        : ""
        }`}
    >
      <div
        className={`flex flex-col md:flex-row justify-between items-start md:gap-8 md:mt-0 sm:mt-8 ${isFullScreen ? "h-full w-full" : ""
          }`}
      >
        <div
          className={`w-full md:w-4/5 overflow-hidden relative mb-4 bg-primary1 px-2 border border-1 rounded-lg ${isFullScreen ? "h-full " : "h-[200px] md:h-[795px]"
            }`}
        >
          <div
            ref={monologueRef}
            className="absolute bottom-0 w-full text-center md:text-3xl text-xl p-8 pt-0 bg-transparent text-text1"
            style={{
              transition: "transform 0.1s linear",
              whiteSpace: "pre-wrap",
              transform: `translateY(${DEFAULT_TRANSLATE_Y}%)`,
            }}
          >
            {monologue}
          </div>
        </div>
        <div
          className={`2xl:w-1/5 md:w-2/5 w-full ${isFullScreen ? "h-full flex flex-col" : "h-[650px]"
            }`}
        >
          <div
            className={`bg-primary1 px-2 border border-1 rounded-lg relative `}
            onMouseEnter={() => setShowPiPButton(true)}
            onMouseLeave={() => setShowPiPButton(false)}
            style={{ height: isFullScreen ? "30%" : "auto" }}
          >
            <Webcam
              audio={true}
              muted={true || recording}
              ref={webcamRef}
              onUserMedia={() => {
                setIsWebcamReady(true);
              }}
              className={`w-full max-w-md ${isFullScreen ? "h-full object-cover" : "h-[200px] md:h-64"
                }`}
            />
            {isPiPAvailable && showPiPButton && (
              <Button
                onClick={togglePiP}
                className="absolute top-2 right-2 bg-opacity-50 bg-black text-white transition-opacity duration-300"
              >
                <View size={16} />
              </Button>
            )}
            {showCountdown && (
              <div className="absolute inset-0 flex items-center justify-center bg-black bg-opacity-50 text-white text-6xl font-bold">
                {countdown}
              </div>
            )}
          </div>
          <div
            className={`mt-4 flex flex-col md:justify-center md:gap-y-8 gap-y-4 bg-primary1 px-8 border border-1 rounded-lg h-auto md:pb-4 pb-6 w-full ${isFullScreen ? "flex-grow overflow-y-auto" : "md:h-[80%]"
              }`}
          >
            <label className="flex flex-col md:gap-y-4 gap-y-6 mt-4 justify-start items-start font-normal text-[16px]">
              Speed of Subtitles
              <Slider
                value={[speed]}
                onValueChange={(newValue) => setSpeed(newValue[0])}
                min={1}
                max={100}
                step={1}
                className="ml-2"
              />
            </label>

            <div className="flex flex-wrap gap-4 w-full  md:flex-col md:gap-8 md:hidden ">
              {!recording && (
                <>
                  <Button
                    onClick={handleResponsiveStartRecording}
                    className="w-full"
                    disabled={isMobile}
                  >
                    <div className="flex gap-2 justify-center">
                      <Video size={16} />
                      Start Recording
                    </div>
                  </Button>
                  {isMobile && (
                    <div className="w-full text-center text-sm text-text1 mt-2 px-4 bg-primary2 py-2 rounded-md">
                      Recording is currently available on desktop only. The more you use Audition Rooms, the more features will grow!
                    </div>
                  )}
                </>
              )}
              {!recording && isWebcamReady ? (
                <Button
                  disabled
                  className="bg-hover1 text-white p-2 rounded w-full"
                >
                  Waiting for webcam...
                </Button>
              ) : (
                <Button
                  onClick={handleResponsiveStopRecording}
                  className="w-full"
                >
                  <div className="flex gap-2 justify-center">
                    <Pause size={16} />
                    Pause
                  </div>
                </Button>
              )}

              {isPaused && videoSrc && (
                <Button
                  onClick={() => onViewRecording(videoSrc)}
                  className="w-full"
                >
                  <div className="flex gap-2">
                    <View size={16} />
                    Publish & Preview
                  </div>
                </Button>
              )}

              <Button onClick={handleReset} className="w-full">
                <div className="flex gap-2 justify-center">
                  <Undo2 size={16} />
                  Reset
                </div>
              </Button>
              {!recording && 
                <Button onClick={closeDialog} className="w-full bg-close text-text1">
                  <div className="flex gap-2 items-center">
                    <CircleX size={16} />
                    Close
                  </div>
                </Button>
              }
              
            </div>

            <div className="md:flex flex-wrap gap-4 w-full md:flex-col md:gap-8 hidden">
              <Button onClick={handlePause}>
                <div className="flex gap-2">
                  {isPaused ? <NotebookText size={16} /> : <Pause size={16} />}
                  {isPaused ? "Start Teleprompter" : "Pause Teleprompter"}
                </div>
              </Button>
              <Button onClick={handleReset}>
                <div className="flex gap-2">
                  <Undo2 size={16} />
                  Reset
                </div>
              </Button>
              {!recording && isWebcamReady ? (
                <Button onClick={handleResponsiveStartRecording}>
                  <div className="flex gap-2">
                    <Video size={16} />
                    Start Recording
                  </div>
                </Button>
              ) : !isWebcamReady ? (
                <Button disabled className="bg-hover1 text-white p-2 rounded">
                  Waiting for webcam...
                </Button>
              ) : (
                <Button onClick={handleResponsiveStopRecording}>
                  <div className="flex gap-2">
                    <VideoOff size={16} />
                    Stop Recording
                  </div>
                </Button>
              )}
              {videoSrc && (
                <Button
                  onClick={() => {
                    if (document.fullscreenElement) {
                      document.exitFullscreen().then(() => {
                        onViewRecording(videoSrc);
                      });
                    } else {
                      onViewRecording(videoSrc);
                    }
                  }}
                  className={
                    isPaused && videoSrc && isVideoReady ? "w-full" : ""
                  }
                >
                  {isPaused && videoSrc && isVideoReady ? (
                    <div className="flex gap-2">
                      <View size={16} />
                      Publish & Preview
                    </div>
                  ) : (
                    "View Recording"
                  )}
                </Button>
              )}
              <Button onClick={toggleFullScreen}>
                <div className="flex gap-2">
                  {isFullScreen ? (
                    <Minimize size={16} />
                  ) : (
                    <Maximize size={16} />
                  )}
                  {isFullScreen ? "Exit Full Screen" : "Full Screen"}
                </div>
              </Button>
              {!recording && 
                <Button onClick={closeDialog} className="bg-close text-text1">
                  <div className="flex gap-2 items-center">
                    <CircleX size={16} />
                    Close
                  </div>
                </Button>
              }
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default VideoRecorder;
