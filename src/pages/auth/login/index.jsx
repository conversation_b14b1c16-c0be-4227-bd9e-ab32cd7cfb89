import { useState } from "react";
import { useForm } from "react-hook-form";
import { useDispatch } from "react-redux";
import { Link, useNavigate, useSearchParams } from "react-router-dom";
import { EyeIcon, EyeOffIcon, LockIcon, MailIcon } from "lucide-react";
import { useToast } from "@/components/ui/use-toast";
import supabase from "@/lib/supabase";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { setRole } from "@/redux/features/userSlice";

const SignIn = () => {
  const dispatch = useDispatch();
  const navigate = useNavigate();
  const [isLoading, setIsLoading] = useState(false);
  const [showPassword, setShowPassword] = useState(false);
  const [searchParams] = useSearchParams();
  const to = searchParams.get("to") || "";
  const { toast } = useToast();


  const {
    register,
    handleSubmit,
    formState: { errors },
  } = useForm({
    defaultValues: {
      email: "",
      password: "",
    },
  });

  const handleLogin = async (data) => {
    try {
      setIsLoading(true);
      const { error } = await supabase.auth.signInWithPassword(data);
      if (error) {
        toast({
          title: `Sign-in failed: ${error.message}`,
          description: "Please check your email and password",
          variant: "destructive",
        });
      } else {
        navigate(to ? `/${to}` : '/');
      }
    } catch (error) {
      console.error("Error during login:", error);
      toast({
        title: `Sign-in error ${error.message}`,
        variant: "destructive",
      });
    } finally {
      const userRole = await fetchUserMetaData();
      if(userRole){
        dispatch(setRole(userRole));
      }
      setIsLoading(false);
    }
  };

  const fetchUserMetaData = async () => {
    const { data: user, error } = await supabase.auth.getUser(); // Use the new `getUser()` method

    if (error || !user) {
      console.error('No user is logged in or there was an error:', error?.message);
      return;
    }
  
    const currentUserId = user.user.id; // Get the current user's ID
  
    // Query the 'users' table or 'profiles' table for the metadata
    const { data, error: fetchError } = await supabase
      .from('users') // Use the table where the metadata is stored (e.g., 'profiles' or 'users')
      .select('metadata') // Specify the metadata field you want to retrieve
      .eq('user_id', currentUserId) // Filter by the current user's ID
      .single(); // Ensures only one record is returned (useful if 'id' is unique)
  
    if (fetchError) {
      return null;
    } else {
      return data?.metadata?.role;
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-primary1 to-secondary2 flex items-center justify-center p-4">
      <Card className="w-full max-w-md bg-secondary2/80 backdrop-blur-md border-border1 shadow-xl rounded-lg overflow-hidden">
    
      <div
         onClick={()=>navigate(-1)}
                  // to="/"
                  className="cursor-pointer m-3  absolute left-0 top-2 "
                >
                    <svg  xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="w-6 h-6 mr-2 stroke-white">
                <path d="M19 12H5"></path>
                <path d="M12 19l-7-7 7-7"></path>
              </svg>
                </div>
        <Link
          to="/"
          className="max-w-max m-auto h-16 bg-secondary2 flex items-center justify-center"
        >
          <img src="/logo.png" alt="Logo" className=" w-20 mt-8 invert" />
        </Link>

                <CardHeader className="space-y-1 pt-6">
          <CardTitle className="text-3xl font-bold text-text1 text-center">
            Welcome Back
          </CardTitle>
          <CardDescription className="text-text2 text-center">
            Enter your credentials to access your account
          </CardDescription>
        </CardHeader>
        <CardContent className="p-6">
          <form onSubmit={handleSubmit(handleLogin)} className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="email" className="text-text1">
                Email
              </Label>
              <div className="relative">
                <Input
                  id="email"
                  type="email"
                  placeholder="<EMAIL>"
                  className="pl-10 bg-otherBtn border-border1 text-text1 placeholder:text-text2 rounded-md"
                  {...register("email", { required: "Email is required" })}
                />
                <MailIcon
                  className="absolute left-3 top-1/2 -translate-y-1/2 text-text2"
                  size={18}
                />
              </div>
              {errors.email && (
                <p className="text-close text-sm">{errors.email.message}</p>
              )}
            </div>
            <div className="space-y-2">
              <div className="flex items-center justify-between">
                <Label htmlFor="password" className="text-text1">
                  Password
                </Label>
                <Link
                  to="/reset-password"
                  className="text-sm text-hover1 hover:underline"
                >
                  Forgot password?
                </Link>
              </div>
              <div className="relative">
                <Input
                  id="password"
                  type={showPassword ? "text" : "password"}
                  placeholder="Password"
                  className="pl-10 pr-10 bg-otherBtn border-border1 text-text1 placeholder:text-text2 rounded-md"
                  {...register("password", {
                    required: "Password is required",
                  })}
                />
                <LockIcon
                  className="absolute left-3 top-1/2 -translate-y-1/2 text-text2"
                  size={18}
                />
                <button
                  type="button"
                  onClick={() => setShowPassword(!showPassword)}
                  className="absolute right-3 top-1/2 -translate-y-1/2 text-text2 hover:text-text1"
                >
                  {showPassword ? (
                    <EyeOffIcon size={18} />
                  ) : (
                    <EyeIcon size={18} />
                  )}
                </button>
              </div>
              {errors.password && (
                <p className="text-close text-sm">{errors.password.message}</p>
              )}
            </div>
            <Button
              type="submit"
              className="w-full bg-hover1 text-white hover:bg-navItemActive transition-colors rounded-md"
              disabled={isLoading}
            >
              {isLoading ? "Signing in..." : "Sign In"}
            </Button>
          </form>
        </CardContent>
        <CardFooter className="flex flex-col space-y-4">
          <div className="text-center text-text2">
            Don't have an account?{" "}
            <Link
              to="/signup"
              className="text-hover1 hover:underline transition-all duration-300"
            >
              Sign up
            </Link>
          </div>
        </CardFooter>
      </Card>
    </div>
  );
};

export default SignIn;
