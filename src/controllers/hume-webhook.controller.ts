import { Controller, Post, Body, Logger, HttpCode, HttpStatus } from '@nestjs/common';
import { HumeAiService, HumeWebhookPayload } from '../services/hume-ai.service';
import { AzureOpenAiService } from '../services/azure-openai.service';
import { PostsService } from '../posts/posts.service';

@Controller('api/hume')
export class HumeWebhookController {
  private readonly logger = new Logger(HumeWebhookController.name);

  constructor(
    private readonly humeAiService: HumeAiService,
    private readonly azureOpenAiService: AzureOpenAiService,
    private readonly postsService: PostsService,
  ) {}

  /**
   * Handle webhook callbacks from HumeAI
   */
  @Post('webhook')
  @HttpCode(HttpStatus.OK)
  async handleWebhook(@Body() payload: HumeWebhookPayload) {
    try {
      this.logger.log(`Received HumeAI webhook: ${JSON.stringify(payload)}`);

      // Process the webhook payload
      const analysisResult = await this.humeAiService.processWebhookCallback(payload);

      if (analysisResult.status === 'analyzing') {
        // Update post status to analyzing (for IN_PROGRESS webhooks)
        const post = await this.postsService.findByHumeJobId(payload.job_id);
        if (post) {
          await this.postsService.updateHumeAnalysis(post.post_id, {
            analysis_status: 'analyzing'
          });
          this.logger.log(`Updated post ${post.post_id} status to analyzing`);
        }
      } else if (analysisResult.status === 'completed') {
        // Find the post associated with this job
        const post = await this.postsService.findByHumeJobId(payload.job_id);

        if (post) {
          this.logger.log(`Processing completed analysis for post: ${post.post_id}`);

          try {
            // Generate insights using Azure OpenAI
            this.logger.log(`Generating insights for post: ${post.post_id}`);
            const insights = await this.azureOpenAiService.generateInsights(
              analysisResult,
              post.content
            );

            this.logger.log(`Insights generated successfully for post: ${post.post_id}`);

            // Update the post with analysis results and insights
            await this.postsService.updateHumeAnalysis(post.post_id, {
              hume_analysis: analysisResult,
              ai_insights: insights,
              analysis_status: 'completed'
            });

            this.logger.log(`Successfully processed analysis for post: ${post.post_id}`);
          } catch (error) {
            this.logger.error(`Failed to generate insights for post ${post.post_id}: ${error.message}`);

            // Update status to failed if insight generation fails
            await this.postsService.updateHumeAnalysis(post.post_id, {
              hume_analysis: analysisResult,
              analysis_status: 'failed'
            });
          }
        } else {
          this.logger.warn(`No post found for HumeAI job: ${payload.job_id}`);
        }
      } else if (analysisResult.status === 'failed') {
        // Update any associated post with failed status
        const post = await this.postsService.findByHumeJobId(payload.job_id);
        if (post) {
          await this.postsService.updateHumeAnalysis(post.post_id, {
            analysis_status: 'failed',
            hume_analysis: analysisResult
          });
        }

        this.logger.error(`HumeAI analysis failed for job: ${payload.job_id}`);
      }

      return { success: true, message: 'Webhook processed successfully' };
    } catch (error) {
      this.logger.error(`Error processing HumeAI webhook: ${error.message}`, error.stack);
      
      // Still return success to prevent HumeAI from retrying
      return { success: false, error: error.message };
    }
  }

  /**
   * Health check endpoint for webhook
   */
  @Post('webhook/health')
  @HttpCode(HttpStatus.OK)
  async webhookHealth() {
    return { 
      status: 'healthy', 
      timestamp: new Date().toISOString(),
      service: 'hume-webhook'
    };
  }
}
