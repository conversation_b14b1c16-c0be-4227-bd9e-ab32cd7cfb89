const HeaderSection = ({ image, title }) => {

  return (
    <div className={`max-sm:h-[180px] h-[256px] w-full bg-cover bg-center bg-no-repeat flex items-center`}
     // style={{ backgroundImage: `url(${image})` }}
      style={{
          backgroundImage: `linear-gradient(to right, rgba(0,0,0,0.8) 10%, transparent 80%), url(${image})`,
          backgroundSize: 'cover',
          backgroundPosition: 'center 30%',
        }}
    >
      <div className="relative max-sm:text-2xl text-text1 text-3xl font-extrabold lg:px-10 px-5 mr-auto">
        {title} <br />
        <div className="w-[100px] h-[3px] mt-2 bg-navItemActive" />
      </div>
    </div>
  );
}

export default HeaderSection;