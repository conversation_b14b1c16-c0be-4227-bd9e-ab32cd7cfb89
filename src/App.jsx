import { BrowserRouter, Routes, Route, Navigate, useLocation } from "react-router-dom";
import SignUp from "./pages/auth/signup";
import SignIn from "./pages/auth/login";
import ResetPassword from "./pages/auth/fc";
import UpdatePassword from "./pages/auth/fc/UpdatePassword";
import FeedPage from "./pages/FeedPage";
import Profile from "./pages/Profile";
import Settings from "./pages/Settings";
import AuthLayout from "./layouts/AuthLayout";
import MainLayout from "./layouts/MainLayout";
import Auth from "./lib/Auth";
import Upload from "./pages/Upload";
import Messages from "./pages/Messages";
import ChatUI from "./pages/ChatUI";
import ProtectedRoutes from "./lib/ProtectedRoutes";
import GuestRoutes from "./lib/GuestRoutes";
import UpdateUserProfile from "./components/shared/Profile/UpdateUserProfile";
import { useEffect, useLayoutEffect } from "react";
import Aos from "aos";
import "aos/dist/aos.css";
import SinglePostDetails from "./components/shared/Feed/SinglePostDetails";
import CoachApplicationForm from "./pages/auth/signup/CoachApplication";
import CoachSetPasswordPage from "./pages/auth/fc/CoachSetPassword";
import CoachPlansPage from "./components/shared/Pricing/CoachPlansPage";
import { useSelector } from "react-redux";
import { useGetCurrentPlanQuery } from "./redux/features/planApi";
import AiSupportBot from "./components/shared/Support-bot/AiSupportBot";
import Coaches from "./pages/Coaches";
import SmoothLoading from "./components/SmoothLoading";
import LandingPage from "./pages/LandingPage";
import AboutUsPage from "./pages/AboutUsPage";
import FeaturesPage from "./pages/FeaturesPage";
import ContactUsPage from "./pages/ContactUsPage";
import PricingPage from "./pages/Pricing";
import SurveyPage from "./pages/SurveyPage";
import ContactUsListing from "./pages/ContactUsListing";

const Wrapper = ({ children }) => {
  const location = useLocation();

  useLayoutEffect(() => {
    window.scrollTo({ top: 0, left: 0, behavior: 'instant' });
  }, [location.pathname]);

  return children;
}

function App() {
  const { session } = useSelector((state) => state.user);
  const { role: userRole } = useSelector((state) => state.user);
  const isAuthenticated = session?.user;
  const isCoach = session?.user?.user_metadata?.role === "coach";
  const isAdmin = userRole === "admin";
  const { data: currentPlan, isLoading: isLoadingPlan } =
    useGetCurrentPlanQuery(undefined, {
      skip: !isAuthenticated || !isCoach,
    });

  const hasPlan = currentPlan?.data?.plan_id;

  // For animation on scroll
  useEffect(() => {
    Aos.init({
      offset: 100,
      duration: 800,
      easing: "ease-in-sine",
      delay: 300,
    });
    Aos.refresh();
  }, []);

  if (isAuthenticated && isCoach && isLoadingPlan) {
    return (
      <div>
        <SmoothLoading />
      </div>
    );
  }

  return (
    <>
      <Auth />
      <BrowserRouter>
        <AiSupportBot />
        <Wrapper>
          <Routes>
            <Route element={<GuestRoutes />}>
              <Route
                path="home"
                element={
                  <AuthLayout>
                    <LandingPage />
                  </AuthLayout>
                }
              />
              <Route
                path="signup"
                element={
                  <AuthLayout>
                    <SignUp />
                  </AuthLayout>
                }
              />
              <Route
                path="/coach-apply"
                element={
                  <AuthLayout>
                    <CoachApplicationForm />
                  </AuthLayout>
                }
              />
              <Route
                path="/set-password"
                element={
                  <AuthLayout>
                    <CoachSetPasswordPage />
                  </AuthLayout>
                }
              />

              <Route
                path="signin"
                element={
                  <AuthLayout>
                    <SignIn />
                  </AuthLayout>
                }
              />

              <Route
                path="reset-password"
                element={
                  <AuthLayout>
                    <ResetPassword />
                  </AuthLayout>
                }
              />

              <Route
                path="update-password"
                element={
                  <AuthLayout>
                    <UpdatePassword />
                  </AuthLayout>
                }
              />
              <Route
                path="about-us"
                element={
                  <AuthLayout>
                    <AboutUsPage />
                  </AuthLayout>
                }
              />
              <Route
                path="features"
                element={
                  <AuthLayout>
                    <FeaturesPage />
                  </AuthLayout>
                }
              />

              <Route
                path="contact-us"
                element={
                  <AuthLayout>
                    <ContactUsPage />
                  </AuthLayout>
                }
              />
              <Route
                path="pricing"
                element={
                  <AuthLayout>
                    <PricingPage />
                  </AuthLayout>
                }
              />
            </Route>

            <Route element={<ProtectedRoutes />}>
              <Route
                path="/coach-plans"
                element={
                  isAuthenticated && isCoach && !hasPlan ? (
                    <AuthLayout>
                      <CoachPlansPage />
                    </AuthLayout>
                  ) : (
                    <Navigate to="/" />
                  )
                }
              />
              <Route
                path="/"
                element={
                  isAuthenticated && isCoach && !hasPlan ? (
                    <Navigate to="/coach-plans" />
                  ) : (
                    <MainLayout>
                      <FeedPage />
                    </MainLayout>
                  )
                }
              />
              <Route
                path="/post/:postId"
                element={
                  <MainLayout>
                    <SinglePostDetails />
                  </MainLayout>
                }
              />
              <Route
                path="/messages"
                element={
                  <MainLayout>
                    <Messages />
                  </MainLayout>
                }
              />
              <Route
                path="/messages/:chatId"
                element={
                  <MainLayout>
                    <ChatUI />
                  </MainLayout>
                }
              />
              <Route
                path="/profile"
                element={
                  <MainLayout>
                    <Profile />
                  </MainLayout>
                }
              />
              <Route
                path="/profile/update"
                element={
                  <MainLayout>
                    <UpdateUserProfile />
                  </MainLayout>
                }
              />
              <Route
                path="/settings"
                element={
                  <MainLayout>
                    <Settings />
                  </MainLayout>
                }
              />
              {!isCoach && (
                <Route
                  path="/coaches"
                  element={
                    <MainLayout>
                      <Coaches />
                    </MainLayout>
                  }
                />
              )}

              <Route
                path="/upload"
                element={
                  <MainLayout>
                    <Upload />
                  </MainLayout>
                }
              />

              {isAdmin && (
                  <Route
                    path="/survey-page"
                    element={
                      <MainLayout>
                        <SurveyPage />
                      </MainLayout>
                    }
                  />
              )}

              <Route
                path="/contact-listing"
                element={
                  <MainLayout>
                    <ContactUsListing />
                  </MainLayout>
                }
              />
            </Route>
          </Routes>
        </Wrapper>
      </BrowserRouter>
    </>
  );
}

export default App;
