import React from "react";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";

const MonologueChipSelector = ({ ChipsFilter, activeChips, onChipChange }) => {
  const allFilters = ["All", ...ChipsFilter];
  const currentSelection = activeChips.length === 0 ? "All" : activeChips[0];

  return (
    <>
      <div className="md:hidden mb-4 w-full">
        <Select onValueChange={onChipChange} value={currentSelection}>
          <SelectTrigger className="w-full bg-primary1 text-text2 border-text2 focus:ring-secondary2 focus:ring-offset-0">
            <SelectValue placeholder="Select a filter" />
          </SelectTrigger>
          <SelectContent className="bg-primary1 text-text2 border-text2">
            {allFilters.map((chip) => (
              <SelectItem
                key={chip}
                value={chip}
                className={`${
                  chip === currentSelection
                    ? "bg-secondary2 text-text1"
                    : "hover:bg-secondary1 hover:text-primary1"
                }`}
              >
                {chip}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
      </div>
      <div className="hidden md:flex flex-wrap gap-2 mb-4">
        {allFilters.map((chip) => (
          <button
            key={chip}
            onClick={() => onChipChange(chip)}
            className={`px-3 py-1 rounded-full text-xs font-semibold ${
              chip === currentSelection
                ? "bg-secondary2 text-text1"
                : "bg-primary1 text-text2 hover:bg-secondary1 hover:text-primary1"
            }`}
          >
            {chip}
          </button>
        ))}
      </div>
    </>
  );
};

export default MonologueChipSelector;
