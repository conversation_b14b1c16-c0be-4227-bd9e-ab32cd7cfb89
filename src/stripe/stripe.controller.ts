import { Body, Controller, Logger, Post, Req } from '@nestjs/common';
import { StripeService } from './stripe.service';
import { <PERSON>ron } from '@nestjs/schedule';

@Controller('stripe')
export class StripeController {
  private readonly logger = new Logger(StripeController.name);

  constructor(private readonly stripeService: StripeService) {}

  @Post('event_listener') // stripe events are forwarded to this endpoint
  async HandleStripeEvents(@Req() req, @Body() body) {
    await this.stripeService.handleStripeEvents(body);
  }

  // @Cron('0 */30 0-6 * * *')
  // @Cron('*/30 * * * * *', { name: 'Cancel Expired Subscriptions' })
  // async cancelSubscriptionCron() {
  //   this.logger.debug('Cron is running');
  //   // await this.stripeService.cancelExpiredSubscriptions();
  // }
}
