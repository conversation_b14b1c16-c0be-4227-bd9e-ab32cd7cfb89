import { HttpStatus, Injectable } from '@nestjs/common';
import { DatabaseService } from '../database/database.service';
import { CustomResponse } from '../utils/CustomResponse';
import * as fs from 'fs';
import { RecursiveCharacterTextSplitter } from 'langchain/text_splitter';
import { SupabaseVectorStore } from '@langchain/community/vectorstores/supabase';
import { OpenAIEmbeddings, ChatOpenAI } from '@langchain/openai';
import { PromptTemplate } from '@langchain/core/prompts';
import { StringOutputParser } from '@langchain/core/output_parsers';
import {
  RunnablePassthrough,
  RunnableSequence,
} from '@langchain/core/runnables';
import { combineDocuments, formatConvHistory } from '../utils/functions';
import { SupabaseClient } from '@supabase/supabase-js';

@Injectable()
export class BotService {
  private llm: ChatOpenAI;
  private vector_store: SupabaseVectorStore;
  private question_chain: RunnableSequence;
  private supabase: SupabaseClient;
  private monologue_prompt: PromptTemplate;
  private monologue_examples_loaded: boolean = false; // this is used to make sure monologue examples are loaded only once
  private monologue_examples: string;

  // all the templates, chains, runnable sequences are initialized once in
  // the constructor and then used in other methods rather than creating them
  // again and again, stuff like monologues that requires database fetching
  // is handled through booleans
  constructor(private readonly dbService: DatabaseService) {
    this.supabase = this.dbService.getSupabaseClient();
    this.llm = new ChatOpenAI({ apiKey: process.env.OAI_KEY });
    this.vector_store = new SupabaseVectorStore(
      new OpenAIEmbeddings({ apiKey: process.env.OAI_KEY }),
      {
        client: this.dbService.getSupabaseClient(),
        queryName: 'match_documents',
        tableName: 'documents',
      },
    );
    const standaloneq_template = `Given some conversation history (if any) and a question, convert the question to a standalone question. 
    conversation history: {conv_history}
    question: {question} 
    standalone question:`;

    const answer_template = `You are a support bot who can answer a given question about an online platform called Audition Room based on the context
    provided and the conversation history. Try to find the answer in the context. If the answer is not given in the context, find the answer in the 
    conversation history if possible. If you cannot find the answer try not to make it up but rather direct the user <NAME_EMAIL>.
    Keep your answers to the point and try not to yap. Be friendly.
    context: {context}
    conversation history: {conv_history}
    question: {question}
    answer: `;

    const retriever = this.vector_store.asRetriever();

    const answerPrompt = PromptTemplate.fromTemplate(answer_template);
    const standaloneQuestionPrompt =
      PromptTemplate.fromTemplate(standaloneq_template);

    const standaloneQuestionChain = standaloneQuestionPrompt
      .pipe(this.llm)
      .pipe(new StringOutputParser());
    const retrieverChain = RunnableSequence.from([
      (prevResult) => prevResult.standalone_question,
      retriever,
      combineDocuments,
    ]);
    const answerChain = answerPrompt
      .pipe(this.llm)
      .pipe(new StringOutputParser());

    this.question_chain = RunnableSequence.from([
      {
        standalone_question: standaloneQuestionChain,
        original_input: new RunnablePassthrough(),
      },
      {
        context: retrieverChain,
        question: ({ original_input }) => original_input.question,
        conv_history: ({ original_input }) => original_input.conv_history,
      },
      answerChain,
    ]);

    const monologue_template = `Given some existing monologues and a prompt, generate a monologue according to the prompt. Make it descriptive, indicate emotions, settings and speakers. It should be goood enough to be used in an audition. 
    existing monologues: {existing_monologues}
    prompt: {prompt}
    new monologue:`;

    this.monologue_prompt = PromptTemplate.fromTemplate(monologue_template);
  }

  async populateVectorStore(
    // populate vector store using input document with questions and answers for later retrieval
    file: Express.Multer.File,
  ): Promise<CustomResponse> {
    try {
      const text = file.buffer.toString();
      const splitter = new RecursiveCharacterTextSplitter({
        chunkSize: 100,
        chunkOverlap: 5,
      });
      const output = await splitter.createDocuments([text]);
      const sb_client = this.dbService.getSupabaseClient();
      await SupabaseVectorStore.fromDocuments(
        output,
        new OpenAIEmbeddings({ apiKey: process.env.OAI_KEY }),
        { client: sb_client, tableName: 'documents' },
      );
      return {
        data: null,
        error: false,
        message: 'Vector store populated',
        status: HttpStatus.OK,
      };
    } catch (err) {
      console.log(err);
      return {
        data: null,
        error: true,
        message: 'Error populating vector store',
        status: HttpStatus.INTERNAL_SERVER_ERROR,
      };
    }
  }
  async chat(question: string, conversation_history: string[] = []) {
    const response = await this.question_chain.invoke({
      question: question,
      conv_history: formatConvHistory(conversation_history), // frontend will provide conversation history and push messages to it
    });

    return response;
  }

  // count is the number of examples to load
  async loadMonologueExamples(count: number = 1) {
    const data = await this.supabase
      .from('monologues')
      .select('content')
      .range(0, count);
    this.monologue_examples = data.data
      ? data.data.map((item) => item.content).join('\n')
      : '';
    this.monologue_examples_loaded = true; // to make sure if monologues are loaded, they arent loaded again
  }

  async generate_monologue(prompt: string) {
    if (this.monologue_examples_loaded == false) {
      // if monologues havent been loaded, load them
      await this.loadMonologueExamples(2);
    }

    const monologue_chain = this.monologue_prompt
      .pipe(this.llm)
      .pipe(new StringOutputParser());

    return await monologue_chain.invoke({
      existing_monologues: this.monologue_examples,
      prompt,
    });
  }
}
