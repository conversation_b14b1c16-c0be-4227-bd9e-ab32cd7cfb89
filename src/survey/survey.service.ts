import { Injectable, HttpStatus } from '@nestjs/common';
import { Prisma, PrismaClient } from '@prisma/client';
import { DatabaseService } from 'src/database/database.service';
import { CustomResponse } from 'src/utils/CustomResponse';

@Injectable()
export class SurveyService {
  private prisma: PrismaClient;
  constructor(private readonly dbService: DatabaseService) {
    this.prisma = dbService.getClient();
  }

  async createSurvey(data: Prisma.surveyCreateInput): Promise<CustomResponse> {
    const survey = await this.prisma.survey.create({
      data,
    });
    return {
      data: survey,
      error: null,
      status: HttpStatus.OK,
      message: 'Survey created successfully',
    };
  }

  async getAllSurvey(page, limit): Promise<CustomResponse> {
    try {
      page = page || 1;
      limit = limit || 25;
      const offset = (Number(page) - 1) * Number(limit);
  
      const surveys = await this.prisma.survey.findMany({
        skip: offset,
        take: Number(limit),
      });
  
      const totalSurveys = await this.prisma.survey.count();
  
      return {
        data: surveys,
        error: null,
        count: totalSurveys,
        status: HttpStatus.OK,
        message: 'Surveys fetched successfully',
      };
    } catch (err) {
      console.log(err);
      return {
        data: null,
        error: true,
        status: HttpStatus.INTERNAL_SERVER_ERROR,
        message: 'Unable to fetch surveys',
      };
    }
  }
}
