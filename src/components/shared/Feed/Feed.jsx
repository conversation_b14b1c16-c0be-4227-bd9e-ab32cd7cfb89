import React, { useCallback, useEffect, useRef } from "react";
import RenderIfVisible from "react-render-if-visible";
import Post from "./Post";
import { Skeleton } from "@/components/ui/skeleton";

const Feed = ({
  posts,
  isLoading,
  isFetching,
  loadMorePosts,
  hasMorePosts,
  onDelete
}) => {
  const lastPostRef = useRef();
  const handleScroll = useCallback(() => {
    if (
      lastPostRef.current &&
      window.innerHeight + window.pageYOffset >=
      lastPostRef.current.offsetTop - 1000 &&
      hasMorePosts &&
      !isFetching
    ) {
      loadMorePosts();
    }
  }, [hasMorePosts, isFetching, loadMorePosts]);

  useEffect(() => {
    window.addEventListener("scroll", handleScroll);
    return () => window.removeEventListener("scroll", handleScroll);
  }, [handleScroll]);

  if (isLoading && posts.length === 0) return <PostSkeleton count={10} />;

  return (
    <div className="py-3 sm:py-5 relative mt-4 sm:mt-8 min-h-screen w-full px-2 sm:px-0">
      {posts.map((post, index) => (
        <RenderIfVisible
          key={post.post_id}
          defaultHeight={400}
          visibleOffset={1000}
        >
          <div ref={index === posts.length - 1 ? lastPostRef : null}>
            <Post onDeletePost={onDelete} postId={post.post_id} {...post} originalPost={post} />
          </div>
        </RenderIfVisible>
      ))}
      {isFetching && <PostSkeleton />}
    </div>
  );
};

export default Feed;

export const PostSkeleton = ({ count = 4 }) => (
  <>
    {[...Array(count)].map((_, index) => (
      <div
        key={index}
        className="p-2 sm:p-4 space-y-2 sm:space-y-4 rounded-lg mb-2 sm:mb-4"
      >
        <div className="flex items-center space-x-2 sm:space-x-4">
          <Skeleton className="w-8 h-8 sm:w-12 sm:h-12 rounded-full" />
          <div className="space-y-1 sm:space-y-2">
            <Skeleton className="h-3 sm:h-4 w-[150px] sm:w-[250px]" />
            <Skeleton className="h-3 sm:h-4 w-[100px] sm:w-[200px]" />
          </div>
        </div>
        <Skeleton className="h-3 sm:h-4 w-full" />
        <Skeleton className="h-3 sm:h-4 w-full" />
        <Skeleton className="h-3 sm:h-4 w-2/3" />
      </div>
    ))}
  </>
);
