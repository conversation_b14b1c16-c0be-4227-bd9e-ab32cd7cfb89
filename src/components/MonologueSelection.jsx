import { useCallback, useEffect, useState } from "react";
import { Skeleton } from "./ui/skeleton";
import { Edit2, ChevronDown, ChevronUp, X, Trash2 } from "lucide-react";
import { useGetMonologuesQuery, useGetMonologueFilterQuery, useDeleteMonologueMutation } from "@/redux/features/postSlice";
import MonologueFilters from "./MonologueFilters";
import { Pagination } from "@heroui/pagination";

// eslint-disable-next-line react/prop-types
const MonologueSelection = ({ onSelect, onEdit, isAdmin }) => {
  const [currentPage, setCurrentPage] = useState(1);
  const itemsPerPage = 25;
  const [monologues, setMonologues] = useState([]);
  const [monologueFilter, setMonologueFilter] = useState([]);
  const [selectedMonologueId, setSelectedMonologueId] = useState(null);
  const [expandedContent, setExpandedContent] = useState(null);
  const [showInfo, setShowInfo] = useState({});
  // Search and Filter States
  const [searchTerm, setSearchTerm] = useState("");
  const [activeChip, setActiveChip] = useState("All");
  const [genderFilter, setGenderFilter] = useState("All");
  const [categoryFilter, setCategoryFilter] = useState("All");
  const [ deleteMonologue ] = useDeleteMonologueMutation();
  const { 
    data: monologuesData,
    isLoading, 
    error,
    refetch
  } = useGetMonologuesQuery({ page: currentPage, limit: itemsPerPage, searchTerm: searchTerm, activeChip, genderFilter, categoryFilter });
  const [activeFilters, setActiveFilters] = useState([]);
  const [isMonologueFilterCompleted, seIsMonologueFilterCompleted] = useState(false);
  const [totalPages, setTotalPages] = useState(0);
  const { data: monologuesFilter } = useGetMonologueFilterQuery();
  const [totalItems, setTotalItems] = useState(0);

  useEffect(()=>{
    if(searchTerm || (activeChip !== 'All' || genderFilter != 'All' || categoryFilter != 'All')){
      setCurrentPage(1);
    }
  },[searchTerm, activeChip, genderFilter, categoryFilter]);
  
  useEffect(()=>{
    refetch();
  },[currentPage, refetch, activeChip, genderFilter, categoryFilter]);

  useEffect(()=>{
    if(monologuesFilter){
      setMonologueFilter(monologuesFilter);
    }
  },[monologuesFilter]);

  useEffect(() => {
    if (monologuesData?.data) {
      setMonologues(monologuesData.data);
      const initialShowInfo = {};
      monologuesData.data.forEach((m) => {
        initialShowInfo[m.monologue_id] = false;
      });
      setShowInfo(initialShowInfo);
    }
    if (monologuesData?.count){
      const lTotalPages = Math.ceil(monologuesData.count/itemsPerPage);
      setTotalItems(monologuesData.count);
      setTotalPages(lTotalPages);
    }
  }, [monologuesData]);

  // Get unique filter options
  const chips = [...new Set(monologueFilter.map((m) => m.chip))];
  const genderOptions = [...new Set(monologueFilter.map((m) => m.gender))];
  const categoryOptions = [...new Set(monologueFilter.map((m) => m.category))];

  const handleSelect = (monologue) => {
    setSelectedMonologueId(monologue.monologue_id);
    onSelect(monologue);
  };

  const handleEdit = (e, monologue) => {
    e.stopPropagation();
    if (onEdit) {
      handleSelect(monologue);
      onEdit(monologue, updateMonologue);
    }
  };

  const updateMonologue = useCallback(
    (editedMonologue) => {
      const updatedMonologues = monologues.map((m) =>
        m.monologue_id === editedMonologue.monologue_id ? editedMonologue : m
      );
      setMonologues(updatedMonologues);
      if (selectedMonologueId === editedMonologue.monologue_id) {
        onSelect(editedMonologue);
      }
    },
    [monologues, selectedMonologueId, onSelect]
  );

  // Filter Handlers
  const handleChipChange = (value) => {
    setActiveChip(value);
    updateActiveFilters("chip", value);
  };

  const handleGenderChange = (value) => {
    setGenderFilter(value);
    updateActiveFilters("gender", value);
  };

  const handleCategoryChange = (value) => {
    setCategoryFilter(value);
    updateActiveFilters("category", value);
  };

  const updateActiveFilters = (type, value) => {
    if (value === "All") {
      setActiveFilters((prev) => prev.filter((f) => f.type !== type));
    } else {
      setActiveFilters((prev) => [
        ...prev.filter((f) => f.type !== type),
        { type, value },
      ]);
    }
  };

  const removeFilter = (filterToRemove) => {
    switch (filterToRemove.type) {
      case "chip":
        setActiveChip("All");
        break;
      case "gender":
        setGenderFilter("All");
        break;
      case "category":
        setCategoryFilter("All");
        break;
    }
    setActiveFilters((prev) =>
      prev.filter(
        (f) =>
          !(f.type === filterToRemove.type && f.value === filterToRemove.value)
      )
    );
  };

  const clearAllFilters = () => {
    setActiveChip("All");
    setGenderFilter("All");
    setCategoryFilter("All");
    setActiveFilters([]);
    setSearchTerm("");
  };

  const toggleInfo = (monologueId, e) => {
    e.stopPropagation();
    setShowInfo((prev) => ({
      ...prev,
      [monologueId]: !prev[monologueId],
    }));
  };

  const toggleContent = (e, monologueId) => {
    e.stopPropagation();
    setExpandedContent(expandedContent === monologueId ? null : monologueId);
  };

  const handlePageChange = (pageNumber) => {
    setCurrentPage(pageNumber);
    window.scrollTo({ top: 0, behavior: 'smooth' });
  };

  const handleRemove = async(id) => {
    try {
      await deleteMonologue(id).unwrap();
      refetch();
    } catch (error) {
      console.error("Failed to delete monologue:", error);
    }
  };

  // Filter monologues
  const filteredMonologues = monologues;

  if (!isMonologueFilterCompleted && filteredMonologues && filteredMonologues.length > 0) seIsMonologueFilterCompleted(true);

  if (isLoading && monologues.length === 0) {
    return (
      <div className="space-y-4 mt-8">
        <Skeleton className="h-40 w-full rounded-lg" />
        <div className="space-y-4">
          {[1, 2, 3, 4, 5].map((i) => (
            <Skeleton key={i} className="h-32 w-full rounded-lg" />
          ))}
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="mt-8 p-6 bg-red-50 rounded-lg border border-red-200">
        <h3 className="text-red-800 font-medium">Error loading monologues</h3>
        <p className="text-red-600 text-sm mt-1">{error.message}</p>
      </div>
    );
  }

  return (
    <div className="mt-8 space-y-6">
      {/* Search and Filter Panel */}
      <div className="bg-primary1 rounded-lg border border-text2/20 shadow-sm">
          {/* Filters */}
          <MonologueFilters
            searchTerm={searchTerm}
            onSearchChange={setSearchTerm}
            chips={chips}
            activeChip={activeChip}
            genderOptions={genderOptions}
            categoryOptions={categoryOptions}
            activeGender={genderFilter}
            activeCategory={categoryFilter}
            activeFilters={activeFilters}
            onChipChange={handleChipChange}
            onGenderChange={handleGenderChange}
            onCategoryChange={handleCategoryChange}
            onRemoveFilter={removeFilter}
            onClearAll={clearAllFilters}
          />
      </div>

      {/* Results Count */}
      <div className="flex items-center justify-between">
        <div className="text-sm text-text2">
          Showing {filteredMonologues.length} out of <span className="text-purple">
            <span className="font-bold">{totalItems}</span> total monologues </span>
        </div>
        {(searchTerm || activeFilters.length > 0) && (
          <button
            onClick={clearAllFilters}
            className="text-text2 hover:text-text1 text-sm font-medium flex items-center gap-1"
          >
            <X size={14} />
            Clear filters
          </button>
        )}
      </div>

      {/* Monologues Table Header */}
      <div className="hidden md:grid grid-cols-10 gap-4 px-6 py-4 bg-secondary2 text-text1 rounded-lg text-sm">
        <div className="col-span-2 font-medium uppercase tracking-wider truncate">
          Title
        </div>
        <div className="col-span-1 font-medium uppercase tracking-wider truncate">
          Character
        </div>
        <div className="col-span-1 font-medium uppercase tracking-wider truncate">
          Type
        </div>
        <div className="col-span-3 font-medium uppercase tracking-wider truncate">
          Monologue
        </div>
        <div className="col-span-1 font-medium uppercase tracking-wider truncate">
          Gender
        </div>
        {isAdmin && (
          <div className="col-span-1 font-medium uppercase tracking-wider truncate">
            Delete
          </div>
        )}
      </div>

      {/* Monologues List */}
      <div className="space-y-4">
        {isMonologueFilterCompleted && filteredMonologues.length === 0 ? (
          <div className="text-center py-8 text-text2">
            <p className="text-lg font-medium">No monologues found</p>
            <p className="text-sm mt-1">Try adjusting your filters or search</p>
          </div>
        ) : (
          <div>
            {filteredMonologues.map((monologue) => (
              <div
                key={monologue.monologue_id}
                className={`md:grid md:grid-cols-10 mb-4 gap-4 px-6 py-4 bg-primary1 rounded-lg border 
                         transition-all duration-300 cursor-pointer
                         ${selectedMonologueId === monologue.monologue_id
                    ? "ring-2 ring-secondary2 bg-secondary1/10 border-secondary2"
                    : "border-text2/20 hover:border-secondary2 hover:bg-secondary1/5"
                  }`}
                onClick={(e) => handleEdit(e, monologue)}
              >
                <div className="md:col-span-2 flex items-start justify-between">
                  <h3 className="font-semibold text-sm md:text-base">
                    {monologue.title}
                  </h3>
                  <button
                    className="md:hidden text-text1 focus:outline-none transition-all duration-200 
                             bg-secondary2/10 hover:bg-secondary2/20 rounded-full p-1.5"
                    onClick={(e) => toggleInfo(monologue.monologue_id, e)}
                  >
                    {showInfo[monologue.monologue_id] ? (
                      <ChevronUp size={16} />
                    ) : (
                      <ChevronDown size={16} />
                    )}
                  </button>
                </div>
                <div className="hidden md:block md:col-span-1 text-sm">
                  {monologue.character}
                </div>
                <div className="hidden md:block md:col-span-1 text-sm">
                  {monologue.category}
                </div>
                <div className="md:col-span-3 text-sm space-y-2">
                  {showInfo[monologue.monologue_id] && (
                    <div className="md:hidden space-y-2 text-sm text-text2 mb-3">
                      <p>
                        <span className="font-medium">Character:</span>{" "}
                        {monologue.character}
                      </p>
                      <p>
                        <span className="font-medium">Type:</span>{" "}
                        {monologue.category}
                      </p>
                      <p>
                        <span className="font-medium">Gender:</span>{" "}
                        {monologue.gender}
                      </p>
                    </div>
                  )}
                  <div className="prose prose-sm max-w-none">
                    {expandedContent === monologue.monologue_id ? (
                      <div className="space-y-4">
                        <div>{monologue.content}</div>
                        <div className="flex items-center gap-2">
                          <button
                            className="text-text2 hover:text-text1 text-sm font-medium 
                                     focus:outline-none transition-colors duration-200 flex items-center gap-1"
                            onClick={(e) =>
                              toggleContent(e, monologue.monologue_id)
                            }
                          >
                            <ChevronUp size={16} />
                            Show less
                          </button>
                          {/* <button
                            className="text-text2 hover:text-text1 text-sm font-medium 
                                     focus:outline-none transition-colors duration-200 flex items-center gap-1"
                            onClick={(e) => handleEdit(e, monologue)}
                          >
                            <Edit2 size={16} />
                            Edit monologue
                          </button> */}
                        </div>
                      </div>
                    ) : (
                      <div className="space-y-2">
                        <div>{monologue.content.substring(0, 100)}...</div>
                        <button
                          className="text-text2 hover:text-text1 text-sm font-medium 
                                   focus:outline-none transition-colors duration-200 flex items-center gap-1"
                          onClick={(e) => handleEdit(e, monologue)}
                        >
                          <ChevronDown size={16} />
                          Read more
                        </button>
                      </div>
                    )}
                  </div>
                </div>
                <div className="hidden md:block md:col-span-1 text-sm">
                  {monologue.gender}
                </div>
                {isAdmin && (   
                  <div className="hidden md:block md:col-span-1 text-sm">
                    <Trash2 size={16} onClick={(e)=>{
                      e.preventDefault();
                      handleRemove(monologue.monologue_id);
                    }} />
                  </div>
                )}
              </div>
            ))}
          </div>
        )}
      </div>
        <Pagination
          loop
          initialPage={currentPage}
          total={totalPages}
          onChange={handlePageChange}
          page={currentPage}
        />
    </div>
  );
};

export default MonologueSelection;