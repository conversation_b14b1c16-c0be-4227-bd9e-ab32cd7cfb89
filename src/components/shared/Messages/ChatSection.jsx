import React, { useState, useEffect, useRef, useCallback } from "react";
import { useParams, useNavigate, useLocation } from "react-router-dom";
import { useDispatch, useSelector } from "react-redux";
import { Button } from "@/components/ui/button";
import { ArrowLeft, BadgeCheck, MessageSquare, Send } from "lucide-react";
import { Skeleton } from "@/components/ui/skeleton";
import { Textarea } from "@/components/ui/textarea";
import supabase, { subscribeToMessages } from "@/lib/supabase";
import {
  useGetChatByIdQuery,
  useGetChatsQuery,
} from "@/redux/features/chatApi";
import {
  addMessage,
  markMessageAsSeen,
  removeMessage,
  setCurrentChat,
  setMessages,
  updateMessage,
} from "@/redux/features/chatSlice";
import { debounce } from "lodash";
import { useGetCoachesQuery } from "@/redux/features/coachApi";
import RenderIfVisible from "react-render-if-visible";
import placeholderImage from "@/assets/images/blank-profile-picture.png";

const ChatSection = () => {
  // State and hooks
  const dispatch = useDispatch();
  const navigate = useNavigate();
  const { chatId } = useParams();
  const location = useLocation();
  const [message, setMessage] = useState("");
  const [page, setPage] = useState(1);
  const [isLoading, setIsLoading] = useState(false);
  const [hasMore, setHasMore] = useState(true);

  // Refs
  const messagesEndRef = useRef(null);
  const chatContainerRef = useRef(null);

  // Selectors
  const currentUserId = useSelector((state) => state.user.session?.user?.id);
  const onlineUsers = useSelector((state) => state.chats.onlineUsers);
  const messagesOp = useSelector((state) => state.chats.messages[chatId] || []);

  // Queries
  const {
    data: chatData,
    isLoading: isChatLoading,
    refetch,
  } = useGetChatByIdQuery(
    { chatId, page_no: page },
    {
      refetchOnMountOrArgChange: true,
    }
  );
  const { data: chats, refetch: refetchChats } = useGetChatsQuery();
  const { data: coaches } = useGetCoachesQuery();

  // Derived state
  const chatPartner =
    location.state?.searchResult ||
    location.state?.coachData ||
    chats?.find((chat) => chat.id === chatId)?.user_data;

  const partnerData =
    chatPartner ||
    coaches?.find((coach) => coach.user_id === chatData?.user_id) ||
    chats?.find((chat) => chat.id === chatId)?.user_data;

  const isCoach = coaches?.some(
    (coach) => coach?.user_id === partnerData?.user_id
  );

  const isPartnerOnline = chatPartner && onlineUsers[chatPartner.id];

  // Functions
  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: "smooth" });
  };

  const handleSendMessage = useCallback(async () => {
    if (message.trim()) {
      const tempId = `temp-${Date.now()}-${Math.random()
        .toString(36)
        .substr(2, 9)}`;
      const optimisticMessage = {
        id: tempId,
        content: message,
        chat_id: chatId,
        sender_id: currentUserId,
        created_at: new Date().toISOString(),
        pending: true,
      };

      dispatch(addMessage({ chatId, message: optimisticMessage }));
      setMessage("");

      try {
        const { data, error } = await supabase
          .from("messages")
          .insert({
            content: message,
            chat_id: chatId,
            sender_id: currentUserId,
            created_at: new Date().toISOString(),
          })
          .select();

        if (error) {
          console.error("Failed to send message:", error);
          dispatch(removeMessage({ chatId, messageId: tempId }));
        } else {
          dispatch(
            updateMessage({
              chatId,
              messageId: tempId,
              updates: { ...data[0], pending: false, id: data[0].id },
            })
          );
          refetchChats();
        }
      } catch (error) {
        console.error("Failed to send message:", error);
        dispatch(removeMessage({ chatId, messageId: tempId }));
      }
    }
  }, [message, chatId, currentUserId, dispatch, refetchChats]);

  const debouncedSendMessage = debounce(handleSendMessage, 300);

  const handleMarkAsRead = async () => {
    try {
      const { data, error } = await supabase
        .from("messages")
        .update({ seen: true })
        .eq("chat_id", chatId)
        .neq("sender_id", currentUserId.toString());
      if (error) {
        console.error("Failed to mark as read:", error);
      } else if (data && data.length > 0) {
        data.forEach((message) => {
          dispatch(markMessageAsSeen({ chatId, messageId: message.id }));
        });
      } else {
        console.log("No messages found to mark as read.");
      }
    } catch (error) {
      console.error("Failed to mark as read:", error);
    }
  };

  const handleKeyDown = (e) => {
    if (e.key === "Enter" && !e.shiftKey) {
      e.preventDefault();
      debouncedSendMessage();
    }
  };

  const formatMessage = (text) => {
    return text.split("\n").map((line, index) => (
      <React.Fragment key={index}>
        {line}
        {index !== text.split("\n").length - 1 && <br />}
      </React.Fragment>
    ));
  };

  const handleNavigation = useCallback(() => {
    navigate("/messages");
  }, [navigate]);

  const loadMoreMessages = useCallback(() => {
    if (!isLoading && hasMore) {
      setIsLoading(true);
      setPage((prevPage) => prevPage + 1);
      refetch().then(() => setIsLoading(false));
    }
  }, [isLoading, hasMore, refetch]);

  const handleScroll = useCallback(() => {
    if (chatContainerRef.current) {
      const { scrollTop, scrollHeight, clientHeight } =
        chatContainerRef.current;
      if (scrollTop === 0 && !isLoading && hasMore) {
        loadMoreMessages();
      }
    }
  }, [loadMoreMessages, isLoading, hasMore]);

  // Effects
  useEffect(() => {
    scrollToBottom();
  }, [messagesOp]);

  useEffect(() => {
    if (chatData && chatData.length > 0) {
      const newMessages = chatData.filter(
        (newMsg) =>
          !messagesOp.some((existingMsg) => existingMsg.id.toString === newMsg.id.toString)
      );

      if (newMessages.length > 0) {
        dispatch(
          setMessages({
            chatId,
            messages: [...newMessages, ...messagesOp],
          })
        );
      }

      setHasMore(chatData.length === 20);
    }
  }, [chatData, chatId, dispatch, messagesOp]);

  useEffect(() => {
    if (chatId) {
      dispatch(setCurrentChat(chatId));
      const messageSubscription = subscribeToMessages(chatId, (payload) => {
        if (payload.new) {
          dispatch(addMessage({ chatId, message: payload.new }));
          refetchChats();
        }
      });

      return () => {
        messageSubscription.unsubscribe();
        dispatch(setCurrentChat(null));
      };
    }
  }, [dispatch, chatId, refetchChats]);

  useEffect(() => {
    const chatContainer = chatContainerRef.current;
    if (chatContainer) {
      chatContainer.addEventListener("scroll", handleScroll);
      return () => chatContainer.removeEventListener("scroll", handleScroll);
    }
  }, [handleScroll]);

  // Components
  const NoMessagesYet = () => (
    <div className="flex flex-col items-center justify-center mt-60 text-gray-400">
      <MessageSquare size={64} className="mb-4 animate-bounce" />
      <h3 className="text-xl font-semibold mb-2">No messages yet</h3>
      <p className="text-sm text-center max-w-md">
        Be the first to break the ice! Send a message to start the conversation.
      </p>
    </div>
  );

  if (isChatLoading) {
    return (
      <div className="flex flex-col h-screen px-4 lg:px-0">
        <div className="flex gap-4 items-center py-4">
          <Skeleton className="w-10 h-10 rounded-full" />
          <Skeleton className="h-6 w-32" />
        </div>

        <div className="flex-grow overflow-y-auto bg-chatSec rounded-t-lg discord-scrollbar">
          <div className="flex flex-col p-4 min-h-full">
            {[...Array(13)].map((_, index) => (
              <div
                key={index}
                className={`mb-4 flex ${
                  index % 2 === 0 ? "justify-start" : "justify-end"
                }`}
              >
                <Skeleton className="h-10 w-48 rounded-lg" />
              </div>
            ))}
          </div>
        </div>

        <div className="bg-primary1 p-4 rounded-b-lg">
          <div className="flex justify-between gap-2 items-center">
            <Skeleton className="h-10 flex-grow rounded-md" />
            <Skeleton className="h-10 w-20 rounded-md" />
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="flex flex-col h-screen">
      <div className="flex items-center gap-4 p-4 bg-primary1 sticky top-0 z-10">
        <Button
          className="px-3 py-2 bg-hover1 hover:bg-hover1"
          onClick={handleNavigation}
        >
          <ArrowLeft size={16} />
        </Button>
        {partnerData && (
          <div className="flex items-center gap-4">
            <img
              src={
                partnerData.profile_picture_url ||
                placeholderImage
              }
              alt={partnerData.metadata?.full_name || partnerData.full_name}
              className="w-12 h-12 rounded-full object-cover"
            />

            <div>
              <div className="font-semibold flex items-center justify-center gap-2">
                {partnerData.metadata?.full_name || partnerData.full_name}
                {isCoach && (
                  <BadgeCheck
                    size={20}
                    className=" text-green-500 bg-primary1 rounded-full"
                  />
                )}
              </div>

              <p
                className={`text-sm ${
                  isPartnerOnline ? "text-green-500" : "text-gray-400"
                }`}
              >
                {isPartnerOnline ? "Online" : "Offline"}
              </p>
            </div>
          </div>
        )}
      </div>

      <div
        ref={chatContainerRef}
        className="flex-grow overflow-y-auto bg-chatSec discord-scrollbar p-4"
      >
        {isLoading && (
          <div className="flex justify-center my-2">
            <Skeleton className="h-10 w-48 rounded-lg" />
          </div>
        )}
        <div className="flex flex-col gap-4">
          {messagesOp?.length > 0 ? (
            [...messagesOp]
              .sort((a, b) => new Date(a.created_at) - new Date(b.created_at))
              .map((msg) => (
                <RenderIfVisible
                  key={`${msg.id}-${msg.created_at}`}
                  defaultHeight={150}
                >
                  <div
                    className={`flex ${
                      msg.sender_id === currentUserId
                        ? "justify-end"
                        : "justify-start"
                    }`}
                  >
                    <div
                      className={`px-4 py-2 rounded-lg max-w-[70%] ${
                        msg.sender_id === currentUserId
                          ? "bg-navItemActive text-white"
                          : "bg-gray-700 text-white"
                      }`}
                    >
                      <p className="whitespace-pre-wrap">
                        {formatMessage(msg.content)}
                      </p>
                      <p className="mt-1 text-xs opacity-50">
                        {new Date(msg.created_at).toLocaleString()}
                      </p>
                      {msg.seen && (
                        <span className="text-xs opacity-50 ml-2">Seen</span>
                      )}
                    </div>
                  </div>
                </RenderIfVisible>
              ))
          ) : (
            <NoMessagesYet />
          )}
          <div ref={messagesEndRef} />
        </div>
      </div>

      <div className="bg-primary1 p-4 sticky bottom-0">
        <div className="flex gap-2 items-center">
          <Textarea
            type="text"
            placeholder="Type a message"
            className="bg-chatSec text-text1 p-2 rounded-md border border-gray-700 flex-grow text-sm focus:outline-none focus:ring-2 focus:ring-white discord-scrollbar"
            value={message}
            onChange={(e) => setMessage(e.target.value)}
            onFocus={handleMarkAsRead}
            onKeyDown={handleKeyDown}
          />
          <Button
            className="bg-navItemActive hover:bg-text1 hover:text-primary1"
            onClick={handleSendMessage}
          >
            <Send size={16} />
          </Button>
        </div>
      </div>
    </div>
  );
};

export default ChatSection;
