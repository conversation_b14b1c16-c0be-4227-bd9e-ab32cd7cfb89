generator client {
  provider        = "prisma-client-js"
  previewFeatures = ["multiSchema"]
  binaryTargets   = ["native", "debian-openssl-3.0.x"]
}

datasource db {
  provider  = "postgresql"
  url       = env("DATABASE_URL")
  directUrl = env("DIRECT_URL")
  schemas   = ["auth", "public"]
}

/// This model or at least one of its fields has comments in the database, and requires an additional setup for migrations: Read more: https://pris.ly/d/database-comments
/// This model contains row level security and requires additional setup for migrations. Visit https://pris.ly/d/row-level-security for more info.
model audit_log_entries {
  instance_id String?   @db.Uuid
  id          String    @id @db.Uuid
  payload     Json?     @db.<PERSON><PERSON>
  created_at  DateTime? @db.Timestamptz(6)
  ip_address  String    @default("") @db.VarChar(64)

  @@index([instance_id], map: "audit_logs_instance_id_idx")
  @@schema("auth")
}

/// This model or at least one of its fields has comments in the database, and requires an additional setup for migrations: Read more: https://pris.ly/d/database-comments
/// This model contains row level security and requires additional setup for migrations. Visit https://pris.ly/d/row-level-security for more info.
model flow_state {
  id                     String                @id @db.Uuid
  user_id                String?               @db.Uuid
  auth_code              String
  code_challenge_method  code_challenge_method
  code_challenge         String
  provider_type          String
  provider_access_token  String?
  provider_refresh_token String?
  created_at             DateTime?             @db.Timestamptz(6)
  updated_at             DateTime?             @db.Timestamptz(6)
  authentication_method  String
  auth_code_issued_at    DateTime?             @db.Timestamptz(6)
  saml_relay_states      saml_relay_states[]

  @@index([created_at(sort: Desc)])
  @@index([auth_code], map: "idx_auth_code")
  @@index([user_id, authentication_method], map: "idx_user_id_auth_method")
  @@schema("auth")
}

/// This model or at least one of its fields has comments in the database, and requires an additional setup for migrations: Read more: https://pris.ly/d/database-comments
/// This model contains row level security and requires additional setup for migrations. Visit https://pris.ly/d/row-level-security for more info.
model identities {
  provider_id     String
  user_id         String    @db.Uuid
  identity_data   Json
  provider        String
  last_sign_in_at DateTime? @db.Timestamptz(6)
  created_at      DateTime? @db.Timestamptz(6)
  updated_at      DateTime? @db.Timestamptz(6)
  email           String?   @default(dbgenerated("lower((identity_data ->> 'email'::text))"))
  id              String    @id @default(dbgenerated("gen_random_uuid()")) @db.Uuid
  users           users     @relation(fields: [user_id], references: [id], onDelete: Cascade, onUpdate: NoAction)

  @@unique([provider_id, provider], map: "identities_provider_id_provider_unique")
  @@index([email])
  @@index([user_id])
  @@schema("auth")
}

/// This model or at least one of its fields has comments in the database, and requires an additional setup for migrations: Read more: https://pris.ly/d/database-comments
/// This model contains row level security and requires additional setup for migrations. Visit https://pris.ly/d/row-level-security for more info.
model instances {
  id              String    @id @db.Uuid
  uuid            String?   @db.Uuid
  raw_base_config String?
  created_at      DateTime? @db.Timestamptz(6)
  updated_at      DateTime? @db.Timestamptz(6)

  @@schema("auth")
}

/// This model or at least one of its fields has comments in the database, and requires an additional setup for migrations: Read more: https://pris.ly/d/database-comments
/// This model contains row level security and requires additional setup for migrations. Visit https://pris.ly/d/row-level-security for more info.
model mfa_amr_claims {
  session_id            String   @db.Uuid
  created_at            DateTime @db.Timestamptz(6)
  updated_at            DateTime @db.Timestamptz(6)
  authentication_method String
  id                    String   @id(map: "amr_id_pk") @db.Uuid
  sessions              sessions @relation(fields: [session_id], references: [id], onDelete: Cascade, onUpdate: NoAction)

  @@unique([session_id, authentication_method], map: "mfa_amr_claims_session_id_authentication_method_pkey")
  @@schema("auth")
}

/// This model or at least one of its fields has comments in the database, and requires an additional setup for migrations: Read more: https://pris.ly/d/database-comments
/// This model contains row level security and requires additional setup for migrations. Visit https://pris.ly/d/row-level-security for more info.
model mfa_challenges {
  id                     String      @id @db.Uuid
  factor_id              String      @db.Uuid
  created_at             DateTime    @db.Timestamptz(6)
  verified_at            DateTime?   @db.Timestamptz(6)
  ip_address             String      @db.Inet
  otp_code               String?
  web_authn_session_data Json?
  mfa_factors            mfa_factors @relation(fields: [factor_id], references: [id], onDelete: Cascade, onUpdate: NoAction, map: "mfa_challenges_auth_factor_id_fkey")

  @@index([created_at(sort: Desc)], map: "mfa_challenge_created_at_idx")
  @@schema("auth")
}

/// This model or at least one of its fields has comments in the database, and requires an additional setup for migrations: Read more: https://pris.ly/d/database-comments
/// This model contains row level security and requires additional setup for migrations. Visit https://pris.ly/d/row-level-security for more info.
model mfa_factors {
  id                   String           @id @db.Uuid
  user_id              String           @db.Uuid
  friendly_name        String?
  factor_type          factor_type
  status               factor_status
  created_at           DateTime         @db.Timestamptz(6)
  updated_at           DateTime         @db.Timestamptz(6)
  secret               String?
  phone                String?
  last_challenged_at   DateTime?        @unique @db.Timestamptz(6)
  web_authn_credential Json?
  web_authn_aaguid     String?          @db.Uuid
  mfa_challenges       mfa_challenges[]
  users                users            @relation(fields: [user_id], references: [id], onDelete: Cascade, onUpdate: NoAction)

  @@unique([user_id, phone], map: "unique_phone_factor_per_user")
  @@index([user_id, created_at], map: "factor_id_created_at_idx")
  @@index([user_id])
  @@schema("auth")
}

/// This table contains check constraints and requires additional setup for migrations. Visit https://pris.ly/d/check-constraints for more info.
/// This model contains row level security and requires additional setup for migrations. Visit https://pris.ly/d/row-level-security for more info.
model one_time_tokens {
  id         String              @id @db.Uuid
  user_id    String              @db.Uuid
  token_type one_time_token_type
  token_hash String
  relates_to String
  created_at DateTime            @default(now()) @db.Timestamp(6)
  updated_at DateTime            @default(now()) @db.Timestamp(6)
  users      users               @relation(fields: [user_id], references: [id], onDelete: Cascade, onUpdate: NoAction)

  @@unique([user_id, token_type])
  @@index([relates_to], map: "one_time_tokens_relates_to_hash_idx", type: Hash)
  @@index([token_hash], map: "one_time_tokens_token_hash_hash_idx", type: Hash)
  @@schema("auth")
}

/// This model or at least one of its fields has comments in the database, and requires an additional setup for migrations: Read more: https://pris.ly/d/database-comments
/// This model contains row level security and requires additional setup for migrations. Visit https://pris.ly/d/row-level-security for more info.
model refresh_tokens {
  instance_id String?   @db.Uuid
  id          BigInt    @id @default(autoincrement())
  token       String?   @unique(map: "refresh_tokens_token_unique") @db.VarChar(255)
  user_id     String?   @db.VarChar(255)
  revoked     Boolean?
  created_at  DateTime? @db.Timestamptz(6)
  updated_at  DateTime? @db.Timestamptz(6)
  parent      String?   @db.VarChar(255)
  session_id  String?   @db.Uuid
  sessions    sessions? @relation(fields: [session_id], references: [id], onDelete: Cascade, onUpdate: NoAction)

  @@index([instance_id])
  @@index([instance_id, user_id])
  @@index([parent])
  @@index([session_id, revoked])
  @@index([updated_at(sort: Desc)])
  @@schema("auth")
}

/// This table contains check constraints and requires additional setup for migrations. Visit https://pris.ly/d/check-constraints for more info.
/// This model or at least one of its fields has comments in the database, and requires an additional setup for migrations: Read more: https://pris.ly/d/database-comments
/// This model contains row level security and requires additional setup for migrations. Visit https://pris.ly/d/row-level-security for more info.
model saml_providers {
  id                String        @id @db.Uuid
  sso_provider_id   String        @db.Uuid
  entity_id         String        @unique
  metadata_xml      String
  metadata_url      String?
  attribute_mapping Json?
  created_at        DateTime?     @db.Timestamptz(6)
  updated_at        DateTime?     @db.Timestamptz(6)
  name_id_format    String?
  sso_providers     sso_providers @relation(fields: [sso_provider_id], references: [id], onDelete: Cascade, onUpdate: NoAction)

  @@index([sso_provider_id])
  @@schema("auth")
}

/// This table contains check constraints and requires additional setup for migrations. Visit https://pris.ly/d/check-constraints for more info.
/// This model or at least one of its fields has comments in the database, and requires an additional setup for migrations: Read more: https://pris.ly/d/database-comments
/// This model contains row level security and requires additional setup for migrations. Visit https://pris.ly/d/row-level-security for more info.
model saml_relay_states {
  id              String        @id @db.Uuid
  sso_provider_id String        @db.Uuid
  request_id      String
  for_email       String?
  redirect_to     String?
  created_at      DateTime?     @db.Timestamptz(6)
  updated_at      DateTime?     @db.Timestamptz(6)
  flow_state_id   String?       @db.Uuid
  flow_state      flow_state?   @relation(fields: [flow_state_id], references: [id], onDelete: Cascade, onUpdate: NoAction)
  sso_providers   sso_providers @relation(fields: [sso_provider_id], references: [id], onDelete: Cascade, onUpdate: NoAction)

  @@index([created_at(sort: Desc)])
  @@index([for_email])
  @@index([sso_provider_id])
  @@schema("auth")
}

/// This model or at least one of its fields has comments in the database, and requires an additional setup for migrations: Read more: https://pris.ly/d/database-comments
/// This model contains row level security and requires additional setup for migrations. Visit https://pris.ly/d/row-level-security for more info.
model schema_migrations {
  version String @id @db.VarChar(255)

  @@schema("auth")
}

/// This model or at least one of its fields has comments in the database, and requires an additional setup for migrations: Read more: https://pris.ly/d/database-comments
/// This model contains row level security and requires additional setup for migrations. Visit https://pris.ly/d/row-level-security for more info.
model sessions {
  id             String           @id @db.Uuid
  user_id        String           @db.Uuid
  created_at     DateTime?        @db.Timestamptz(6)
  updated_at     DateTime?        @db.Timestamptz(6)
  factor_id      String?          @db.Uuid
  aal            aal_level?
  not_after      DateTime?        @db.Timestamptz(6)
  refreshed_at   DateTime?        @db.Timestamp(6)
  user_agent     String?
  ip             String?          @db.Inet
  tag            String?
  mfa_amr_claims mfa_amr_claims[]
  refresh_tokens refresh_tokens[]
  users          users            @relation(fields: [user_id], references: [id], onDelete: Cascade, onUpdate: NoAction)

  @@index([not_after(sort: Desc)])
  @@index([user_id])
  @@index([user_id, created_at], map: "user_id_created_at_idx")
  @@schema("auth")
}

/// This table contains check constraints and requires additional setup for migrations. Visit https://pris.ly/d/check-constraints for more info.
/// This model or at least one of its fields has comments in the database, and requires an additional setup for migrations: Read more: https://pris.ly/d/database-comments
/// This model contains row level security and requires additional setup for migrations. Visit https://pris.ly/d/row-level-security for more info.
/// This model contains an expression index which requires additional setup for migrations. Visit https://pris.ly/d/expression-indexes for more info.
model sso_domains {
  id              String        @id @db.Uuid
  sso_provider_id String        @db.Uuid
  domain          String
  created_at      DateTime?     @db.Timestamptz(6)
  updated_at      DateTime?     @db.Timestamptz(6)
  sso_providers   sso_providers @relation(fields: [sso_provider_id], references: [id], onDelete: Cascade, onUpdate: NoAction)

  @@index([sso_provider_id])
  @@schema("auth")
}

/// This table contains check constraints and requires additional setup for migrations. Visit https://pris.ly/d/check-constraints for more info.
/// This model or at least one of its fields has comments in the database, and requires an additional setup for migrations: Read more: https://pris.ly/d/database-comments
/// This model contains row level security and requires additional setup for migrations. Visit https://pris.ly/d/row-level-security for more info.
/// This model contains an expression index which requires additional setup for migrations. Visit https://pris.ly/d/expression-indexes for more info.
model sso_providers {
  id                String              @id @db.Uuid
  resource_id       String?
  created_at        DateTime?           @db.Timestamptz(6)
  updated_at        DateTime?           @db.Timestamptz(6)
  saml_providers    saml_providers[]
  saml_relay_states saml_relay_states[]
  sso_domains       sso_domains[]

  @@schema("auth")
}

/// This table contains check constraints and requires additional setup for migrations. Visit https://pris.ly/d/check-constraints for more info.
/// This model or at least one of its fields has comments in the database, and requires an additional setup for migrations: Read more: https://pris.ly/d/database-comments
/// This model contains row level security and requires additional setup for migrations. Visit https://pris.ly/d/row-level-security for more info.
/// This model contains an expression index which requires additional setup for migrations. Visit https://pris.ly/d/expression-indexes for more info.
model users {
  instance_id                 String?           @db.Uuid
  id                          String            @id @db.Uuid
  aud                         String?           @db.VarChar(255)
  role                        String?           @db.VarChar(255)
  email                       String?           @db.VarChar(255)
  encrypted_password          String?           @db.VarChar(255)
  email_confirmed_at          DateTime?         @db.Timestamptz(6)
  invited_at                  DateTime?         @db.Timestamptz(6)
  confirmation_token          String?           @db.VarChar(255)
  confirmation_sent_at        DateTime?         @db.Timestamptz(6)
  recovery_token              String?           @db.VarChar(255)
  recovery_sent_at            DateTime?         @db.Timestamptz(6)
  email_change_token_new      String?           @db.VarChar(255)
  email_change                String?           @db.VarChar(255)
  email_change_sent_at        DateTime?         @db.Timestamptz(6)
  last_sign_in_at             DateTime?         @db.Timestamptz(6)
  raw_app_meta_data           Json?
  raw_user_meta_data          Json?
  is_super_admin              Boolean?
  created_at                  DateTime?         @db.Timestamptz(6)
  updated_at                  DateTime?         @db.Timestamptz(6)
  phone                       String?           @unique
  phone_confirmed_at          DateTime?         @db.Timestamptz(6)
  phone_change                String?           @default("")
  phone_change_token          String?           @default("") @db.VarChar(255)
  phone_change_sent_at        DateTime?         @db.Timestamptz(6)
  confirmed_at                DateTime?         @default(dbgenerated("LEAST(email_confirmed_at, phone_confirmed_at)")) @db.Timestamptz(6)
  email_change_token_current  String?           @default("") @db.VarChar(255)
  email_change_confirm_status Int?              @default(0) @db.SmallInt
  banned_until                DateTime?         @db.Timestamptz(6)
  reauthentication_token      String?           @default("") @db.VarChar(255)
  reauthentication_sent_at    DateTime?         @db.Timestamptz(6)
  is_sso_user                 Boolean           @default(false)
  deleted_at                  DateTime?         @db.Timestamptz(6)
  is_anonymous                Boolean           @default(false)
  identities                  identities[]
  mfa_factors                 mfa_factors[]
  one_time_tokens             one_time_tokens[]
  sessions                    sessions[]
  chats_chats_user_1Tousers   chats[]           @relation("chats_user_1Tousers")
  chats_chats_user_2Tousers   chats[]           @relation("chats_user_2Tousers")
  coaches                     coaches[]
  comments                    comments[]
  messages                    messages[]
  post_upvotes                post_upvotes[]
  track_events                track_events[]
  users                       public_users[]

  @@index([instance_id])
  @@index([is_anonymous])
  @@schema("auth")
}

model posts {
  post_id         BigInt         @id @default(autoincrement())
  created_at      DateTime       @default(now()) @db.Timestamptz(6)
  content         String?
  is_private      Boolean?       @default(false)
  url             String?
  parent_id       BigInt?
  view_token      String?        @default(dbgenerated("gen_random_uuid()")) @db.Uuid
  user_row_id     BigInt?
  hume_job_id     String?        // HumeAI job ID for webhook lookups
  hume_analysis   Json?          // HumeAI expression analysis results
  ai_insights     Json?          // Azure OpenAI generated insights
  analysis_status String?        // 'analyzing', 'completed', 'failed'
  comments        comments[]
  post_upvotes    post_upvotes[]
  posts           posts?         @relation("postsToposts", fields: [parent_id], references: [post_id])
  other_posts     posts[]        @relation("postsToposts")
  users           public_users?  @relation(fields: [user_row_id], references: [id], onDelete: NoAction, onUpdate: NoAction)

  @@index([hume_job_id])
  @@schema("public")
}

/// This model contains row level security and requires additional setup for migrations. Visit https://pris.ly/d/row-level-security for more info.
model comments {
  comment_id     BigInt         @id @default(autoincrement())
  created_at     DateTime       @default(now()) @db.Timestamptz(6)
  post_id        BigInt?
  parent_id      BigInt?
  comment        String?
  is_bad         Boolean?
  user_id        String?        @db.Uuid
  bad_comments   bad_comments[]
  comments       comments?      @relation("commentsTocomments", fields: [parent_id], references: [comment_id], onDelete: Cascade)
  other_comments comments[]     @relation("commentsTocomments")
  posts          posts?         @relation(fields: [post_id], references: [post_id], onDelete: Cascade)
  users          users?         @relation(fields: [user_id], references: [id], onDelete: Cascade)

  @@schema("public")
}

/// This model contains row level security and requires additional setup for migrations. Visit https://pris.ly/d/row-level-security for more info.
model monologues {
  monologue_id BigInt   @id @default(autoincrement())
  created_at   DateTime @default(now()) @db.Timestamptz(6)
  content      String?
  category     String?  @default("NONE")
  ai_generated Boolean?
  title        String?
  character    String?
  gender       String?
  chip         String?
  deleted      Boolean? @default(false)

  @@schema("public")
}

/// This model contains row level security and requires additional setup for migrations. Visit https://pris.ly/d/row-level-security for more info.
model plans {
  plan_id            BigInt         @id @default(autoincrement())
  created_at         DateTime       @default(now()) @db.Timestamptz(6)
  plan_name          String?
  plan_price_monthly Decimal?       @db.Decimal
  plan_price_annual  Decimal?       @db.Decimal
  plan_price_id      String?
  plan_product_id    String?
  description        String?
  type               Int?           @default(1) @db.SmallInt
  status             Int?           @default(1) @db.SmallInt
  expire_date        DateTime?      @db.Date
  users              public_users[]

  @@schema("public")
}

/// This model contains row level security and requires additional setup for migrations. Visit https://pris.ly/d/row-level-security for more info.
model post_upvotes {
  upvote_id  BigInt   @default(autoincrement())
  created_at DateTime @default(now()) @db.Timestamptz(6)
  post_id    BigInt
  user_id    String   @db.Uuid
  posts      posts    @relation(fields: [post_id], references: [post_id], onDelete: Cascade)
  users      users    @relation(fields: [user_id], references: [id], onDelete: Cascade)

  @@id([post_id, user_id])
  @@schema("public")
}

/// This model contains row level security and requires additional setup for migrations. Visit https://pris.ly/d/row-level-security for more info.
model public_users {
  id                      BigInt    @id @default(autoincrement())
  created_at              DateTime  @default(now()) @db.Timestamptz(6)
  user_id                 String?   @db.Uuid
  email                   String?
  metadata                Json?
  plan_in_use             BigInt?
  stripe_customer_id      String?
  is_banned               Boolean?  @default(false)
  profile_picture_url     String?
  profile_description     String?
  subscription_id         String?
  subscription_expires_at DateTime? @db.Date
  posts                   posts[]
  plans                   plans?    @relation(fields: [plan_in_use], references: [plan_id], onDelete: NoAction, onUpdate: NoAction)
  users                   users?    @relation(fields: [user_id], references: [id], onDelete: Cascade)

  @@map("users")
  @@schema("public")
}

/// This model contains row level security and requires additional setup for migrations. Visit https://pris.ly/d/row-level-security for more info.
model bad_comments {
  id         BigInt    @id(map: "obscene_comments_pkey") @default(autoincrement())
  created_at DateTime  @default(now()) @db.Timestamptz(6)
  comment_id BigInt?
  comments   comments? @relation(fields: [comment_id], references: [comment_id], onDelete: Cascade, map: "obscene_comments_comment_id_fkey")

  @@schema("public")
}

/// This model contains row level security and requires additional setup for migrations. Visit https://pris.ly/d/row-level-security for more info.
model chats {
  id                        BigInt     @id @default(autoincrement())
  created_at                DateTime   @default(now()) @db.Timestamptz(6)
  user_1                    String?    @db.Uuid
  user_2                    String?    @db.Uuid
  deleted_by_user_1         Boolean    @default(true)
  deleted_by_user_2         Boolean    @default(true)
  updated_at                DateTime?  @db.Timestamptz(6)
  users_chats_user_1Tousers users?     @relation("chats_user_1Tousers", fields: [user_1], references: [id], onDelete: NoAction, onUpdate: NoAction)
  users_chats_user_2Tousers users?     @relation("chats_user_2Tousers", fields: [user_2], references: [id], onDelete: NoAction, onUpdate: NoAction)
  messages                  messages[]

  @@schema("public")
}

/// This model contains row level security and requires additional setup for migrations. Visit https://pris.ly/d/row-level-security for more info.
model messages {
  id                BigInt   @id @default(autoincrement())
  created_at        DateTime @default(now()) @db.Timestamptz(6)
  chat_id           BigInt?
  sender_id         String?  @db.Uuid
  content           String
  seen              Boolean? @default(false)
  visible_to_user_1 Boolean? @default(true)
  visible_to_user_2 Boolean? @default(true)
  chats             chats?   @relation(fields: [chat_id], references: [id], onDelete: Cascade)
  users             users?   @relation(fields: [sender_id], references: [id], onDelete: NoAction, onUpdate: NoAction)

  @@schema("public")
}

/// This model contains row level security and requires additional setup for migrations. Visit https://pris.ly/d/row-level-security for more info.
model coaches {
  id                  BigInt   @id @default(autoincrement())
  created_at          DateTime @default(now()) @db.Timestamptz(6)
  verified            Boolean? @default(false)
  profile_description String?
  interview_status    String?
  expertise           String[]
  user_id             String?  @db.Uuid
  resumes             String[]
  headshots           String[]
  voice_reels         String[]
  video_reels         String[]
  email               String?
  metadata            Json?
  status              String?  @default("Available")
  application_status  String?  @default("Pending")
  users               users?   @relation(fields: [user_id], references: [id], onDelete: NoAction, onUpdate: NoAction)

  @@schema("public")
}

model documents {
  id        BigInt                 @id @default(autoincrement())
  content   String?
  metadata  Json?
  embedding Unsupported("vector")?

  @@schema("public")
}

/// This model contains row level security and requires additional setup for migrations. Visit https://pris.ly/d/row-level-security for more info.
model waitlist {
  id         BigInt   @id @default(autoincrement())
  created_at DateTime @default(now()) @db.Timestamptz(6)
  email      String?

  @@schema("public")
}

model track_events {
  id               String    @id(map: "login_events_pkey") @default(dbgenerated("uuid_generate_v4()")) @db.Uuid
  user_id          String?   @db.Uuid
  email            String?
  created_at       DateTime? @default(now()) @db.Timestamptz(6)
  headers          String?
  event            String?
  data             Json?
  interaction_time String?   @default("0")
  is_flagged       Boolean?  @default(false)
  users            users?    @relation(fields: [user_id], references: [id], onDelete: Cascade, onUpdate: NoAction, map: "login_events_user_id_fkey")

  @@schema("public")
}

/// The underlying table does not contain a valid unique identifier and can therefore currently not be handled by Prisma Client.
model monologues_copy {
  monologue_id BigInt?
  created_at   DateTime? @db.Timestamptz(6)
  content      String?
  category     String?
  ai_generated Boolean?
  title        String?
  character    String?
  gender       String?
  chip         String?

  @@ignore
  @@schema("public")
}

model survey {
  id                        Int       @id @default(autoincrement())
  userid                    String?   @db.Uuid
  email                     String?
  usability_rating          Int
  overall_experience_rating Int
  features_description      String?
  created_at                DateTime? @default(now()) @db.Timestamp(6)
  full_name                 String?   @db.VarChar

  @@schema("public")
}

model contact_us {
  id         Int       @id @default(autoincrement())
  full_name  String?
  email      String
  phone      String
  message    String?
  created_at DateTime? @default(now()) @db.Timestamp(6)

  @@schema("public")
}

enum aal_level {
  aal1
  aal2
  aal3

  @@schema("auth")
}

enum code_challenge_method {
  s256
  plain

  @@schema("auth")
}

enum factor_status {
  unverified
  verified

  @@schema("auth")
}

enum factor_type {
  totp
  webauthn
  phone

  @@schema("auth")
}

enum one_time_token_type {
  confirmation_token
  reauthentication_token
  recovery_token
  email_change_token_new
  email_change_token_current
  phone_change_token

  @@schema("auth")
}
