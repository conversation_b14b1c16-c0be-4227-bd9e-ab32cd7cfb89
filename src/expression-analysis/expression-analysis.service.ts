import { Injectable, Logger } from '@nestjs/common';
import { DatabaseService } from '../database/database.service';
import { HumeAiService } from '../services/hume-ai.service';
import { AzureOpenAiService } from '../services/azure-openai.service';
import { CustomResponse } from '../utils/CustomResponse';
import { HttpStatus } from '@nestjs/common';

@Injectable()
export class ExpressionAnalysisService {
  private readonly logger = new Logger(ExpressionAnalysisService.name);

  constructor(
    private readonly dbService: DatabaseService,
    private readonly humeAiService: HumeAiService,
    private readonly azureOpenAiService: AzureOpenAiService,
  ) {}

  /**
   * Start expression analysis for a video post
   * Called after video upload in posts.service.ts
   */
  async startAnalysisForPost(
    postId: number,
    videoUrl: string,
    postContent?: string
  ): Promise<CustomResponse> {
    try {
      this.logger.log(`Starting expression analysis for post ${postId}`);

      // Update post status to analyzing (immediately when job starts)
      await this.updatePostAnalysisStatus(postId, 'analyzing');

      // Start HumeAI analysis
      const { job_id } = await this.humeAiService.startAnalysis(videoUrl);

      // Store the job ID in the post for tracking
      await this.updatePostHumeJobId(postId, job_id);
      await this.updatePostHumeData(postId, { job_id, status: 'analyzing' });

      this.logger.log(`HumeAI job ${job_id} started for post ${postId}`);

      return {
        data: { job_id, status: 'analyzing' },
        error: false,
        message: 'Expression analysis started successfully',
        status: HttpStatus.OK,
      };
    } catch (error) {
      this.logger.error(`Failed to start analysis for post ${postId}: ${error.message}`, error.stack);

      await this.updatePostAnalysisStatus(postId, 'failed');

      return {
        data: null,
        error: true,
        message: 'Failed to start expression analysis',
        status: HttpStatus.INTERNAL_SERVER_ERROR,
      };
    }
  }

  /**
   * Retry failed analysis for a post
   */
  async retryAnalysisForPost(postId: number): Promise<CustomResponse> {
    try {
      this.logger.log(`Retrying expression analysis for post ${postId}`);

      // Get the post to retrieve video URL and content
      const prisma = this.dbService.getClient();
      const post = await prisma.posts.findUnique({
        where: { post_id: BigInt(postId) },
        select: { url: true, content: true, analysis_status: true }
      });

      if (!post) {
        return {
          data: null,
          error: true,
          message: 'Post not found',
          status: HttpStatus.NOT_FOUND,
        };
      }

      if (!post.url) {
        return {
          data: null,
          error: true,
          message: 'Post has no video to analyze',
          status: HttpStatus.BAD_REQUEST,
        };
      }

      // Clear previous failed analysis data
      await this.updatePostAnalysisStatus(postId, 'analyzing');
      await this.updatePostHumeData(postId, null);

      // Start new analysis
      return await this.startAnalysisForPost(postId, post.url, post.content);

    } catch (error) {
      this.logger.error(`Failed to retry analysis for post ${postId}: ${error.message}`, error.stack);

      return {
        data: null,
        error: true,
        message: 'Failed to retry expression analysis',
        status: HttpStatus.INTERNAL_SERVER_ERROR,
      };
    }
  }



  /**
   * Get analysis results for a post
   */
  async getPostAnalysis(postId: number): Promise<CustomResponse> {
    try {
      const prisma = this.dbService.getClient();
      
      const post = await prisma.posts.findUnique({
        where: { post_id: BigInt(postId) },
        select: {
          post_id: true,
          hume_analysis: true,
          ai_insights: true,
          analysis_status: true,
          created_at: true,
          url: true,
          content: true,
        },
      });

      if (!post) {
        return {
          data: null,
          error: true,
          message: 'Post not found',
          status: HttpStatus.NOT_FOUND,
        };
      }

      return {
        data: {
          post_id: post.post_id.toString(),
          analysis_status: post.analysis_status,
          hume_analysis: post.hume_analysis,
          ai_insights: post.ai_insights,
          created_at: post.created_at,
          has_video: !!post.url,
        },
        error: false,
        message: 'Analysis data retrieved successfully',
        status: HttpStatus.OK,
      };
    } catch (error) {
      this.logger.error(`Failed to get analysis for post ${postId}: ${error.message}`, error.stack);
      
      return {
        data: null,
        error: true,
        message: 'Failed to retrieve analysis data',
        status: HttpStatus.INTERNAL_SERVER_ERROR,
      };
    }
  }

  /**
   * Get user's analysis history
   */
  async getUserAnalysisHistory(userRowId: number): Promise<CustomResponse> {
    try {
      const prisma = this.dbService.getClient();
      
      const posts = await prisma.posts.findMany({
        where: { 
          user_row_id: userRowId,
          analysis_status: { not: null },
        },
        select: {
          post_id: true,
          analysis_status: true,
          ai_insights: true,
          created_at: true,
          content: true,
        },
        orderBy: { created_at: 'desc' },
        take: 20, // Last 20 analyzed posts
      });

      const analysisHistory = posts.map(post => ({
        post_id: post.post_id.toString(),
        status: post.analysis_status,
        created_at: post.created_at,
        content_preview: post.content?.substring(0, 100) + (post.content?.length > 100 ? '...' : ''),
        has_insights: !!post.ai_insights,
        quick_summary: (post.ai_insights as any)?.overall_assessment || null,
      }));

      return {
        data: analysisHistory,
        error: false,
        message: 'Analysis history retrieved successfully',
        status: HttpStatus.OK,
      };
    } catch (error) {
      this.logger.error(`Failed to get analysis history for user ${userRowId}: ${error.message}`, error.stack);
      
      return {
        data: null,
        error: true,
        message: 'Failed to retrieve analysis history',
        status: HttpStatus.INTERNAL_SERVER_ERROR,
      };
    }
  }

  /**
   * Update post analysis status
   */
  private async updatePostAnalysisStatus(postId: number, status: string): Promise<void> {
    const prisma = this.dbService.getClient();
    await prisma.posts.update({
      where: { post_id: BigInt(postId) },
      data: { analysis_status: status },
    });
  }

  /**
   * Update post with HumeAI job ID
   */
  private async updatePostHumeJobId(postId: number, jobId: string): Promise<void> {
    const supabase = this.dbService.getSupabaseClient();
    await supabase
      .from('posts')
      .update({ hume_job_id: jobId })
      .eq('post_id', postId);
  }

  /**
   * Update post with HumeAI data
   */
  private async updatePostHumeData(postId: number, humeData: any): Promise<void> {
    const prisma = this.dbService.getClient();
    await prisma.posts.update({
      where: { post_id: BigInt(postId) },
      data: { hume_analysis: humeData },
    });
  }



  /**
   * Health check for all services
   */
  async healthCheck(): Promise<CustomResponse> {
    try {
      const humeHealthy = await this.humeAiService.healthCheck();
      const azureHealthy = await this.azureOpenAiService.healthCheck();

      return {
        data: {
          hume_ai: humeHealthy,
          azure_openai: azureHealthy,
          overall: humeHealthy && azureHealthy,
        },
        error: false,
        message: 'Health check completed',
        status: HttpStatus.OK,
      };
    } catch (error) {
      return {
        data: { overall: false },
        error: true,
        message: 'Health check failed',
        status: HttpStatus.INTERNAL_SERVER_ERROR,
      };
    }
  }
}
