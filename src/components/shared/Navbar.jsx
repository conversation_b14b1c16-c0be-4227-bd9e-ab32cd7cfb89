import React, { useEffect, useState } from "react";
import { Link, NavLink, useLocation } from "react-router-dom";
import { Button } from "@/components/ui/button";
import { Sheet, SheetContent, SheetTrigger } from "@/components/ui/sheet";
import { Menu } from "lucide-react";
import LogoImg from '../../assets/images/logo.png';
import sidebarLogo from '../../../public/logo.png';

const Navbar = ({ children }) => {
  const [isOpen, setIsOpen] = useState(false);
  const location = useLocation();

  useEffect(() => {
    setIsOpen(false);
  }, [location]);

  useEffect(() => {
    const handleResize = () => {
      if (window.innerWidth >= 1024) {
        setIsOpen(false);
      }
    };

    window.addEventListener("resize", handleResize);
    return () => window.removeEventListener("resize", handleResize);
  }, []);

  useEffect(() => {
    const handleScroll = () => {
      const sections = ["home", "how-it-works", "features", "testimonials"];
      let current = "";

      for (const section of sections) {
        const element = document.getElementById(section);
        if (element) {
          const rect = element.getBoundingClientRect();
          if (rect.top <= 100 && rect.bottom >= 100) {
            current = section;
            break;
          }
        }
      }
    };

    window.addEventListener("scroll", handleScroll);
    return () => window.removeEventListener("scroll", handleScroll);
  }, []);

  const navItems = [
    { name: "Home", href: "/home" },
    { name: "About Us", href: "/about-us" },
    { name: "Features", href: "/features" },
    { name: "Pricing", href: "/pricing" },
    { name: "Contact Us", href: "/contact-us" },
  ];

  const NavLinks = ({ className = "" }) => (
    <>
      {
        navItems.map((item, index) => (
          <div key={index} className={`${className} hover:text-hover1 transition-colors duration-300`}>
            <NavLink to={item.href} className={({ isActive }) => isActive ? 'text-navItemActive' : ''}>{item.name}</NavLink>
          </div>
        ))
      }
    </>
  );

  return (
    <nav id="navbar" className='sticky top-0 z-50'>
      {children}
      <div className="bg-secondary2 text-text1 w-full h-[71px] flex item-center">
        <div className="w-full lg:px-10 px-5 mx-auto flex justify-between items-center">
          <div className="flex items-center">
            {/* Mobile Menu Trigger */}
            <Sheet open={isOpen} onOpenChange={setIsOpen}>
              <SheetTrigger asChild>
                <Button className="lg:hidden mr-2">
                  <Menu size={24} />
                </Button>
              </SheetTrigger>
              <SheetContent
                side="left"
                className="w-[300px] sm:w-[400px] bg-secondary2 border-r border-border1"
              >
                <div className="flex flex-col h-full">
                  <Link
                    to="/home"
                    className="flex flex-col items-center group mb-5 mx-auto"
                  >
                    <img
                      src={sidebarLogo}
                      className="invert mt-4 w-20 transition-all duration-1000 ease-in-out transform group-hover:scale-110 group-hover:rotate-3 animate-pulse"
                      alt="Audition Rooms"
                    />
                    <h1 className="mt-4 text-2xl font-bold text-center text-white tracking-wide">
                      Audition Rooms
                    </h1>
                  </Link>
                  <nav className="flex flex-col space-y-4 flex-grow">
                    <NavLinks
                      className="block py-2 text-lg text-center text-text1"
                      onClick={() => setIsOpen(false)}
                    />
                  </nav>
                  <div className="mt-auto space-y-4">
                    <Button
                      asChild
                      className="w-full bg-hover1 hover:bg-navItemActive text-text1"
                    >
                      <NavLink to="/signin">Login</NavLink>
                    </Button>
                    <Button
                      asChild
                      className="w-full bg-hover1 hover:bg-navItemActive text-text1"
                    >
                      <NavLink to="/signup">Sign Up</NavLink>
                    </Button>
                  </div>
                </div>
              </SheetContent>
            </Sheet>

            {/* Logo */}
            <Link
              to="/home"
              className="relative hidden lg:inline-block group h-[45px]"
            >
              <img
                src={LogoImg}
                className="w-full h-full"
                alt="Audition Rooms"
              />
            </Link>
            <span className="lg:hidden lg-hidden flex ml-4 text-2xl font-bold">
              <h1> AuditionRooms</h1>
            </span>
          </div>

          {/* Desktop Menu */}
          <div className="hidden lg:flex items-center space-x-6 ">
            <NavLinks />
            <Button
              asChild
              className="bg-hover1 hover:bg-navItemActive text-text1"
            >
              <NavLink to="/signin">Login</NavLink>
            </Button>
            <Button
              asChild
              className="bg-white hover:bg-navItemActive text-hover1"
            >
              <NavLink to="/signup">Sign Up</NavLink>
            </Button>
          </div>
        </div>
      </div>
    </nav>
  );
};

export default Navbar;
