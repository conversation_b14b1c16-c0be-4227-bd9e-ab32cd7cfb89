import * as React from "react";
import { Table, TableHeader, TableBody, TableRow, TableHead, TableCell } from "@/components/ui/table";
import { useGetAllSurveyQuery } from "@/redux/features/surveySlice";
import { useEffect, useState } from "react";
import { Pagination } from "@heroui/pagination";
import { Skeleton } from "@/components/ui/skeleton";

const SurveyPage = () => {
  const [currentPage, setCurrentPage] = useState(1);
  const itemsPerPage = 10;
  const [totalPages, setTotalPages] = useState(0);
  const { data, error, isLoading, refetch } = useGetAllSurveyQuery({ page: currentPage, limit: itemsPerPage });

  const handlePageChange = (pageNumber) => {
    setCurrentPage(pageNumber);
    window.scrollTo({ top: 0, behavior: 'smooth' });
  }

  useEffect(() => {
    refetch();
  }, [currentPage, refetch]);

  useEffect(() => {
    if (data?.count) {
      const lTotalPages = Math.ceil(data.count / itemsPerPage);
      setTotalPages(lTotalPages);
    }
  }, [data]);

  if (isLoading) {
    return <SurveyPageSkeleton />;
  }

  if (error) {
    return <div className="text-red-500 text-center text-xl">Error loading survey data</div>;
  }

  return (
    <div className="flex flex-col justify-center items-center w-full min-h-screen bg-black py-10">
      <div className="lg:w-[90%] w-full bg-gray-900 shadow-lg rounded-lg p-8 flex flex-col items-center flex-grow">
        <h1 className="text-4xl font-bold text-center mb-8 text-white">Survey Data</h1>
        <div className="w-full flex-grow">
          <Table className="overflow-x-auto w-full">
            <TableHeader>
              <TableRow>
                <TableHead>Name</TableHead>
                <TableHead>Email</TableHead>
                <TableHead>Usability Experience</TableHead>
                <TableHead>Overall Experience</TableHead>
                <TableHead>Features Description</TableHead>
                <TableHead>Submitted At</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {data?.data?.map((survey) => (
                <TableRow key={survey.id}>
                  <TableCell>{survey.full_name}</TableCell>
                  <TableCell>{survey.email}</TableCell>
                  <TableCell>{survey.usability_rating} / 5</TableCell>
                  <TableCell>{survey.overall_experience_rating} / 5</TableCell>
                  <TableCell>{survey.features_description}</TableCell>
                  <TableCell>{new Date(survey.created_at).toLocaleString()}</TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </div>
        <Pagination
          loop
          initialPage={currentPage}
          total={totalPages}
          onChange={handlePageChange}
          page={currentPage}
        />
      </div>
    </div>
  );
};

export default SurveyPage;

export const SurveyPageSkeleton = () => (
  <>
    <div className="flex flex-col justify-center items-center w-full min-h-screen bg-black py-10">
      <div className="lg:w-[90%] w-full bg-gray-900 shadow-lg rounded-lg p-8 flex flex-col items-center flex-grow">
        <h1 className="text-4xl font-bold text-center mb-8 text-white">Loading Survey Data...</h1>
        <div className="w-full flex-grow">
          <Table className="overflow-x-auto w-full">
            <TableHeader>
              <TableRow>
                <TableHead>Name</TableHead>
                <TableHead>Email</TableHead>
                <TableHead>Usability Experience</TableHead>
                <TableHead>Overall Experience</TableHead>
                <TableHead>Features Description</TableHead>
                <TableHead>Submitted At</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {[...Array(12)].map((_, index) => (
                <TableRow key={index}>
                  <TableCell><Skeleton className="h-4 w-[100px]" /></TableCell>
                  <TableCell><Skeleton className="h-4 w-[100px]" /></TableCell>
                  <TableCell><Skeleton className="h-4 w-[100px]" /></TableCell>
                  <TableCell><Skeleton className="h-4 w-[100px]" /></TableCell>
                  <TableCell><Skeleton className="h-4 w-[100px]" /></TableCell>
                  <TableCell><Skeleton className="h-4 w-[100px]" /></TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </div>
      </div>
    </div>
  </>
);
