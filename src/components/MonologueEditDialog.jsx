import React, { useState, useEffect } from "react";
import { Dialog, DialogContent, DialogTitle } from "@/components/ui/dialog";
import { But<PERSON> } from "@/components/ui/button";
import { X } from "lucide-react";
import { Textarea } from "./ui/textarea";
import { ScrollArea } from "./ui/scroll-area";
import { FaVideo } from "react-icons/fa6";

const MonologueEditDialog = ({ isOpen, onClose, onSave, monologue, handleContinue }) => {
  const [editedContent, setEditedContent] = useState("");
  const [isMobile, setIsMobile] = useState(false);

  useEffect(() => {
    const checkMobile = () => {
      const isMobileBySize = window.innerWidth <= 768;
      const isMobileByAgent = ('ontouchstart' in window) || 
        (navigator?.maxTouchPoints > 0) || 
        /android|iphone|ipad|ipod/i.test(navigator.userAgent);
      setIsMobile(isMobileBySize || isMobileByAgent);
    };
    
    checkMobile();
    window.addEventListener('resize', checkMobile);
    return () => window.removeEventListener('resize', checkMobile);
  }, []);

  useEffect(() => {
    if (monologue) {
      setEditedContent(monologue.content);
    }
  }, [monologue]);

  const handleSave = () => {
    onSave({ ...monologue, content: editedContent });
    onClose();
  };

  const handleRecord = () => { 
    onSave({ ...monologue, content: editedContent });
    handleContinue();
  }

  const handleOnClose = (event) => {
    event.preventDefault();
    if (monologue) {
      setEditedContent(monologue.content);
    }
    onClose();
  };

  return (
    <Dialog open={isOpen} onOpenChange={(open) => open && onClose()}>
      <DialogContent className="max-w-3xl bg-primary1 text-text1 border-text2 p-5 discord-scrollbar">
        <DialogTitle>Edit Monologue to Record</DialogTitle>
        <Textarea
          value={editedContent}
          onChange={(e) => setEditedContent(e.target.value)}
          className="w-full mt-4 bg-secondary2 border border-text2 rounded-lg px-4 py-2 focus:outline-none focus:ring focus:ring-text2"
          rows={14}
        />
        <div className="flex flex-col gap-4">
          {isMobile && (
            <div className="w-full text-center text-sm text-text1 mt-2 px-4 bg-primary2 py-2 rounded-md">
              Recording is currently available on desktop only. The more you use Audition Rooms, the more features will grow!
            </div>
          )}
          <div className="flex justify-end gap-2 flex-wrap">
            <Button
              className="bg-close shadow-none hover:bg-close/80"
              onClick={handleOnClose}
            >
              <span className="flex gap-2 items-center">
                <X size={16} /> Close
              </span>
            </Button>
            <Button onClick={handleSave}>Save Changes</Button>
            <Button
              className={`px-8 py-2`}
              onClick={handleRecord}
            >
              <div className="flex gap-2 items-center">
                <FaVideo size={16} />
                Record Video
              </div>
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default MonologueEditDialog;
