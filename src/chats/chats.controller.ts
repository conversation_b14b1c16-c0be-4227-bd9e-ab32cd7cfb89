import {
  Body,
  Controller,
  Delete,
  Get,
  HttpException,
  Param,
  Post,
  Query,
  Req,
  UseGuards,
} from '@nestjs/common';
import { ChatsService } from './chats.service';
import { UserGuard } from '../auth/guards/user.guard';
import { CreateChatDto } from './dto/create-chat-dto';

@Controller('chats')
export class ChatsController {
  constructor(private readonly chatsService: ChatsService) {}

  @Get('/') // get all chats for a user
  @UseGuards(UserGuard)
  async GetChats(@Req() req) {
    const resp = await this.chatsService.getChats(req.user.id);
    if (resp.error) {
      throw new HttpException(resp.message, resp.status);
    }
    return resp;
  }

  @Post('/create_chat')
  @UseGuards(UserGuard)
  async CreateChat(@Req() req, @Body() createChatDto: CreateChatDto) {
    const resp = await this.chatsService.createChat(
      req.user.id,
      createChatDto.user_id,
    );
    if (resp.error) {
      throw new HttpException(resp.message, resp.status);
    }
    return resp;
  }

  @Delete('/delete_chat/:chat_id')
  @UseGuards(UserGuard)
  async DeleteChat(@Req() req, @Param('chat_id') chat_id: number) {
    const resp = await this.chatsService.deleteChat(req.user.id, chat_id);
    if (resp.error) {
      throw new HttpException(resp.message, resp.status);
    }
    return resp;
  }

  @Get('/:chat_id') // get messages of a specific chat
  @UseGuards(UserGuard)
  async GetChatMessages(
    @Req() req,
    @Param('chat_id') chat_id: number,
    @Query('page_no') page_no: number = 1,
  ) {
    const resp = await this.chatsService.getChatMessages(
      req.user.id,
      chat_id,
      page_no,
    );
    if (resp.error) {
      throw new HttpException(resp.message, resp.status);
    }
    return resp;
  }
}
