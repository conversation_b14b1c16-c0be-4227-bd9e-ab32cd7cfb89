import { useEffect, useState, useCallback } from "react";
import { useSelector } from "react-redux";
import { useNavigate } from "react-router-dom";

import { Skeleton } from "@/components/ui/skeleton";
import { Button } from "@/components/ui/button";
import { Ellipsis, MessageSquare, Loader2 } from "lucide-react";

import { subscribeToChats } from "@/lib/supabase";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
} from "@/components/ui/dropdown-menu";
import { DropdownMenuTrigger } from "@radix-ui/react-dropdown-menu";
import {
  useDeleteChatMutation,
  useGetChatsQuery,
} from "@/redux/features/chatApi";
import placeholderImage from "@/assets/images/blank-profile-picture.png";

const MessagesList = () => {
  const navigate = useNavigate();
  const { data: chats, isLoading: chatsLoading, refetch } = useGetChatsQuery();
  const currentUserId = useSelector((state) => state.user.session?.user?.id);
  const onlineUsers = useSelector((state) => state.chats.onlineUsers);
  const [backgroundLoading, setBackgroundLoading] = useState(false);
  const [deletingChatId, setDeletingChatId] = useState(null);

  const [deleteChat] = useDeleteChatMutation();

  useEffect(() => {
    const chatSubscription = subscribeToChats((payload) => {
    
      if (payload.eventType === "UPDATE" || payload.eventType === "INSERT") {
        setBackgroundLoading(true);
        refetch().then(() => setBackgroundLoading(false));
      }
    });

    return () => {
      chatSubscription.unsubscribe();
    };
  }, [refetch]);

  const handleDelete = useCallback(
    async (chatId) => {
      setDeletingChatId(chatId);
      try {
        await deleteChat({ chatId, userId: currentUserId }).unwrap();
      
        await refetch(); // Wait for the refetch to complete
      } catch (error) {
        console.error("Failed to delete chat:", error);
      } finally {
        setDeletingChatId(null);
      }
    },
    [deleteChat, currentUserId, refetch]
  );

  const filteredChats = chats?.filter(
    (chat) =>
      (currentUserId === chat.user_1 && !chat.deleted_by_user_1) ||
      (currentUserId === chat.user_2 && !chat.deleted_by_user_2)
  );

  if (chatsLoading || backgroundLoading) {
    return (
      <div className="flex flex-col gap-2 2xl:gap-4 px-9 lg:px-0 my-2 lg:my-8">
        {[...Array(7)].map((_, index) => (
          <div key={index} className="flex gap-2 px-2 sm:px-4 py-2 2xl:py-4">
            <Skeleton className="w-14 h-14 rounded-full" />
            <div className="grid grid-cols-2 items-center flex-grow">
              <Skeleton className="h-4 w-24" />
              <Skeleton className="h-3 w-16 justify-self-end" />
              <Skeleton className="h-3 w-full col-span-2 mt-2" />
            </div>
          </div>
        ))}
      </div>
    );
  }

  return (
    <div className="flex flex-col gap-4 px-4 lg:px-0 my-4 lg:my-8">
      {filteredChats?.length === 0 ? (
        <div className="flex flex-col items-center justify-center text-center mt-20">
          <MessageSquare className="w-20 h-20 text-gray-400" />
          <h2 className="text-2xl font-semibold mt-4 text-gray-300">
            No Messages Yet
          </h2>
          <p className="text-gray-500 mt-2">
            Start a conversation by sending your first message!
          </p>
          <Button
            variant="primary"
            size="lg"
            className="mt-6 bg-navItemActive hover:bg-blue-600"
            onClick={() => navigate("/")}
          >
            Go to Feed Page
          </Button>
        </div>
      ) : (
        filteredChats?.map((chat) => (
          <div
            key={chat.id}
            className="flex gap-4 p-4 bg-chatSec hover:bg-gray-700 rounded-xl cursor-pointer transition-colors duration-200 relative"
            onClick={() => navigate(`/messages/${chat.id}`)}
          >
            <img
              src={
                chat.user_data?.profile_picture_url ||
                placeholderImage
              }
              alt={chat.user_data?.full_name}
              className="w-14 h-14 object-cover rounded-full"
            />

            <div className="flex-grow">
              <div className="flex md:flex-row flex-col md:justify-start md:items-center md:gap-5 gap-1">
                <h3 className="md:text-lg text-sm font-semibold">
                  {chat.user_data?.full_name}
                </h3>
                {onlineUsers[chat.user_data?.id] && (
                  <div className="w-3 h-3 rounded-full bg-green-500 absolute left-14 md:bottom-4 bottom-6"></div>
                )}
                <p className="text-xs text-gray-400">
                  {new Date(chat?.latest_message?.sent_at).toLocaleString()}
                </p>
              </div>
              <p className="text-sm text-gray-300 truncate mt-1 text-wrap pr-14">
                {chat?.latest_message?.message}
              </p>
            </div>

            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button
                  variant="ghost"
                  className="absolute right-6 bottom-7 h-8 w-8 p-0 hover:bg-transparent hover:text-white"
                  disabled={deletingChatId === chat.id}
                >
                  {deletingChatId === chat.id ? (
                    <Loader2 size={40} className=" animate-spin text-navItemActive" />
                  ) : (
                    <Ellipsis />
                  )}
                </Button>
              </DropdownMenuTrigger>
              {deletingChatId === chat.id ? "" : (
                <DropdownMenuContent
                  align="end"
                  className="bg-transparent min-w-12 border-gray-700"
                >
                  <DropdownMenuItem className="bg-primary1 text-white border-primary1 p-0">
                    <button
                      onClick={(e) => {
                        e.stopPropagation();
                        handleDelete(chat.id);
                      }}
                      className="bg-primary1 text-white border-primary1 p-1 text-xs w-full text-left"
                      disabled={deletingChatId === chat.id}
                    >
                      Delete
                    </button>
                  </DropdownMenuItem>
                </DropdownMenuContent>
              )}
            </DropdownMenu>
          </div>
        ))
      )}
    </div>
  );
};

export default MessagesList;
