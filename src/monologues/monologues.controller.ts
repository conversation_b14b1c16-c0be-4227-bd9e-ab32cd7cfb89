import {
  Body,
  Controller,
  Get,
  HttpException,
  Post,
  Query,
  UseGuards,
  Delete,
  Param,
} from '@nestjs/common';
import { MonologuesService } from './monologues.service';
import { UserGuard } from '../auth/guards/user.guard';
// import { BotService } from '../bot/bot.service'; // Temporarily disabled
import { CreateMonologueDto } from './dto/create-monologues.dto';

@Controller('monologues')
export class MonologuesController {
  constructor(
    private readonly monologuesService: MonologuesService,
    // private botService: BotService, // Temporarily disabled
  ) {}

  @Get('get_monologues')
  @UseGuards(UserGuard)
  async getMonologues(
    @Query('_start') page: number,
    @Query('_limit') limit: number,
    @Query('_searchTerm') searchString: string,
    @Query('_chip') chipString: string,
    @Query('_gender') genderString: string,
    @Query('_category') categoryString: string,
  ) {
    const resp = await this.monologuesService.getMonologues(
      page,
      limit,
      searchString,
      chipString,
      genderString,
      categoryString,
    );
    if (resp.error) {
      throw new HttpException(resp.message, resp.status);
    }
    return resp;
  }

  // @Post('generate_monologue')
  // @UseGuards(UserGuard)
  // async genMonologue(@Body() body) {
  //   return await this.botService.generate_monologue(body.prompt);
  // }

  @Post('insert_monologue')
  @UseGuards(UserGuard)
  async create(@Body() createMonologuesDto: CreateMonologueDto[]) {
    const resp = await this.monologuesService.insertRecord(createMonologuesDto);
    if (resp.error) {
      throw new HttpException(resp.message, resp.status);
    }
    return resp;
  }

  @Get('get_monologues_filter')
  @UseGuards(UserGuard)
  async fetchMonologueFilter() {
    const resp = await this.monologuesService.getMonologuesFilter();
    if (resp.error) {
      throw new HttpException(resp.message, resp.status);
    }
    return resp;
  }

  @Delete('/:monologue_id')
  @UseGuards(UserGuard)
  async deleteMonolgoue(@Param('monologue_id') monologue_id: number) {
    console.log({ monologue_id });
    const resp = await this.monologuesService.deleteMonologue(monologue_id);
    if (resp.error) {
      throw new HttpException(resp.message, resp.status);
    }
    return resp;
  }
}
