import { Dot } from 'lucide-react'
// import images
import GuitaristImg from '../../../assets/images/showcase.png';
import FriendsImg from '../../../assets/images/friends.jpg';
import LadyImg from '../../../assets/images/lady.png';
import NetworkingImg from '../../../assets/images/networking.png';
import SocialImg from '../../../assets/images/social.jpg';
import PerformerImg from '../../../assets/images/assistance.png';
import WorkFromHomeImg from '../../../assets/images/affordability.png';
import OneImg from '../../../assets/images/one.png';
import TwoImg from '../../../assets/images/two.png';
import ThreeImg from '../../../assets/images/three.png';
import FourImg from '../../../assets/images/four.png';
import FiveImg from '../../../assets/images/five.png';
import SixImg from '../../../assets/images/six.png';
import SevenImg from '../../../assets/images/seven.png';


const ListItem = ({ image, bgImg, title, list, listNumber }) => {

  return (
    <div className={`no-box relative max-sm:pt-[230px] max-sm:block md:flex lg:flex ${listNumber % 2 != 0 ? 'flex-row-reverse rounded-r-3xl' : 'rounded-l-3xl'} bg-gray2 w-[95%] h-auto md:h-[280px] mb-10`}>
      <div className="max-sm:block  max-sm:m-auto  md:block max-sm:w-[90%] md:w-[200px] lg:w-[400px] border-[10px] overflow-hidden border-navItemActive rounded-3xl">
        <img src={image} alt="image" className="h-full w-full object-cover" />
      </div>
      <div className="flex-1 px-5 pb-5">
        <h1 className='text-3xl font-extrabold pt-8 pb-3'>{title}</h1>
        {list.map((item, index) =>
          <div className="flex items-center">
            <div>
              <Dot size={30} className="text-navItemActive" />
            </div>
            <span key={index}>{item}</span>
          </div>
        )}
      </div>
      <div className={`no-img max-sm:absolute max-sm:top-[15px] max-sm:left-[20px] flex items-center md:items-end ${listNumber % 2 == 0 ? 'justify-center pr-5' : 'justify-center  pl-5'} w-[150px]`}>
        <img src={bgImg} alt="number" className="h-48" />
      </div>
    </div>
  )
}

const FeaturesList = () => {
  const featuresList = [
    {
      image: GuitaristImg,
      bgImg: OneImg,
      title: 'Showcase Your Talent',
      list: [
        'Create a detailed profile with your skills, experience, and headshots.',
        'Upload demo reels, self-taped auditions, and audio submissions to highlight your strengths.',
        'Customize your profile to emphasize specific strengths and achievements.'
      ]
    },

    {
      image: FriendsImg,
      bgImg: TwoImg,
      title: 'Peer Feedback and Growth',
      list: [
        'Submit performances and receive constructive feedback from fellow actors.',
        'Resubmit your monologues to refine your craft and perfect your performance',
        'Track your growth.'
      ]
    },
    {
      image: LadyImg,
      bgImg: ThreeImg,
      title: 'Self-Tape and Submission Tools',
      list: [
        'Record professional self-tapes directly through the platform.',
        'Upload your auditions seamlessly using our Audition Software for Actors.',
      ]
    },
    {
      image: NetworkingImg,
      bgImg: FourImg,
      title: 'Networking and Collaboration',
      list: [
        'Connect with other actors to share tips and collaborate on projects.',
        'Join a thriving community to grow your network within the acting community.',
      ]
    },
    {
      image: SocialImg,
      bgImg: FiveImg,
      title: 'Social Sharing Capabilities',
      list: [
        'Share performances directly to WhatsApp, Facebook, TikTok, and Instagram.',
        'Engage with a global audience to showcase your talent.',
        'Grow your fanbase and expand your reach effortlessly.'
      ]
    },
    {
      image: PerformerImg,
      bgImg: SixImg,
      title: 'AI-Powered Assistance',
      list: [
        'Use AI tools to generate customized monologues suited to your needs.',
        'Get personalized suggestions to refine and elevate your performance.',
        'Leverage smart technology to stay ahead in the community.'
      ]
    },
    {
      image: WorkFromHomeImg,
      bgImg: SevenImg,
      title: 'Affordability and Accessibility',
      list: [
        'Remove financial barriers with cost-effective tools and resources.',
        'Access audition opportunities without the need for expensive training programs.',
        'Enjoy premium features designed for all levels of actors.'
      ]
    },
  ]
  return (
    <>
      <div className="bg-primary1 text-text1 pb-20">
        {featuresList.map((feature, index) =>
          <div className={`${index % 2 == 0 && 'flex justify-end'}`}>
            <ListItem key={index} image={feature.image} bgImg={feature.bgImg} title={feature.title} list={feature.list} listNumber={index} ></ListItem>
          </div>
        )}
      </div>
    </>
  )
}

export default FeaturesList;