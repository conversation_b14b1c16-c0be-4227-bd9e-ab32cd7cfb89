import { HttpStatus, Injectable } from '@nestjs/common';
import { DatabaseService } from '../database/database.service';
import { CustomResponse } from '../utils/CustomResponse';

@Injectable()
export class AuthService {
  constructor(private readonly dbService: DatabaseService) {}

  // login, signup are handled in the frontend, having these here makes it easier to test
  async login(email: string, password: string) {
    return await this.dbService.supabaseLoginWithEmail(email, password);
  }

  async signup(email: string, password: string, metadata: object) {
    return await this.dbService.signUp({
      email,
      password,
      options: { data: metadata },
    });
  }

  async setPassword(
    user_id: string,
    password: string,
  ): Promise<CustomResponse> {
    try {
      const update_resp = await this.dbService.updateUserbyId(user_id, {
        password,
      });
      return {
        data: update_resp,
        error: false,
        message: 'Password set',
        status: HttpStatus.OK,
      };
    } catch (err) {
      console.log(err);
      return {
        data: null,
        error: true,
        message: 'Error setting password',
        status: HttpStatus.INTERNAL_SERVER_ERROR,
      };
    }
  }

  async updatePasswordPrisma(emailId: string, newPassword: string){
    this.dbService.updatePasswordPrisma(emailId, newPassword);
  }
}
