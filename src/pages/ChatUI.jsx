import { useParams } from "react-router-dom";

import MessagesList from "@/components/shared/Messages/MessagesList";
import ChatSection from "@/components/shared/Messages/ChatSection";




const ChatUI = () => {
  const { chatId } = useParams();

  return (
    <div className="flex flex-col justify-center items-center w-full">
      <div className="lg:w-[52%] w-full ">
        {chatId ? <ChatSection/>  : <MessagesList />}
      </div>
    </div>
  );
};

export default ChatUI;
