import HeaderSection from "@/components/shared/HeaderSection";

import Navbar from "@/components/shared/Navbar";
import MissionSection from "@/components/shared/MissionSection";
import FooterSection from "@/components/shared/FooterSection";
import FeaturesList from "@/components/shared/Features/FeaturesList";
import TalentSection from "@/components/shared/TalentSection";
import { Helmet } from "react-helmet-async";

// import images
import SingerMicImg from '../assets/images/feature-banner.png';
import JokerImg from '../assets/images/showcase-talent-new.png';

const FeaturesPage = () => {
  const featuresDesc = [
    `Audition Rooms is a cutting-edge Audition Platform and Talent Management System, designed to empower actors and coaches alike. With innovative tools like self-taping, social sharing, and peer feedback, we’re here to simplify auditions and connect talent with opportunity.`
  ];

  const mission = [
    'Features Built for Aspiring Stars'
  ]

  return (
    <>
      <Helmet>
        <title>Audition Rooms Features | Innovative Tools for Actors & Casting Directors</title>
        <meta name="title" content="Audition Rooms Features | Innovative Tools for Actors &
        Casting Directors" />

        <meta name="Description" content="Explore the cutting-edge features of Audition
        Rooms, the premier virtual casting platform. Discover self-tape tools, peer feedback,
        AI-powered assistance, and more to enhance your acting journey." />

        <meta name="Keywords" content="Casting Platform, Talent Management System, Best
        Audition Software for Actors, Online Casting Platform Features, Tools for Managing
        Auditions, How to Submit Auditions Online, Peer Feedback Tools for Actors, Self-Tape
        Audition Features, Video Audition Platform, AI Monologue Suggestions for Actors, Acting
        Industry Networking Platform" />
      </Helmet>
      <section className="bg-primary1 animate-smooth-appear">
        <Navbar />
        <HeaderSection image={SingerMicImg} title="Features" />
        <MissionSection descArr={featuresDesc} mission={mission} />
        <FeaturesList />
        <TalentSection image={JokerImg} title={`Revolutionize Your <br/> <span class="text-purple">Audition Experience</span><br/>Today`} desc="Discover a platform built to elevate your acting journey." btnText="Explore Features Now" />
        <FooterSection />
      </section>
    </>
  );
}

export default FeaturesPage;