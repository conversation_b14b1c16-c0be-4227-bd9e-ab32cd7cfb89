import { Module } from '@nestjs/common';
import { AppController } from './app.controller';
import { AppService } from './app.service';
import { ConfigModule } from '@nestjs/config';
import { DatabaseModule } from './database/database.module';
import { AuthModule } from './auth/auth.module';
import { PostsModule } from './posts/posts.module';
import { MonologuesModule } from './monologues/monologues.module';
import { PlansModule } from './plans/plans.module';
import { StripeModule } from './stripe/stripe.module';
import { ProfileModule } from './profile/profile.module';
import { CacheInterceptor, CacheModule } from '@nestjs/cache-manager';
import { APP_INTERCEPTOR } from '@nestjs/core';
import { CompressorModule } from './compressor/compressor.module';
import { ChatsModule } from './chats/chats.module';
import { CoachesModule } from './coaches/coaches.module';
import { CustomCacheInterceptor } from './cache';
import { BotModule } from './bot/bot.module';
import { ScheduleModule } from '@nestjs/schedule';
import { SurveyModule } from './survey/survey.module';
import { ContactUsModule } from './contact-us/contact-us.module';
import { ExpressionAnalysisModule } from './expression-analysis/expression-analysis.module';
import { WebhooksModule } from './webhooks/webhooks.module';

@Module({
  imports: [
    ConfigModule.forRoot({
      isGlobal: true,
    }),
    // CacheModule.register({
    //   ttl: 10000, // 10 seconds
    //   isGlobal: true,
    // }),
    DatabaseModule,
    AuthModule,
    PostsModule,
    MonologuesModule,
    PlansModule,
    StripeModule,
    ProfileModule,
    CompressorModule,
    ChatsModule,
    CoachesModule,
    // BotModule, // Temporarily disabled due to OpenAI config conflict
    SurveyModule,
    ContactUsModule,
    ExpressionAnalysisModule,
    WebhooksModule,
    ScheduleModule.forRoot(),
  ],
  controllers: [AppController],
  providers: [
    // {
    //   // provide: APP_INTERCEPTOR,
    //   // useClass: CustomCacheInterceptor,
    // },
    AppService,
  ],
})
export class AppModule {}
