import {
  Link,
  NavLink,
  useNavigate,
  useLocation,
  useParams,
} from "react-router-dom";
import { Sheet, SheetContent, SheetTrigger } from "@/components/ui/sheet";
import { Button } from "@/components/ui/button";
import {
  AlignJustify,
  Home,
  LogOut,
  Settings,
  User,
  MessageCircle,
  BotMessageSquare,
  Upload, // Added for Monologue Library
  Contact,
} from "lucide-react";
import supabase from "@/lib/supabase";
import { useToast } from "./ui/use-toast";
import React, { useState, useEffect } from "react";
import { Tooltip, TooltipContent, TooltipTrigger } from "./ui/tooltip";
import coachesIcon from "../../public/coach.svg";
import { useSelector } from "react-redux";
import AiSupportBot from "./shared/Support-bot/AiSupportBot";
import { MdOutlineLocalLibrary } from "react-icons/md";
import { FcSurvey } from "react-icons/fc";

const Sidebar = () => {
  const { toast } = useToast();
  const navigate = useNavigate();
  const location = useLocation();
  const { chatId } = useParams();
  const [isMobile, setIsMobile] = useState(false);
  const { session } = useSelector((state) => state.user);
   const { role: userRole } = useSelector((state) => state.user);
  const isCoach = session?.user?.user_metadata?.role === "coach";
  const isAdmin = userRole === "admin";

  // Handling closing of nav menu
  const [isMenuOpen, setIsMenuOpen] = useState(false);

  const toggleMenu = () => {
    setIsMenuOpen(!isMenuOpen);
  };

  const closeMenu = () => {
    setIsMenuOpen(false);
  };

  // For AI Chatbot
  const [isDialogOpen, setIsDialogOpen] = useState(false);

  const toggleDialog = () => {
    setIsDialogOpen(!isDialogOpen);
  };

  // For messages chat screen on smaller devices the hamburgar menu must be hidden
  const [isMenuBtnHidden, setIsMenuBtnHidden] = useState(false);

  useEffect(() => {
    const checkScreenSize = () => {
      setIsMobile(window.innerWidth < 1024);
      if (location.pathname === `/messages/${chatId}`) {
        setIsMenuBtnHidden(true);
      } else {
        setIsMenuBtnHidden(false);
      }
    };

    checkScreenSize();
    window.addEventListener("resize", checkScreenSize);

    return () => window.removeEventListener("resize", checkScreenSize);
  }, [location.pathname]);

  const handleSignout = async () => {
    try {
      await supabase.auth.signOut();
      navigate("/signin");
      toast({ title: "Sign Out Successful" });
    } catch (error) {
      if (error instanceof Error) {
        console.error("Error signing out:", error.message);
      }
    }
  };

  const NavItem = React.forwardRef(
    ({ to, icon: Icon, iconSrc, children }, ref) => (
      <NavLink onClick={closeMenu}
        to={to}
        className={({ isActive }) =>
          `w-full text-center p-3 rounded ${
            isActive
              ? "bg-navItemActive  text-secondary1 shadow-buttonShadow"
              : "hover:bg-hover1"
          }`
        }
      >
        <div className="flex justify-center items-center gap-2 text-[14px]">
          {Icon && <Icon size={20} />}
          {iconSrc && <img src={iconSrc} alt="" className="w-5 h-5" />}
          {children}
        </div>
      </NavLink>
    )
  );

  NavItem.displayName = "NavItem";

  const TooltipNavItem = ({ to, icon: Icon, iconSrc, label }) => (
    <Tooltip>
      <TooltipTrigger asChild>
        <NavItem to={to} icon={Icon} iconSrc={iconSrc} />
      </TooltipTrigger>
      <TooltipContent side="right" className="ml-8">
        {label}
      </TooltipContent>
    </Tooltip>
  );

  TooltipNavItem.displayName = "TooltipNavItem";
  const SidebarContent = () => (
    <aside className="fixed inset-y-0 left-0 z-10 hidden w-20 flex-col border-r border-gray-800 bg-primary1 sm:flex">
      <nav className="flex flex-col items-center justify-around h-full gap-4 px-2 sm:py-4">
        <div className="h-auto">
          <Link to="/" className="relative inline-block group">
            <img
              src="/logo.png"
              className="invert mt-4 w-16 transition-all duration-1000 ease-in-out transform group-hover:scale-110 group-hover:rotate-3 animate-pulse"
              alt="Audition Rooms"
            />
            <span className="sr-only">Audition Rooms</span>
          </Link>
        </div>
        <div className="h-auto flex flex-col items-center justify-center gap-2">
          <TooltipNavItem icon={Home} to="/" label="Feed" />
          <TooltipNavItem
            icon={MessageCircle}
            to="/messages"
            label="Messages"
          />
          <TooltipNavItem icon={User} to="/profile" label="Profile" />
          <TooltipNavItem icon={Settings} to="/settings" label="Settings" />
          <TooltipNavItem
            icon={MdOutlineLocalLibrary}
            to="/upload"
            label="Monologue Library"
          />
          {!isCoach && (
            <TooltipNavItem
              iconSrc={coachesIcon}
              to="/coaches"
              label="Coaches"
            />
          )}
          {isAdmin && (
              <TooltipNavItem
                icon={FcSurvey}
                to="/survey-page"
                label="Survey Page"
              />
            )
          }
          {isAdmin && (
              <TooltipNavItem
                icon={Contact}
                to="/contact-listing"
                label="Contacts Listing"
              />
            )
          }
        </div>

        <div className="h-auto flex flex-col justify-end">
          <Tooltip>
            <TooltipTrigger asChild>
              <button
                className="flex h-9 w-9 items-center justify-center rounded-full bg-text1 text-primary1 transition-colors hover:text-text1 hover:bg-hover1 md:h-8 md:w-8"
                onClick={handleSignout}
              >
                <LogOut className="h-5 w-5" />
                <span className="sr-only">Logout</span>
              </button>
            </TooltipTrigger>
            <TooltipContent side="right" className="ml-8">
              Logout
            </TooltipContent>
          </Tooltip>
        </div>
      </nav>
    </aside>
  );

  return (
    <>
      {isMobile ? (
        <Sheet open={isMenuOpen} onOpenChange={toggleMenu}>
          <SheetTrigger asChild>
            {!isMenuBtnHidden && (
              <Button
                variant="outline"
                size="icon"
                className="fixed top-4 left-4 z-50 bg-[#202020] border-none"
              >
                <AlignJustify size={30} />
              </Button>
            )}
          </SheetTrigger>
          <SheetContent
            side="left"
            className="w-[300px] sm:w-[400px] bg-primary1 text-text1 border-none"
          >
            <div className="flex flex-col h-full">
              <div className="flex-grow flex flex-col items-center pt-[5px]">
                <Link
                  to="/"
                  className="flex flex-col items-center group mb-[15px] mx-auto"
                  onClick={closeMenu}
                >
                  <img
                    src="/logo.png"
                    className="invert mt-4  w-20 transition-all duration-1000 ease-in-out transform group-hover:scale-110 group-hover:rotate-3 animate-pulse"
                    alt="Audition Rooms"
                  />
                  <h1 className="mt-4 text-2xl font-bold text-center text-white tracking-wide">
                    Audition Rooms
                  </h1>
                </Link>
                <NavItem to="/" icon={Home}>
                  Feed
                </NavItem>
                <NavItem to="/messages" icon={MessageCircle}>
                  Messages
                </NavItem>
                <NavItem to="/profile" icon={User}>
                  Profile
                </NavItem>
                <NavItem to="/settings" icon={Settings}>
                  Settings
                </NavItem>
                <NavItem to="/upload" icon={Upload}>
                  Monologue Library
                </NavItem>
                {!isCoach && (
                  <NavItem to="/coaches" iconSrc={coachesIcon}>
                    Coaches
                  </NavItem>
                )}
                {isAdmin && (
                  <NavItem to="/survey-page" icon={FcSurvey}>
                    Survey Listing
                  </NavItem>
                )}
                {isAdmin && (
                  <NavItem to="/contact-listing" icon={Contact}>
                    ContactUs Listing
                  </NavItem>
                )}
                <Button
                  className="w-full bg-transparent shadow-none text-text1 hover:bg-hover1 flex gap-2"
                  onClick={toggleDialog}
                >
                  <BotMessageSquare size={20} /> AI Assistant
                </Button>
              </div>
              <AiSupportBot
                className="hidden"
                isDialogOpen={isDialogOpen}
                toggleDialog={toggleDialog}
              />
              <div className="flex justify-center items-center mb-6">
                <Button
                  className="w-full text-text1 hover:bg-hover1"
                  onClick={handleSignout}
                >
                  <LogOut size={16} className="mr-2" /> Logout
                </Button>
              </div>
            </div>
          </SheetContent>
        </Sheet>
      ) : (
        <div className="fixed left-0 top-0 h-screen  bg-secondary1 p-4">
          <SidebarContent />
        </div>
      )}
    </>
  );
};

export default Sidebar;
