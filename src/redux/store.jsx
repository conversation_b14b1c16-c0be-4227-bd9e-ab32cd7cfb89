import { configureStore } from "@reduxjs/toolkit";
import userReducer from "./features/userSlice";
import postReducer, { postApi } from "./features/postSlice";
import dialogReducer from "./features/dialogSlice";
import { commentApi } from "./features/commentSlice";
import profileReducer, { profileApi } from "./features/profileSlice";
import chatsReducer from "./features/chatSlice";
import { presenceMiddleware } from "@/middleware/presenceMiddleware";
import chatApi from "./features/chatApi";
import planApi from "./features/planApi";
import { coachApi } from "./features/coachApi";
import { contactUsApi } from "./features/contactUsSlice";
import { surveyApi } from "./features/surveySlice";
import { setupListeners } from "@reduxjs/toolkit/query";

const store = configureStore({
  reducer: {
    user: userReducer,
    posts: postReducer,
    [postApi.reducerPath]: postApi.reducer,
    dialog: dialogReducer,
    // comments: commentReducer,
    [commentApi.reducerPath]: commentApi.reducer,
    profile: profileReducer,
    [profileApi.reducerPath]: profileApi.reducer,
    chats: chatsReducer,
    [chatApi.reducerPath]: chatApi.reducer,
    [planApi.reducerPath]: planApi.reducer,
    [coachApi.reducerPath]: coachApi.reducer,
    [surveyApi.reducerPath]: surveyApi.reducer,
    [contactUsApi.reducerPath]: contactUsApi.reducer,
  },
  middleware: (getDefaultMiddleware) =>
    getDefaultMiddleware().concat(
      presenceMiddleware,
      postApi.middleware,
      profileApi.middleware,
      chatApi.middleware,
      commentApi.middleware,
      planApi.middleware,
      coachApi.middleware,
      surveyApi.middleware,
      contactUsApi.middleware 
    ),
});

// Optional: but recommended for refetchOnFocus/refetchOnReconnect behaviors
setupListeners(store.dispatch);

export default store;
