// helper function to add artificial delay
export function delay(ms: number): Promise<void> {
  return new Promise((resolve) => setTimeout(resolve, ms));
}

// helper function to extract filename from supabase public url
export function extractFilename(url) {
  const parts = url.split('/');
  return parts[parts.length - 1];
}

// to combine documents fetched from retriever
export function combineDocuments(docs: string[]) {
  return docs.map((doc) => doc['pageContent']).join('\n\n');
}

// to format conversation history to make it easier to parse for the bot
export function formatConvHistory(messages: string[]) {
  return messages
    .map((message, i) => {
      if (i % 2 === 0) {
        return `Human: ${message}`;
      } else {
        return `AI: ${message}`;
      }
    })
    .join('\n');
}
