import { useState, useEffect } from "react";
import { <PERSON><PERSON>, DialogContent, DialogTitle } from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { X } from "lucide-react";
import <PERSON> from 'papa<PERSON><PERSON>';
import {
    Card,
    CardContent
  } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { useInsertMonologueMutation } from "@/redux/features/postSlice";
import { useToast } from "@/components/ui/use-toast";

// eslint-disable-next-line react/prop-types
const MonologueCSVDialog = ({ isOpen, onClose}) => {
    const [csvData, setCsvData] = useState([]);
    const [insertMonologue, { isLoading }] = useInsertMonologueMutation();
    const { toast } = useToast();

    useEffect(()=>{
        const addMonologue = async(csvData) => {
            const resData = await insertMonologue(csvData).unwrap();
            if(resData && resData.status == 200){
                toast({
                    title: "Monologues",
                    description: "Your CSV monologues has been successfully submitted!",
                    variant: "success",
                });
                onClose();
            }else{
                toast({
                    title: "Monologue",
                    description: "Something went wrong!",
                    variant: "failed",
                });
            }
        };
        
        if(csvData && Array.isArray(csvData) && csvData.length){
            addMonologue(csvData);   
        }
    // eslint-disable-next-line react-hooks/exhaustive-deps
    },[csvData, insertMonologue]);

    const handleOnClose = (event) => {
        event.preventDefault(); // Prevents backdrop click from closing the dialog
        onClose();
    };
    
    const handleFileUpload = (event) => {
        const file = event.target.files[0];
        if (file) {
          Papa.parse(file, {
            complete: (result) => {
              setCsvData(result.data);
            },
            header: true,
            skipEmptyLines: true,
          });
        }
    };
    
    const handleDownloadDemoCSV = () => {
      const filePath = '/monologue_demo.csv';
      const link = document.createElement('a');
      link.href = filePath;
      link.download = 'monologue_demo.csv';
      link.click();
    };
    
  return (
    <Dialog open={isOpen} onOpenChange={(open) => open && onClose()}>
      <DialogContent className="max-w-3xl bg-primary1 text-text1 border-text2 p-5 discord-scrollbar">
        <DialogTitle>Import CSV File for Monologue</DialogTitle>
          <div className=" bg-primary1 text-text1 flex items-center justify-center p-4">
              <Card className="w-full bg-secondary2 backdrop-blur-md max-w-6xl border-border1 shadow-lg rounded-xl">
                  <CardContent className="block gap-2">
                      <div className="w-full space-y-4">
                          <div className="space-y-2">
                              <Label
                                  htmlFor="file"
                                  className="text-lg font-medium block text-text1"
                              >
                                  Select CSV
                              </Label>
                              <Input
                                  id="file"
                                  type="file"
                                  placeholder="Select CSV File"
                                  accept=".csv"
                                  onChange={handleFileUpload}
                                  className="bg-white/5 border-border1 text-text1 rounded-lg focus:ring-2 focus:ring-hover1 transition-all placeholder:text-text2"
                              />
                          </div>                             
                      </div>
                  </CardContent>
              </Card>
          </div>
          <div>
            <Button 
              className="shadow-none hover:80"
              onClick={handleDownloadDemoCSV}
            >
              Download Demo CSV
            </Button>
          </div>
          <div className="w-full space-y-4">
              <h4>CSV file columns names would be:</h4>
              <p>title</p>
              <p>character</p>
              <p>category</p>
              <p>gender</p>
              <p>content</p>
          </div>
          <div className="flex justify-end mt-2 gap-2">
            <Button
              className="bg-close shadow-none hover:bg-close/80"
              onClick={handleOnClose}
              disabled={isLoading}
            >
              <span className="flex gap-2 items-center">
                <X size={16} /> Close
              </span>
            </Button>
          </div>
      </DialogContent>
    </Dialog>
  );
};

export default MonologueCSVDialog;