import React, { useState, useEffect } from "react";
import {
  Brain,
  ChevronLeft,
  ChevronRight,
  BarChart3,
  Heart,
  Lightbulb,
  Target,
  Volume2,
  Eye,
  MessageSquare,
  TrendingUp
} from "lucide-react";
import { subscribeToPostAnalysis } from "@/lib/supabase";
import { useRetryAnalysisMutation, useGetPostByIdQuery } from "@/redux/features/postSlice";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Progress } from "@/components/ui/progress";
import { 
  Dialog, 
  DialogContent, 
  DialogHeader, 
  DialogTitle, 
  DialogTrigger 
} from "@/components/ui/dialog";

// Enhanced emotion data with color-coded emotional states
const emotionColors = {
  contemplation: { bg: 'bg-blue-600', glow: 'shadow-blue-500/30', text: 'text-blue-400' },
  uncertainty: { bg: 'bg-purple-600', glow: 'shadow-purple-500/30', text: 'text-purple-400' },
  philosophical: { bg: 'bg-indigo-600', glow: 'shadow-indigo-500/30', text: 'text-indigo-400' },
  melancholy: { bg: 'bg-slate-600', glow: 'shadow-slate-500/30', text: 'text-slate-400' },
  resolve: { bg: 'bg-emerald-600', glow: 'shadow-emerald-500/30', text: 'text-emerald-400' },
  conflict: { bg: 'bg-red-600', glow: 'shadow-red-500/30', text: 'text-red-400' },
  acceptance: { bg: 'bg-amber-600', glow: 'shadow-amber-500/30', text: 'text-amber-400' }
};



const EmotionAnalytics = ({ postData }) => {
  const [currentSegment, setCurrentSegment] = useState(0);
  const [realtimePostData, setRealtimePostData] = useState(postData);
  const [retryAnalysis, { isLoading: isRetrying }] = useRetryAnalysisMutation();

  // Use real-time post data if available, fallback to prop
  const currentPostData = realtimePostData || postData;

  // Polling fallback for when real-time doesn't work
  const { data: polledData } = useGetPostByIdQuery(postData?.post_id, {
    pollingInterval: currentPostData?.analysis_status === 'analyzing' ? 3000 : 0, // Poll every 3 seconds while analyzing
    skip: !postData?.post_id
  });

  // Update with polled data if it's newer
  useEffect(() => {
    if (polledData?.data && polledData.data.analysis_status !== currentPostData?.analysis_status) {
      console.log('📊 Polling detected status change:', currentPostData?.analysis_status, '→', polledData.data.analysis_status);
      setRealtimePostData(polledData.data);
    }
  }, [polledData, currentPostData?.analysis_status]);

  // Set up real-time subscription for analysis status updates
  useEffect(() => {
    if (!postData?.post_id) return;

    console.log('Setting up realtime subscription for post:', postData.post_id, 'type:', typeof postData.post_id);

    const subscription = subscribeToPostAnalysis(postData.post_id, (payload) => {
      console.log('🎯 EmotionAnalytics received realtime update:', payload.new);
      // Update local state with new analysis data
      setRealtimePostData(payload.new);
    });

    return () => {
      console.log('Cleaning up realtime subscription for post:', postData.post_id);
      subscription?.unsubscribe();
    };
  }, [postData?.post_id]);

  // Update local state when prop changes (initial load)
  useEffect(() => {
    setRealtimePostData(postData);
  }, [postData]);

  // Use actual AI insights data if available
  const emotionData = currentPostData?.ai_insights;

  // Debug logging
  console.log('EmotionAnalytics render:', {
    postId: currentPostData?.post_id,
    analysis_status: currentPostData?.analysis_status,
    has_ai_insights: !!currentPostData?.ai_insights,
    insights_type: typeof currentPostData?.ai_insights,
    segments_count: emotionData?.segments?.length || 0
  });

  // Safety check to ensure we have valid segments data
  const hasValidData = emotionData?.segments && Array.isArray(emotionData.segments) && emotionData.segments.length > 0;

  const handleSegmentClick = (segmentIndex) => {
    setCurrentSegment(segmentIndex);
  };

  const formatTime = (seconds) => {
    const mins = Math.floor(seconds / 60);
    const secs = Math.floor(seconds % 60);
    return `${mins}:${secs.toString().padStart(2, '0')}`;
  };

  const getEmotionColor = (category) => {
    switch(category) {
      case 'facial': return 'bg-blue2';
      case 'vocal': return 'bg-navItemActive';
      case 'language': return 'bg-purple';
      default: return 'bg-gray1';
    }
  };

  const getEmotionTheme = (emotion) => {
    return emotionColors[emotion] || emotionColors.contemplation;
  };

  const getCategoryIcon = (category) => {
    switch(category) {
      case 'facial': return <Eye className="w-4 h-4" />;
      case 'vocal': return <Volume2 className="w-4 h-4" />;
      case 'language': return <MessageSquare className="w-4 h-4" />;
      default: return <Brain className="w-4 h-4" />;
    }
  };

  // Show subtle loading state if no AI insights are available yet
  if (!hasValidData) {
    return (
      <div className="w-full max-w-6xl mx-auto p-6 space-y-8 bg-primary1 text-text1 rounded-xl border border-border1/30 relative overflow-hidden">
        {/* Subtle background animation */}
        <div className="absolute inset-0 bg-gradient-to-br from-navItemActive/5 via-transparent to-purple/5 opacity-50 animate-pulse"></div>

        <div className="relative z-10 space-y-6">
          {/* Header with subtle loading indicator */}
          <div className="text-center space-y-3">
            <div className="flex items-center justify-center gap-3">
              <Brain className="w-8 h-8 text-navItemActive/70" />
              <h3 className="text-xl font-semibold text-text1/90">Emotion Analysis</h3>
              <div className="w-2 h-2 bg-navItemActive/60 rounded-full animate-pulse"></div>
            </div>
            <p className="text-text2/80 text-sm max-w-md mx-auto">
              {currentPostData?.analysis_status === 'analyzing' && "AI is analyzing your performance..."}
              {currentPostData?.analysis_status === 'completed' && "Loading insights..."}
              {currentPostData?.analysis_status === 'failed' && "Analysis failed. Please try again."}
              {!currentPostData?.analysis_status && "Upload a video to see emotion analysis."}
            </p>
          </div>

          {/* Subtle skeleton placeholders */}
          <div className="space-y-4">
            {/* Performance metrics skeleton */}
            <div className="bg-secondary2/50 border border-border1/20 rounded-lg p-4">
              <div className="flex items-center gap-2 mb-3">
                <div className="w-4 h-4 bg-navItemActive/30 rounded animate-pulse"></div>
                <div className="w-32 h-4 bg-text2/20 rounded animate-pulse"></div>
              </div>
              <div className="grid grid-cols-3 gap-4">
                {[1, 2, 3].map((i) => (
                  <div key={i} className="space-y-2">
                    <div className="w-full h-3 bg-text2/10 rounded animate-pulse" style={{ animationDelay: `${i * 200}ms` }}></div>
                    <div className="w-3/4 h-2 bg-text2/10 rounded animate-pulse" style={{ animationDelay: `${i * 200 + 100}ms` }}></div>
                  </div>
                ))}
              </div>
            </div>

            {/* Timeline skeleton */}
            <div className="bg-secondary2/50 border border-border1/20 rounded-lg p-4">
              <div className="flex items-center gap-2 mb-3">
                <div className="w-4 h-4 bg-navItemActive/30 rounded animate-pulse"></div>
                <div className="w-40 h-4 bg-text2/20 rounded animate-pulse"></div>
              </div>
              <div className="flex gap-2">
                {[1, 2, 3, 4, 5].map((i) => (
                  <div
                    key={i}
                    className="w-16 h-12 bg-text2/10 rounded animate-pulse"
                    style={{ animationDelay: `${i * 150}ms` }}
                  ></div>
                ))}
              </div>
            </div>
          </div>

          {/* Processing indicator for active analysis */}
          {currentPostData?.analysis_status === 'analyzing' && (
            <div className="flex justify-center">
              <div className="flex items-center gap-2 px-4 py-2 bg-navItemActive/10 border border-navItemActive/20 rounded-full">
                <div className="w-2 h-2 bg-navItemActive rounded-full animate-bounce"></div>
                <div className="w-2 h-2 bg-navItemActive rounded-full animate-bounce" style={{ animationDelay: '0.1s' }}></div>
                <div className="w-2 h-2 bg-navItemActive rounded-full animate-bounce" style={{ animationDelay: '0.2s' }}></div>
                <span className="text-xs text-navItemActive/80 ml-2">Analyzing</span>
              </div>
            </div>
          )}

          {/* Retry button for failed analysis */}
          {currentPostData?.analysis_status === 'failed' && (
            <div className="flex justify-center">
              <button
                className="px-4 py-2 bg-red-600/20 border border-red-600/40 rounded-full text-red-400 text-sm hover:bg-red-600/30 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
                disabled={isRetrying}
                onClick={async () => {
                  try {
                    await retryAnalysis(currentPostData.post_id).unwrap();
                    // Success - the real-time subscription will handle UI updates
                  } catch (error) {
                    console.error('Failed to retry analysis:', error);
                    // Could add toast notification here
                  }
                }}
              >
                {isRetrying ? 'Retrying...' : 'Retry Analysis'}
              </button>
            </div>
          )}
        </div>
      </div>
    );
  }

  // Now we know emotionData is valid, so we can safely access it
  const currentSegmentData = emotionData.segments[currentSegment];

  return (
    <div className="w-full max-w-6xl mx-auto p-6 space-y-8 bg-primary1 text-text1 rounded-xl border border-border1/30 relative overflow-hidden">
      {/* Cinematic spotlight overlay */}
      <div className="absolute inset-0 animate-spotlight-sweep opacity-30 pointer-events-none"></div>
      {/* Header */}
      {/* <div className="text-center space-y-2">
        <div className="flex items-center justify-center gap-2 mb-4">
          <Brain className="w-6 h-6 text-navItemActive" />
          <h2 className="text-2xl font-bold">Emotion Analytics</h2>
          <Badge variant="secondary" className="bg-navItemActive/20 text-navItemActive">
            AI-Powered
          </Badge>
        </div>
        <h3 className="text-lg font-semibold text-text1">{mockEmotionData.title}</h3>
        <div className="flex justify-center gap-4 text-sm text-text2">
          <span className="flex items-center gap-1">
            <Clock className="w-4 h-4" />
            {formatTime(mockEmotionData.performance.duration)}
          </span>
          <span className="flex items-center gap-1">
            <Star className="w-4 h-4" />
            {mockEmotionData.performance.overallScore}/10
          </span>
        </div>
      </div> */}

      {/* Performance Overview with Spotlight Effects */}
      <Card className="bg-secondary2 border-border1 shadow-lg relative overflow-hidden group">
        {/* Spotlight gradient overlay */}
        <div className="absolute inset-0 bg-gradient-to-br from-navItemActive/5 via-transparent to-purple/5 opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>

        <CardHeader className="relative z-10">
          <CardTitle className="flex items-center gap-2 text-text1">
            <TrendingUp className="w-5 h-5 text-navItemActive" />
            Performance Metrics
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-6 relative z-10">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            {/* Overall Score */}
            <div className="space-y-3 p-4 rounded-lg bg-primary1/50 border border-border1/30 hover:border-navItemActive/50 transition-all duration-300 hover:shadow-lg hover:shadow-navItemActive/20">
              <div className="flex justify-between items-center">
                <span className="text-sm text-text2 font-medium">Overall Score</span>
                <span className="text-lg font-bold text-navItemActive">{emotionData.performance.overallScore}/10</span>
              </div>
              <Progress value={emotionData.performance.overallScore * 10} className="h-3" />
              <div className="text-xs text-text2 opacity-75">Exceptional performance</div>
            </div>

            {/* Emotional Range */}
            <div className="space-y-3 p-4 rounded-lg bg-primary1/50 border border-border1/30 hover:border-purple/50 transition-all duration-300 hover:shadow-lg hover:shadow-purple/20">
              <div className="flex justify-between items-center">
                <span className="text-sm text-text2 font-medium">Emotional Range</span>
                <span className="text-lg font-bold text-purple">{emotionData.performance.emotionalRange}/10</span>
              </div>
              <Progress value={emotionData.performance.emotionalRange * 10} className="h-3" />
              <div className="text-xs text-text2 opacity-75">Outstanding depth</div>
            </div>

            {/* Technical Quality */}
            <div className="space-y-3 p-4 rounded-lg bg-primary1/50 border border-border1/30 hover:border-blue2/50 transition-all duration-300 hover:shadow-lg hover:shadow-blue2/20">
              <div className="flex justify-between items-center">
                <span className="text-sm text-text2 font-medium">Technical Quality</span>
                <span className="text-lg font-bold text-blue2">{emotionData.performance.technicalQuality}/10</span>
              </div>
              <Progress value={emotionData.performance.technicalQuality * 10} className="h-3" />
              <div className="text-xs text-text2 opacity-75">Strong technique</div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Film Strip Timeline */}
      <Card className="bg-secondary2 border-border1 shadow-lg overflow-hidden">
        <CardHeader>
          <CardTitle className="flex items-center gap-2 text-text1">
            <BarChart3 className="w-5 h-5" />
            Emotional Journey Timeline
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-6">
            {/* Film Strip Navigation */}
            <div className="relative">
              {/* Film strip perforations */}
              <div className="absolute top-0 left-0 right-0 h-2 bg-gray-800 flex justify-between items-center px-2">
                {Array.from({ length: 20 }).map((_, i) => (
                  <div key={i} className="w-1 h-1 bg-gray-600 rounded-full"></div>
                ))}
              </div>
              <div className="absolute bottom-0 left-0 right-0 h-2 bg-gray-800 flex justify-between items-center px-2">
                {Array.from({ length: 20 }).map((_, i) => (
                  <div key={i} className="w-1 h-1 bg-gray-600 rounded-full"></div>
                ))}
              </div>

              {/* Film frames */}
              <div className="flex gap-1 overflow-x-auto py-3 px-4 bg-gray-900 border-y-2 border-gray-700">
                {emotionData.segments.map((segment, index) => {
                  const theme = getEmotionTheme(segment.primaryEmotion);
                  const isActive = currentSegment === index;

                  return (
                    <div
                      key={segment.id}
                      onClick={() => handleSegmentClick(index)}
                      className={`
                        relative flex-shrink-0 w-24 h-16 cursor-pointer transition-all duration-300 transform
                        ${isActive ? 'scale-110 z-10' : 'hover:scale-105'}
                        ${isActive ? theme.glow : ''}
                      `}
                    >
                      {/* Film frame */}
                      <div className={`
                        w-full h-full rounded border-2 transition-all duration-300
                        ${isActive ? `${theme.bg} border-white shadow-lg` : 'bg-gray-700 border-gray-600'}
                        ${isActive ? theme.glow : ''}
                      `}>
                        {/* Frame content */}
                        <div className="p-2 h-full flex flex-col justify-between">
                          <div className={`text-xs font-bold ${isActive ? 'text-white' : 'text-gray-300'}`}>
                            {formatTime(segment.timeRange.start)}
                          </div>
                          <div className={`text-xs text-center ${isActive ? theme.text : 'text-gray-400'}`}>
                            {segment.primaryEmotion}
                          </div>
                        </div>
                      </div>

                      {/* Active indicator */}
                      {isActive && (
                        <div className="absolute -bottom-2 left-1/2 transform -translate-x-1/2">
                          <div className={`w-2 h-2 ${theme.bg} rounded-full animate-pulse`}></div>
                        </div>
                      )}
                    </div>
                  );
                })}
              </div>
            </div>

            {/* Current Segment Details with Smooth Transitions */}
            <div className={`
              relative overflow-hidden rounded-lg p-6 space-y-4 transition-all duration-500 ease-in-out
              ${getEmotionTheme(currentSegmentData.primaryEmotion).bg}/10
              border-2 ${getEmotionTheme(currentSegmentData.primaryEmotion).bg}/30
              ${getEmotionTheme(currentSegmentData.primaryEmotion).glow}
            `}>
              {/* Animated background gradient */}
              <div className={`
                absolute inset-0 bg-gradient-to-br
                ${getEmotionTheme(currentSegmentData.primaryEmotion).bg}/5
                via-transparent to-primary1/50
                animate-pulse
              `}></div>

              <div className="relative z-10 flex justify-between items-start">
                <div className="space-y-2">
                  <h4 className={`
                    font-bold text-xl transition-colors duration-300
                    ${getEmotionTheme(currentSegmentData.primaryEmotion).text}
                  `}>
                    {currentSegmentData.title}
                  </h4>
                  <p className="text-text2 text-sm leading-relaxed">{currentSegmentData.description}</p>
                  <div className={`
                    inline-flex items-center gap-2 px-3 py-1 rounded-full text-xs font-medium
                    ${getEmotionTheme(currentSegmentData.primaryEmotion).bg} text-white
                  `}>
                    <div className="w-2 h-2 bg-white rounded-full animate-pulse"></div>
                    {currentSegmentData.primaryEmotion.toUpperCase()}
                  </div>
                </div>
                <div className="flex gap-2">
                  <Button
                    onClick={() => setCurrentSegment(Math.max(0, currentSegment - 1))}
                    disabled={currentSegment === 0}
                    size="sm"
                    variant="outline"
                    className="border-border1"
                  >
                    <ChevronLeft className="w-4 h-4" />
                  </Button>
                  <Button
                    onClick={() => setCurrentSegment(Math.min(emotionData.segments.length - 1, currentSegment + 1))}
                    disabled={currentSegment === emotionData.segments.length - 1}
                    size="sm"
                    variant="outline"
                    className="border-border1"
                  >
                    <ChevronRight className="w-4 h-4" />
                  </Button>
                </div>
              </div>

              {/* Dominant Emotions with Fade-in Animation */}
              <div className="relative z-10 space-y-4">
                <h5 className="font-medium flex items-center gap-2 text-text1">
                  <Heart className={`w-5 h-5 ${getEmotionTheme(currentSegmentData.primaryEmotion).text}`} />
                  Dominant Emotions
                </h5>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  {currentSegmentData.dominantEmotions.map((emotion, index) => (
                    <div
                      key={index}
                      className={`
                        bg-secondary2/80 backdrop-blur-sm border border-border1/50 rounded-lg p-4 space-y-3
                        hover:border-${getEmotionColor(emotion.category).replace('bg-', '')}/50
                        transition-all duration-300 hover:scale-105 hover:shadow-lg
                        animate-smooth-appear
                      `}
                      style={{ animationDelay: `${index * 150}ms` }}
                    >
                      <div className="flex items-center justify-between">
                        <div className="flex items-center gap-3">
                          <div className={`p-2 rounded-full ${getEmotionColor(emotion.category)}/20`}>
                            {getCategoryIcon(emotion.category)}
                          </div>
                          <span className="font-semibold text-sm text-text1">{emotion.name}</span>
                        </div>
                        <Badge
                          variant="secondary"
                          className={`${getEmotionColor(emotion.category)} text-white text-xs font-bold px-2 py-1`}
                        >
                          {Math.round(emotion.confidence * 100)}%
                        </Badge>
                      </div>
                      <Progress value={emotion.confidence * 100} className="h-3" />
                    </div>
                  ))}
                </div>
              </div>

              {/* Key Moments */}
              {currentSegmentData.keyMoments.length > 0 && (
                <div className="space-y-2">
                  <h5 className="font-medium flex items-center gap-2">
                    <Target className="w-4 h-4" />
                    Key Moments
                  </h5>
                  {currentSegmentData.keyMoments.map((moment, index) => (
                    <div key={index} className="bg-blue2/10 border border-blue2/20 rounded-lg p-3">
                      <div className="flex justify-between items-start mb-2">
                        <span className="font-medium text-blue2">{moment.significance}</span>
                        <span className="text-xs text-text2">{formatTime(moment.timestamp)}</span>
                      </div>
                      <p className="text-sm text-text2">{moment.description}</p>
                      <div className="mt-2">
                        <div className="flex justify-between text-xs">
                          <span>Emotion Intensity</span>
                          <span>{Math.round(moment.emotionIntensity * 100)}%</span>
                        </div>
                        <Progress value={moment.emotionIntensity * 100} className="h-1 mt-1" />
                      </div>
                    </div>
                  ))}
                </div>
              )}

              {/* Insights */}
              <div className="space-y-2">
                <h5 className="font-medium flex items-center gap-2">
                  <Lightbulb className="w-4 h-4" />
                  Performance Insights
                </h5>
                <ul className="space-y-1">
                  {currentSegmentData.insights.map((insight, index) => (
                    <li key={index} className="text-sm text-text2 flex items-start gap-2">
                      <span className="text-navItemActive mt-1">•</span>
                      {insight}
                    </li>
                  ))}
                </ul>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Overall Insights & Recommendations */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <Card className="bg-secondary2 border-border1 shadow-lg">
          <CardHeader>
            <CardTitle className="flex items-center gap-2 text-text1">
              <Lightbulb className="w-5 h-5 text-navItemActive" />
              Overall Insights
            </CardTitle>
          </CardHeader>
          <CardContent>
            <ul className="space-y-2">
              {emotionData.overallInsights.map((insight, index) => (
                <li key={index} className="text-sm text-text2 flex items-start gap-2">
                  <span className="text-navItemActive mt-1">✓</span>
                  {insight}
                </li>
              ))}
            </ul>
          </CardContent>
        </Card>

        <Card className="bg-secondary2 border-border1 shadow-lg">
          <CardHeader>
            <CardTitle className="flex items-center gap-2 text-text1">
              <Target className="w-5 h-5 text-hover1" />
              Recommendations
            </CardTitle>
          </CardHeader>
          <CardContent>
            <ul className="space-y-2">
              {(emotionData.recommendations || emotionData.overallInsights || []).map((rec, index) => (
                <li key={index} className="text-sm text-text2 flex items-start gap-2">
                  <span className="text-hover1 mt-1">→</span>
                  {rec}
                </li>
              ))}
            </ul>
          </CardContent>
        </Card>
      </div>

      {/* Data Export/Share Options */}
      <div className="flex justify-center gap-4 pt-4">
        <Dialog>
          <DialogTrigger asChild>
            <Button variant="outline" className="border-border1 hover:bg-secondary2">
              <BarChart3 className="w-4 h-4 mr-2" />
              View Detailed Analytics
            </Button>
          </DialogTrigger>
          <DialogContent className="max-w-4xl bg-secondary2 border-border1">
            <DialogHeader>
              <DialogTitle className="text-text1">Detailed Emotion Analytics Report</DialogTitle>
            </DialogHeader>
            <div className="space-y-4 max-h-96 overflow-y-auto">
              <div className="text-sm text-text2">
                <p className="mb-2">Complete technical analysis including frame-by-frame emotion detection, voice stress analysis, and semantic sentiment scoring.</p>
                <div className="bg-primary1 border border-border1 rounded p-3">
                  <pre className="text-xs overflow-x-auto">
{`Technical Metrics:
- Average Confidence: 87.3%
- Emotion Transitions: 12
- Peak Intensity: 94% (timestamp 34.7s)
- Voice Stress Index: 0.73
- Facial Action Units: AU1(0.8), AU4(0.6), AU6(0.9)
- Language Sentiment: -0.23 (contemplative/melancholic)
`}
                  </pre>
                </div>
              </div>
            </div>
          </DialogContent>
        </Dialog>
        
        <Button variant="outline" className="border-border1 hover:bg-secondary2">
          <Target className="w-4 h-4 mr-2" />
          Export Report
        </Button>
      </div>
    </div>
  );
};

export default EmotionAnalytics;
