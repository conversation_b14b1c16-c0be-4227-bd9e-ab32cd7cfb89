import { createApi, fetchBaseQuery } from "@reduxjs/toolkit/query/react";
import { fetchAccessToken } from "./chatSlice";

export const contactUsApi = createApi({
  reducerPath: "contactUsApi",
  baseQuery: fetchBaseQuery({
    baseUrl: import.meta.env.VITE_API_BASE_URL,
    prepareHeaders: async (headers, { endpoint }) => {
      if (endpoint === "getAllContacts") {
        const token = await fetchAccessToken();
        if(token)
          headers.set("authorization", `Bearer ${token}`);
      }
      return headers;
    },
  }),
  keepUnusedDataFor: 600,
  endpoints: (builder) => ({
    submitContactUsData: builder.mutation({
      query: (contactUsData) => {
        return {
          url: "contact-us/submit_contact_us",
          method: "POST",
          body: contactUsData,
        };
      },
    }),
    getAllContacts: builder.query({
      query: ({ page, limit = 10 }) => {
        return {
          url: "contact-us/get_contacts",
          method: "GET",
          params: { _start: page, _limit: limit },
        };
      },
    }),
  }),
});

export const {
  useSubmitContactUsDataMutation,
  useGetAllContactsQuery,
} = contactUsApi;
