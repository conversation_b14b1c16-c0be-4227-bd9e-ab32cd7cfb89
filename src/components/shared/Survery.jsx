import { NotepadTex<PERSON>, Heart } from 'lucide-react';
import { useEffect, useState } from 'react';
import { Textarea } from '../ui/textarea';
import { Button } from '../ui/button';
import { Rating } from '@fluentui/react-rating';
import { useSubmitSurveyMutation } from '@/redux/features/surveySlice';
import { HttpStatus } from '@/constants/httpStatus';

const Survey = () => {
  const [formTranslateX, setFormTranslateX] = useState('translate-x-[120%]');
  const [formOpen, setFormOpen] = useState(false);
  const [useabilityRating, setUseabilityRating] = useState(null);
  const [overallExperienceRating, setOverallExperienceRating] = useState(null);
  const [featuresDesc, setFeaturesDesc] = useState(null);
  const [isFormSubmitted, setIsFormSubmitted] = useState(false);
  const [submitSurvey] = useSubmitSurveyMutation();

  const toggleFormOpen = () => {
    setFormOpen(!formOpen);
  }

  useEffect(() => {
    formOpen ? setFormTranslateX('translate-x-0') : setFormTranslateX('translate-x-[120%]');
  }, [formOpen]);

  const handleFormSubmit = async (ev) => {
    ev.preventDefault();

    let formData = {
      useabilityRating,
      overallExperienceRating,
      featuresDescription: featuresDesc
    }

    try {
      const response = await submitSurvey(formData);

      if (response.data.status === HttpStatus.OK) {
          setIsFormSubmitted(true);
      } else {
        console.error('Failed to submit survey');
      }
    } catch (error) {
      console.error('Error submitting survey:', error);
    }
  }

  const handleClose = () => {
    setFormOpen(false);
  }

  return (
    <section className="">
      <div className='fixed right-0 top-[45%] hover:cursor-pointer rounded-l-xl p-3 bg-navItemActive shadow-md shadow-black z-50'
        onClick={toggleFormOpen}
      >
        <NotepadText size={20} />
      </div>

      <div className={`fixed right-0 top-[5%] max-sm:ml-3 mr-14 transition-all duration-500 ${formTranslateX} h-[90%] max-sm:w-[83%] sm:w-[300px] md:w-[500px] lg:w-[600px] border-2 rounded-2xl z-50 bg-secondary2`}>
        {
          !isFormSubmitted ? (
            <form className='p-5' onSubmit={handleFormSubmit}>
              <h1 className='text-2xl font-extrabold text-center pb-10'>Feedback Form</h1>
              <div className='grid grid-cols-1 gap-2'>
                <div className='flex justify-between'>
                  <label className=''>Usability</label>
                  <div >
                    <Rating name='rating' color="brand" onChange={(_, data) => setUseabilityRating(data.value)} />
                  </div>
                </div>
                <div className='flex justify-between'>
                  <label className=''>Overall Experience</label>
                  <div >
                    <Rating onChange={(_, data) => setOverallExperienceRating(data.value)} />
                  </div>
                </div>
                <div className='d-block w-100 '>
                  <label className='pr-3'>Features/Monologue you would like to add/see</label>
                  <div className='flex-1 mt-2' >
                    <Textarea name="featuresDesc" className="bg-secondary2" rows='8' onChange={(e) => setFeaturesDesc(e.target.value)}></Textarea>
                  </div>
                </div>
                <Button type="submit" className="mt-10">Submit</Button>
              </div>
            </form>
          ) : (
            <div className='flex flex-col justify-center items-center gap-4 h-full w-full text-2xl'>
              <div className='flex gap-2'>
                <Heart size={30} fill='red' color='red' />
                <span className='font-extralight'>Thank you for your valuable feedback.</span>
              </div>
              <Button type="submit" onClick={handleClose}>Close</Button>
            </div>
          )
        }

      </div>


    </section>
  )
}

export default Survey;