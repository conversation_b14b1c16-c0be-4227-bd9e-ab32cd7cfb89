import { Injectable, ExecutionContext } from '@nestjs/common';
import {
  ThrottlerGuard,
  ThrottlerOptions,
  ThrottlerException,
} from '@nestjs/throttler';

@Injectable()
export class WsThrottlerGuard extends ThrottlerGuard {
  async handleRequest({
    context,
    limit,
    ttl,
    throttler,
  }: {
    context: ExecutionContext;
    limit: number;
    ttl: number;
    throttler: ThrottlerOptions;
  }): Promise<boolean> {
    const client = context.switchToWs().getClient();

    const ip = client.conn.remoteAddress;
    const key = this.generateKey(context, ip, throttler.name);
    const out = await this.storageService.increment(
      key,
      ttl,
      limit,
      60000, // blocked for a minute
      'default',
    );
    if (out.totalHits > limit) {
      throw new ThrottlerException();
    }

    return true;
  }
}
