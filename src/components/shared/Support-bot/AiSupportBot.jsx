import React, { useState, useEffect, useRef } from "react";
import { Button } from "@/components/ui/button";
import {
  Dialog,
  DialogClose,
  DialogContent,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { Textarea } from "@/components/ui/textarea";
import { Bot, BotMessageSquare, Send, X } from "lucide-react";
import { useGetBotResponseMutation } from "@/redux/features/chatApi";
import { useLocation } from "react-router-dom";

const PulsatingDots = () => {
  return (
    <div className="flex space-x-1">
      {[...Array(3)].map((_, i) => (
        <div
          key={i}
          className="w-2 h-2 bg-navItemActive rounded-full animate-pulse"
          style={{ animationDelay: `${i * 0.15}s` }}
        />
      ))}
    </div>
  );
};

const AiSupportBot = ({ className, isDialogOpen, toggleDialog }) => {
  const [inputMessage, setInputMessage] = useState("");
  const [getBotResponse] = useGetBotResponseMutation();
  const [isLoading, setIsLoading] = useState(false);
  const messagesEndRef = useRef(null);
  const [isFirstPrompt, setIsFirstPrompt] = useState(true);
  const [isMobile, setIsMobile] = useState(false);
  const [isShownBotBtn, setIsShownBotBtn] = useState(true);
  const location = useLocation();
  const [messages, setMessages] = useState([
    {
      text: "Hi, Welcome to Audition Rooms AI Chat Bot. How can I help you today?",
      isUser: false,
      timestamp: "3 min ago",
    },
  ]);

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: "smooth" });
  };

  useEffect(scrollToBottom, [messages]);

  useEffect(() => {
    const checkScreenSize = () => {
      setIsMobile(window.innerWidth < 1024);
    };

    checkScreenSize();
    window.addEventListener("resize", checkScreenSize);

    return () => window.removeEventListener("resize", checkScreenSize);
  }, []);

  // Not to be shown the Bot Assistant on Sigin/SignUp/Coaches-Apply pages
  useEffect(() => {
    const path = location.pathname;
    if (
      path === "/signin" ||
      path === "/signup" ||
      path === "/coach-apply" ||
      path === "/reset-password"
    ) {
      setIsShownBotBtn(false);
    } else {
      setIsShownBotBtn(true);
    }
  }, [location.pathname]);

  if (!isShownBotBtn) {
    return null;
  }

  const handleSendMessage = async () => {
    if (inputMessage.trim() === "" || isLoading) return;

    const userMessage = inputMessage.trim();
    setInputMessage("");
    setIsLoading(true);

    const newUserMessage = { text: userMessage, isUser: true };

    setMessages((prevMessages) => [...prevMessages, newUserMessage]);
    const messageTexts = messages.map((message) => message.text);

    try {
      const payload = {
        message: userMessage,
        conv_history: isFirstPrompt
          ? [newUserMessage.text]
          : messageTexts.slice(1).concat(newUserMessage.text),
      };
      const response = await getBotResponse(payload).unwrap();
      setMessages((prevMessages) => [
        ...prevMessages,
        { text: response, isUser: false },
      ]);
      if (isFirstPrompt) {
        setIsFirstPrompt(false);
      }
    } catch (error) {
      console.error("Failed to get bot response:", error);
      setMessages((prevMessages) => [
        ...prevMessages,
        {
          text: "Sorry, I couldn't process your request. Please try again.",
          isUser: false,
        },
      ]);
    } finally {
      setIsLoading(false);
    }
  };

  const handleKeyPress = (e) => {
    if (e.key === "Enter" && !e.shiftKey) {
      e.preventDefault();
      handleSendMessage();
    }
  };

  return (
    <React.Fragment>
      {isShownBotBtn && (
        <div className={className}>
          <Dialog
            open={isDialogOpen}
            onOpenChange={toggleDialog}
            className="relative"
          >
            <DialogTrigger asChild>
              {!isMobile && (
                <Button className="fixed w-16 h-16 rounded-full bg-navItemActive text-text1 hover:bg-hover1 bottom-8 right-8 z-[50] flex justify-center items-center shadow-lg transition-all duration-300 ease-in-out transform hover:scale-110">
                  <BotMessageSquare size={30} />
                </Button>
              )}
            </DialogTrigger>
            <DialogTitle>
              <DialogContent className="bg-primary1 px-0 sm:px-6 gap-0 sm:gap-4 flex flex-col h-[90%] lg:h-[95%] max-w-2xl lg:rounded-lg border border-border1 shadow-2xl discord-scrollbar">
                <div className="flex justify-between p-4 border-b border-border1 sticky top-0">
                  <div className="flex items-center gap-2">
                    <div className="bg-navItemActive rounded-full p-2">
                      <Bot size={24} className="text-text1" />
                    </div>
                    <h3 className="text-xl font-semibold text-text1">
                      AI Support Assistant
                    </h3>
                  </div>
                  <DialogClose>
                    <X className="h-6 w-6 text-white" />
                  </DialogClose>
                </div>
                <div className="flex-grow overflow-y-auto discord-scrollbar p-4 space-y-4 bg-chatSec">
                  {messages.map((message, index) => (
                    <div
                      key={index}
                      className={`flex flex-col ${
                        message.isUser ? "items-end" : "items-start"
                      }`}
                    >
                      <div
                        className={`p-3 rounded-lg max-w-[80%] ${
                          message.isUser
                            ? "bg-navItemActive text-text1 text-xs lg:text-sm"
                            : "bg-gray-700 text-text1 text-xs lg:text-sm"
                        }`}
                      >
                        {message.text}
                      </div>
                    </div>
                  ))}
                  {isLoading && (
                    <div className="self-start p-3 rounded-lg  border-navItemActive flex items-center space-x-2">
                      <span className="text-text2 flex justify-center items-center gap-2">
                        <Bot size={20} className="animate-pulse" /> AI is
                        thinking
                      </span>
                      <PulsatingDots />
                    </div>
                  )}
                  <div ref={messagesEndRef} />
                </div>
                <div className="p-4 border-t border-border1 bg-primary1 ">
                  <div className="flex items-center gap-2 sticky bottom-0">
                    <Textarea
                      value={inputMessage}
                      onChange={(e) => setInputMessage(e.target.value)}
                      onKeyPress={handleKeyPress}
                      placeholder="Type your message..."
                      className="bg-chatSec text-text1 placeholder-text2 p-2 rounded-md border border-border1 flex-grow text-sm focus:outline-none focus:ring-2 focus:ring-navItemActive resize-none"
                      disabled={isLoading}
                      rows={1}
                    />
                    <Button
                      onClick={handleSendMessage}
                      className="bg-navItemActive text-text1 hover:bg-text1 hover:text-primary1"
                      disabled={isLoading}
                    >
                      <Send size={18} />
                    </Button>
                  </div>
                </div>
              </DialogContent>
            </DialogTitle>
          </Dialog>
        </div>
      )}
    </React.Fragment>
  );
};

export default AiSupportBot;
