import React, { useState, useEffect, forwardRef } from "react";
import { motion, AnimatePresence } from "framer-motion";
import { Avatar, AvatarImage, AvatarFallback } from "@/components/ui/avatar";
import { Card, CardContent } from "@/components/ui/card";
import {
  HoverCard,
  HoverCardContent,
  HoverCardTrigger,
} from "@/components/ui/hover-card";
import { FaUserGroup } from "react-icons/fa6";
import { ArrowRight, BookOpenIcon, UserIcon } from "lucide-react";
import CTAReuseable from "./CTAReuseable";
import { useNavigate } from "react-router-dom";

const testimonials = [
  {
    id: 1,
    name: "@StageStarRising",
    role: "Theater Enthusiast",
    quote:
      "The community here is incredibly supportive. I've grown so much as a performer!",
    avatar:
      "https://images.unsplash.com/photo-1641214880602-3c9cd45eadf5?q=80&w=1974&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D",
  },
  {
    id: 2,
    name: "@ScriptMaster",
    role: "Aspiring Playwright",
    quote:
      "AuditionRooms helped me refine my writing skills. The feedback is invaluable!",
    avatar:
      "https://images.unsplash.com/photo-1595347097560-69238724e7bd?q=80&w=1974&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D",
  },
  {
    id: 3,
    name: "@VocalVirtuoso",
    role: "Musical Theater Performer",
    quote: "Found my vocal coach here. My range has improved dramatically!",
    avatar:
      "https://images.unsplash.com/photo-1641214880602-3c9cd45eadf5?q=80&w=1974&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D",
  },
  {
    id: 4,
    name: "@DramaticDreamer",
    role: "Drama Student",
    quote: "The monologue database is a goldmine for audition prep!",
    avatar:
      "https://images.unsplash.com/photo-1595347097560-69238724e7bd?q=80&w=1974&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D",
  },
  {
    id: 5,
    name: "@ImprovMaster",
    role: "Comedy Actor",
    quote: "The improv workshops here have sharpened my quick-thinking skills.",
    avatar:
      "https://images.unsplash.com/photo-1641214880602-3c9cd45eadf5?q=80&w=1974&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D",
  },
];

const TestimonialSection = () => {
  const [activeTestimonial, setActiveTestimonial] = useState(testimonials[0]);
  const [autoRotate, setAutoRotate] = useState(true);

  useEffect(() => {
    let interval;
    if (autoRotate) {
      interval = setInterval(() => {
        setActiveTestimonial((prev) => {
          const nextIndex =
            (testimonials.findIndex((t) => t.id === prev.id) + 1) %
            testimonials.length;
          return testimonials[nextIndex];
        });
      }, 3000);
    }
    return () => clearInterval(interval);
  }, [autoRotate]);

  return (
    <section id="testimonials" className="bg-primary1 text-text1 py-20">
      <div className="container mx-auto px-4">
        <h2 className="text-4xl font-bold text-center mb-16">
          Join our Vibrant Community
        </h2>

        {/* Animated Testimonial Avatars */}
        <div className="relative flex justify-center items-center w-full h-[300px] md:h-[400px] mb-16 ">
          {testimonials.map((testimonial, index) => (
            <motion.div
              key={testimonial.id}
              className="absolute"
              animate={{
                x: `${
                  Math.cos((2 * Math.PI * index) / testimonials.length) *
                  (window.innerWidth < 768 ? 100 : 150)
                }px`,
                y: `${
                  Math.sin((2 * Math.PI * index) / testimonials.length) *
                  (window.innerWidth < 768 ? 100 : 150)
                }px`,
                scale: activeTestimonial.id === testimonial.id ? 1.3 : 1,
                zIndex: activeTestimonial.id === testimonial.id ? 10 : 1,
              }}
              transition={{
                type: "spring",
                stiffness: 100,
                damping: 10,
                mass: 0.5,
              }}
            >
              <HoverCard>
                <HoverCardTrigger asChild>
                  <motion.div
                    whileHover={{ scale: 1.1 }}
                    whileTap={{ scale: 0.95 }}
                  >
                    <Avatar
                      className="h-[60px] w-[60px] md:h-[80px] md:w-[80px] cursor-pointer border-2 border-[#6D66FB] transition-all duration-300 hover:border-[#8A84FF] hover:shadow-lg"
                      onClick={() => {
                        setActiveTestimonial(testimonial);
                        setAutoRotate(false);
                      }}
                    >
                      <AvatarImage
                        src={testimonial.avatar}
                        alt={testimonial.name}
                      />
                      <AvatarFallback>
                        {testimonial.name.slice(1, 3)}
                      </AvatarFallback>
                    </Avatar>
                  </motion.div>
                </HoverCardTrigger>
                <HoverCardContent className="w-80 bg-secondary2 border-border1 shadow-xl ">
                  <div className="flex justify-between space-x-4">
                    <Avatar>
                      <AvatarImage src={testimonial.avatar} />
                      <AvatarFallback>
                        {testimonial.name.slice(1, 3)}
                      </AvatarFallback>
                    </Avatar>
                    <div className="space-y-1">
                      <h4 className="text-sm font-semibold text-text1">
                        {testimonial.name}
                      </h4>
                      <p className="text-sm text-text2">{testimonial.role}</p>
                      <p className="text-sm text-text1">{testimonial.quote}</p>
                    </div>
                  </div>
                </HoverCardContent>
              </HoverCard>
            </motion.div>
          ))}
        </div>

        {/* Active Testimonial */}
        <AnimatePresence mode="wait">
          <motion.div
            key={activeTestimonial.id}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -20 }}
            transition={{ duration: 0.3 }}
          >
            <Card className="bg-secondary2 border-border1 shadow-xl max-w-2xl mx-auto">
              <CardContent className="p-6 md:p-8">
                <p className="text-xl md:text-2xl text-text1 mb-4 md:mb-6 italic leading-relaxed">
                  "{activeTestimonial.quote}"
                </p>
                <div className="flex items-center">
                  <Avatar className="h-10 w-10 md:h-12 md:w-12 mr-3 md:mr-4">
                    <AvatarImage
                      src={activeTestimonial.avatar}
                      alt={activeTestimonial.name}
                    />
                    <AvatarFallback>
                      {activeTestimonial.name.slice(1, 3)}
                    </AvatarFallback>
                  </Avatar>
                  <div>
                    <p className="text-text1 font-semibold text-base md:text-lg">
                      {activeTestimonial.name}
                    </p>
                    <p className="text-text2 text-sm">
                      {activeTestimonial.role}
                    </p>
                  </div>
                </div>
              </CardContent>
            </Card>
          </motion.div>
        </AnimatePresence>

        {/* Statistics Section */}
        <div className=" mx-auto px-4 py-16 md:py-20">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8 mb-12 md:mb-16">
            {[
              { icon: FaUserGroup, value: "1,000+", label: "Active Members" },
              {
                icon: BookOpenIcon,
                value: "500+",
                label: "Monologues Shared",
              },
              {
                icon: UserIcon,
                value: "200+",
                label: "Acting Coaches Listed",
              },
            ].map((stat, index) => (
              <Card
                key={index}
                className="bg-secondary2 border-border1 overflow-hidden group hover:shadow-lg transition-all duration-300"
              >
                <CardContent className="p-6 md:p-8 flex items-center space-x-4 md:space-x-6">
                  <div className="bg-hover1 p-3 md:p-4 rounded-full group-hover:scale-110 transition-transform duration-300">
                    <stat.icon className="w-6 h-6 md:w-8 md:h-8 text-white" />
                  </div>
                  <div>
                    <p className="font-bold text-text1 text-2xl md:text-4xl group-hover:text-hover1 transition-colors duration-300">
                      {stat.value}
                    </p>
                    <p className="text-text2 text-base md:text-lg mt-1">
                      {stat.label}
                    </p>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </div>
    </section>
  );
};

export default TestimonialSection;
