import { HttpStatus, Injectable } from '@nestjs/common';
import { PrismaClient } from '@prisma/client';
import { DatabaseService } from '../database/database.service';
import { CustomResponse } from '../utils/CustomResponse';
import { StripeService } from '../stripe/stripe.service';
import { PLAN_STATUS, PLANS_TYPE } from 'src/types/users.enum';

@Injectable()
export class PlansService {
  private prisma: PrismaClient;

  constructor(
    private readonly dbService: DatabaseService,
    private readonly stripeService: StripeService,
  ) {
    this.prisma = dbService.getClient();
  }

  async getPlans(plan_type: PLANS_TYPE): Promise<CustomResponse> {
    try {
      const plans = await this.prisma.plans.findMany({
        where: {
          type: plan_type,
          status: PLAN_STATUS.ACTIVE,
          OR: [
            {
              expire_date: {
                gt: new Date(),
              },
            },
            {
              expire_date: null,
            },
          ],
        },
        orderBy: {
          plan_price_monthly: 'asc',
        },
      });
      return {
        data: plans,
        error: false,
        message: 'Plans fetched successfully',
        status: HttpStatus.OK,
      };
    } catch (err) {
      console.log(err);
      return {
        data: null,
        error: true,
        message: 'Unable to fetch plans',
        status: HttpStatus.INTERNAL_SERVER_ERROR,
      };
    }
  }

  async getAllPlans(): Promise<CustomResponse> {
    try {
      const plans = await this.prisma.plans.findMany({
        where: {
          status: PLAN_STATUS.ACTIVE,
          OR: [
            {
              expire_date: {
                gt: new Date(),
              },
            },
            {
              expire_date: null,
            },
          ],
        },
        orderBy: {
          plan_price_monthly: 'asc',
        },
      });
      return {
        data: plans,
        error: false,
        message: 'Plans fetched successfully',
        status: HttpStatus.OK,
      };
    } catch (err) {
      console.log(err);
      return {
        data: null,
        error: true,
        message: 'Unable to fetch plans',
        status: HttpStatus.INTERNAL_SERVER_ERROR,
      };
    }
  }

  async getCurrentPlan(user_id: string): Promise<CustomResponse> {
    try {
      const plan = await this.prisma.public_users.findFirstOrThrow({
        where: {
          user_id,
        },
        include: {
          plans: true,
        },
      });
      return {
        data: plan.plans,
        error: false,
        message: 'Current Plan Fetched',
        status: HttpStatus.OK,
      };
    } catch (err) {
      console.log(err);
      return {
        data: null,
        error: true,
        message: 'Unable to fetch plan',
        status: HttpStatus.INTERNAL_SERVER_ERROR,
      };
    }
  }

  async buyPlan(email: string, plan_id: number): Promise<CustomResponse> {
    try {
      const payment_url = await this.stripeService.createPaymentLink(
        email,
        plan_id,
        process.env.PAYMENT_USER_SUCCESS_REDIRECT,
        process.env.PAYMENT_USER_CANCEL_REDIRECT,
      );
      if (payment_url == null) {
        return {
          data: null,
          error: true,
          message: 'Error buying plan',
          status: HttpStatus.INTERNAL_SERVER_ERROR,
        };
      }
      return {
        data: payment_url,
        error: false,
        message: 'Plans payment url fetched',
        status: HttpStatus.OK,
      };
    } catch (err) {
      console.log(err);
      return {
        data: null,
        error: true,
        message: 'Error buying plan',
        status: HttpStatus.INTERNAL_SERVER_ERROR,
      };
    }
  }

  async getStripeDashboard(email: string): Promise<CustomResponse> {
    try {
      const url = await this.stripeService.getDashboard(email);
      if (url == null) {
        return {
          data: null,
          error: true,
          message: 'Error fetching dashboard',
          status: HttpStatus.INTERNAL_SERVER_ERROR,
        };
      }
      return {
        data: url,
        error: false,
        message: 'Dashboard fetched',
        status: HttpStatus.OK,
      };
    } catch (err) {
      console.log(err);
      return {
        data: null,
        error: true,
        message: 'Error fetching dashboard',
        status: HttpStatus.INTERNAL_SERVER_ERROR,
      };
    }
  }

  async cancelPlan(email: string): Promise<CustomResponse> {
    try {
      const cancel = await this.stripeService.cancelSubscription(email);
      if (cancel != null) {
        return {
          data: null,
          error: false,
          message: 'Subscription Cancelled',
          status: HttpStatus.OK,
        };
      }
      return {
        data: null,
        error: true,
        message: 'Error Cancelling Subscription',
        status: HttpStatus.INTERNAL_SERVER_ERROR,
      };
    } catch (err) {
      console.log(err);
      return {
        data: null,
        error: true,
        message: 'Error Cancelling Subscription',
        status: HttpStatus.INTERNAL_SERVER_ERROR,
      };
    }
  }
}
