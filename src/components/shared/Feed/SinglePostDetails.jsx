import React, { useState } from "react";
import { useNavigate, usePara<PERSON>, Link } from "react-router-dom";
import { useGetPostByIdQuery } from "@/redux/features/postSlice";
import { ArrowLeft, Plus } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { Skeleton } from "@/components/ui/skeleton";
import Upload from "@/pages/Upload";
import Post from "./Post";
import { useToast } from "@/components/ui/use-toast";
import { useSelector } from "react-redux";
import EmotionAnalytics from "@/components/EmotionAnalytics";

const SinglePostDetails = () => {
  const { postId } = useParams();
  const navigate = useNavigate();
  const [isReplying, setIsReplying] = useState(false);
  const { session } = useSelector((state) => state.user);
  const isCoach = session?.user?.user_metadata?.role === "coach";
  const handleReply = () => {
    setIsReplying(true);
  };
  const handleCloseReply = () => {
    setIsReplying(false);
  };
  const { toast } = useToast();
  const { data, isLoading, error } = useGetPostByIdQuery(postId, {
    refetchOnMountOrArgChange: true,
  });

  const renderChildPosts = (childPosts, level = 1) => {
    return childPosts.map((childPost) => (
      <div
        key={childPost.post_id}
        className={`ml-2 sm:ml-${
          level * 4
        } mt-2 sm:mt-4 border-l-2 border-gray-700 pl-2 sm:pl-4`}
      >
        <Post
          postId={childPost.post_id}
          showReplyButton={true}
          onReply={handleReply}
        />
        {childPost.children_posts &&
          renderChildPosts(childPost.children_posts, level + 1)}
      </div>
    ));
  };

  if (isReplying) {
    return (
      <Upload parentId={postId} isReply={true} onClose={handleCloseReply} />
    );
  }

  return (
    <div className="flex justify-center flex-col items-center w-full  sm:px-0">
      <div className="w-full lg:w-[92%]">
        {/* Enhanced Header Layout with Centered Title and Action Buttons */}
        <div className="sticky lg:relative top-0 flex items-center justify-between bg-[#202020] lg:bg-transparent p-4 lg:p-0 lg:py-9 z-40">
          {/* Left side - Back button */}
          <div className="flex items-center gap-2">
            <Button
              className="px-2 py-1 sm:px-3 sm:py-2 bg-navItemActive hover:bg-hover1"
              onClick={() => navigate("/")}
            >
              <ArrowLeft size={16} />
            </Button>
          </div>

          {/* Center - Feed Title */}
          <div className="flex-1 text-center">
            <h1 className="text-xl lg:text-3xl font-extrabold text-text1">Feed</h1>
          </div>

          {/* Right side - Action buttons */}
          <div className="flex items-center gap-2 sm:gap-3">

            {!isCoach && (
              <Link to="/upload">
                <Button className="px-2 py-1 sm:px-3 sm:py-2 bg-navItemActive hover:bg-hover1 transition-all duration-300">
                  <Plus size={16} className="mr-1 sm:mr-2" />
                  <span className="hidden sm:inline">Monologue Library</span>
                  <span className="sm:hidden">Library</span>
                </Button>
              </Link>
            )}
          </div>
        </div>
        {isLoading ? (
          <div className="shadow-lg rounded-lg overflow-hidden">
            <div className="p-3 sm:p-6">
              <div className="flex justify-between mb-2">
                <div>
                  <Skeleton className="h-4 sm:h-6 w-20 sm:w-40 mb-1 sm:mb-2" />
                  <Skeleton className="h-3 sm:h-4 w-16 sm:w-24" />
                </div>
                <Skeleton className="h-6 w-6 sm:h-8 sm:w-8 rounded-full" />
              </div>
              <Skeleton className="h-3 sm:h-4 w-full mb-1 sm:mb-2" />
              <Skeleton className="h-3 sm:h-4 w-full mb-1 sm:mb-2" />
              <Skeleton className="h-3 sm:h-4 w-3/4 mb-2 sm:mb-4" />
              <Skeleton className="h-40 sm:h-64 w-full mb-2 sm:mb-4" />
              <div className="flex justify-between items-center">
                <div className="flex items-center">
                  <Skeleton className="h-6 sm:h-8 w-16 sm:w-24 mr-2 sm:mr-4" />
                  <Skeleton className="h-3 sm:h-4 w-12 sm:w-16" />
                </div>
              </div>
            </div>
          </div>
        ) : error ? (
          toast({
            title: "Error",
            description: error.message,
            variant: "destructive",
          })
        ) : (
          <>
            {/* Enhanced Layout with Better Spacing */}
            <div className="mt-4 lg:mt-6">
              <div className="flex flex-col lg:flex-row gap-6 lg:gap-8">
                {/* Left side - Video (optimized size) */}
                <div className="lg:w-2/5 xl:w-1/3">
                  <Post
                    postId={postId}
                    showReplyButton={true}
                    onReply={handleReply}
                  />
                </div>

                {/* Right side - Analytics Dashboard (larger) */}
                <div className="lg:w-3/5 xl:w-2/3">
                  <EmotionAnalytics postData={data?.data} />
                </div>
              </div>
            </div>
            
            {data?.data?.children_posts &&
              renderChildPosts(data.data.children_posts)}
          </>
        )}
      </div>
    </div>
  );
};

export default SinglePostDetails;
