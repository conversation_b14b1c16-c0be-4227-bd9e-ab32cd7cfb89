import React, { useState, useCallback, useEffect } from "react";
import { Plus } from "lucide-react";
import Feed from "@/components/shared/Feed/Feed";
import ScreenTopSection from "@/components/ScreenTopSection";
import { useGetPostsQuery } from "@/redux/features/postSlice";
import { useSelector } from "react-redux";

const FeedPage = () => {
  const [page, setPage] = useState(1);
  const [allPosts, setAllPosts] = useState([]);
  const { data, isLoading, isFetching, error } = useGetPostsQuery(page);
  const { session } = useSelector((state) => state.user);
  const isCoach = session?.user?.user_metadata?.role === "coach";

  useEffect(() => {
    if (data?.data) {
      setAllPosts((prevPosts) => {
        const newPosts = data.data.filter(
          (newPost) =>
            !prevPosts.some(
              (existingPost) => existingPost.post_id === newPost.post_id
            )
        );
        return [...prevPosts, ...newPosts];
      });
    }
  }, [data]);

  const loadMorePosts = useCallback(() => {
    if (!isFetching && data?.data.length > 0) {
      setPage((prevPage) => prevPage + 1);
    }
  }, [isFetching, data]);

  // Empty all posts to get fresh data/posts
  const resetPostsAfterDelete = () => {
    setAllPosts([]);
  }

  return (
    <div className="flex justify-center flex-col items-center w-full">
      <div className="lg:w-[52%] w-full ">
        <ScreenTopSection
          page="Feed"
          link="/upload"
          btnText="Monologue Library"
          Icon={Plus}
          isCoach={isCoach}
        />

        <Feed
          posts={allPosts}
          isLoading={isLoading}
          isFetching={isFetching}
          loadMorePosts={loadMorePosts}
          hasMorePosts={data?.data.length > 0}
          onDelete={resetPostsAfterDelete}
        />
      </div>
    </div>
  );
};

export default FeedPage;
