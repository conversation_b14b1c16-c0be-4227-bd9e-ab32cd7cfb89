import { useState, useEffect } from "react";
import { Search, X } from "lucide-react";

const MonologueSearchBar = ({
  value,
  onChange,
  placeholder = "Search monologues...",
  className = "",
  debounceTime = 500, 
}) => {
  const [debouncedValue, setDebouncedValue] = useState(value);

  useEffect(() => {
    const timer = setTimeout(() => {
      setDebouncedValue(value);
    }, debounceTime);

    return () => clearTimeout(timer); // cleanup on each input change
  }, [value, debounceTime]);

  const handleClear = () => {
    onChange("");
  };

  const handleChange = (e) => {
    onChange(e.target.value);
  };

  return (
    <div className={`relative ${className}`}>
      <Search
        className="absolute left-3 top-1/2 -translate-y-1/2 h-5 w-5 text-text2/50 
                  group-focus-within:text-secondary2 transition-colors duration-200"
      />
      <input
        type="text"
        value={value}
        onChange={handleChange}
        placeholder={placeholder}
        className="w-full h-11 pl-11 pr-10 rounded-lg border border-text2/20
                   bg-primary1 placeholder:text-text2/50 text-text1
                   focus:outline-none focus:ring-2 focus:ring-secondary2/50
                   focus:border-secondary2 transition-all duration-200"
      />
      {debouncedValue && (
        <button
          onClick={handleClear}
          className="absolute right-3 top-1/2 -translate-y-1/2 p-1 rounded-full
                   hover:bg-text2/10 transition-colors duration-200"
        >
          <X className="h-4 w-4 text-text2/50 hover:text-text2" />
        </button>
      )}
    </div>
  );
};

export default MonologueSearchBar;
