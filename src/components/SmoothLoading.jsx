import React from "react";
import { motion } from "framer-motion";

const SmoothLoading = () => {
  return (
    <div className="fixed inset-0 bg-[#171717] flex flex-col items-center justify-center">
      <motion.div
        className="text-[#FAFAFA] text-5xl font-bold mb-8"
        initial={{ opacity: 0, y: -20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.6, ease: "easeOut" }}
      >
        AuR
      </motion.div>

      <motion.div
        className="relative"
        initial={{ opacity: 0, scale: 0.8 }}
        animate={{ opacity: 1, scale: 1 }}
        transition={{ duration: 0.8, delay: 0.3, ease: "easeOut" }}
      >
        <svg
          width="200"
          height="200"
          viewBox="0 0 200 200"
          fill="none"
          xmlns="http://www.w3.org/2000/svg"
        >
          {/* Circular animation */}
          <motion.circle
            cx="100"
            cy="100"
            r="90"
            stroke="#FAFAFA"
            strokeWidth="2"
            strokeLinecap="round"
            initial={{ pathLength: 0, rotate: -90 }}
            animate={{ pathLength: 1, rotate: 270 }}
            transition={{ duration: 1.5, repeat: Infinity, ease: "linear" }}
          />

          {/* Star icon */}
          <motion.path
            d="M100 50L111.2 88.8L150 100L111.2 111.2L100 150L88.8 111.2L50 100L88.8 88.8L100 50Z"
            fill="#FAFAFA"
            initial={{ rotate: 0 }}
            animate={{ rotate: 360 }}
            transition={{ duration: 20, repeat: Infinity, ease: "linear" }}
          />
        </svg>
      </motion.div>

      <motion.div
        className="text-[#71717A] text-lg mt-8"
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.6, delay: 0.6, ease: "easeOut" }}
      >
        AuditionRooms
      </motion.div>

      <motion.div
        className="mt-12 flex space-x-3"
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ delay: 1 }}
      >
        {[0, 1, 2].map((index) => (
          <motion.div
            key={index}
            className="w-2 h-2 bg-[#FAFAFA] rounded-full"
            animate={{
              scale: [1, 1.5, 1],
              opacity: [0.7, 1, 0.7],
            }}
            transition={{
              duration: 1.5,
              repeat: Infinity,
              repeatType: "reverse",
              ease: "easeInOut",
              delay: index * 0.3,
            }}
          />
        ))}
      </motion.div>
    </div>
  );
};

export default SmoothLoading;
