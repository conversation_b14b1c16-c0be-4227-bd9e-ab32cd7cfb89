import { HttpStatus, Injectable } from '@nestjs/common';
import { PrismaClient } from '@prisma/client';
import { DatabaseService } from '../database/database.service';
import { CustomResponse } from '../utils/CustomResponse';
import { SupabaseClient } from '@supabase/supabase-js';
import { CreateMonologueDto } from './dto/create-monologues.dto';

@Injectable()
export class MonologuesService {
  private prisma: PrismaClient;
  private supbase: SupabaseClient;
  constructor(private readonly dbService: DatabaseService) {
    this.prisma = dbService.getClient();
    this.supbase = dbService.getSupabaseClient();
  }

  async getMonologues(
    page,
    limit,
    searchString = null,
    chipString = null,
    genderString = null,
    categoryString = null,
  ): Promise<CustomResponse> {
    try {
      page = page || 1;
      limit = limit || 25;
      const offset = (Number(page) - 1) * Number(limit);
      const nLimit = Number(offset) + Number(limit) - 1;
      let monologuesQuery = this.supbase
        .from('monologues')
        .select('*', { count: 'exact' })
        .is('deleted', null)
        .range(offset, nLimit);

      // Only apply search filter if searchString is provided
      if (searchString && searchString.trim() !== '') {
        monologuesQuery = monologuesQuery.or(
          `content.ilike.%${searchString}%,title.ilike.%${searchString}%,character.ilike.%${searchString}%`,
        );
      }

      if (
        (chipString && chipString.trim() !== '' && chipString !== 'All') ||
        (genderString &&
          genderString.trim() !== '' &&
          genderString !== 'All') ||
        (categoryString &&
          categoryString.trim() !== '' &&
          categoryString !== 'All')
      ) {
        const orConditions = [];
        // Add condition for chipString if it's not empty
        if (chipString && chipString.trim() !== '' && chipString !== 'All') {
          orConditions.push(`chip.ilike.%${chipString}%`);
        }

        // Add condition for maleString if it's not empty
        if (
          genderString &&
          genderString.trim() !== '' &&
          genderString !== 'All'
        ) {
          orConditions.push(`gender.ilike.%${genderString}%`);
        }

        // Add condition for categoryString if it's not empty
        if (
          categoryString &&
          categoryString.trim() !== '' &&
          categoryString !== 'All'
        ) {
          orConditions.push(`category.ilike.%${categoryString}%`);
        }

        // If we have any conditions, apply the OR filter
        if (orConditions.length > 0) {
          monologuesQuery = monologuesQuery.or(orConditions.join(','));
        }
      }
      // Execute the query
      const monologues = await monologuesQuery;
      return {
        data: monologues.data,
        count: monologues.count,
        error: false,
        message: 'Monologues fetched successfully',
        status: HttpStatus.OK,
      };
    } catch (err) {
      console.log(err);
      return {
        data: null,
        error: true,
        message: 'Unable to fetch monologues',
        status: HttpStatus.INTERNAL_SERVER_ERROR,
      };
    }
  }

  async insertRecord(
    monologuesRecord: CreateMonologueDto[],
  ): Promise<CustomResponse> {
    try {
      const lMonologue = await this.supbase
        .from('monologues')
        .insert(monologuesRecord);
      if (lMonologue && lMonologue?.error == null) {
        return {
          data: lMonologue.data,
          error: false,
          message: 'Monologues created successfully',
          status: HttpStatus.OK,
        };
      } else {
        return {
          data: null,
          error: true,
          message: 'Monologue is not inserted.',
          status: HttpStatus.INTERNAL_SERVER_ERROR,
        };
      }
    } catch (err) {
      console.log(err);
      return {
        data: null,
        error: true,
        message: 'Monologue is not inserted.',
        status: HttpStatus.INTERNAL_SERVER_ERROR,
      };
    }
  }

  async getMonologuesFilter() {
    try {
      const uniqueValues = await this.prisma.monologues.groupBy({
        by: ['gender', 'category', 'chip'],
        _count: {
          gender: true,
          category: true,
          chip: true,
        },
        orderBy: {
          gender: 'asc', // You can order by any column if needed
        },
      });

      return {
        data: uniqueValues,
        error: false,
        message: 'Monologue filter fetched',
        status: HttpStatus.OK,
      };
    } catch (err) {
      console.log(err);
      return {
        data: null,
        error: true,
        message: 'Monologue is not inserted.',
        status: HttpStatus.INTERNAL_SERVER_ERROR,
      };
    }
  }

  async deleteMonologue(monologue_id: number): Promise<CustomResponse> {
    try {
      const monologue = await this.supbase
        .from('monologues')
        .update({ deleted: true })
        .eq('monologue_id', monologue_id);
      return {
        data: monologue,
        error: false,
        message: 'Monologue removed successfully',
        status: HttpStatus.OK,
      };
    } catch (err) {
      console.log(err);
      return {
        data: null,
        error: true,
        message: 'Error removing upvoting',
        status: HttpStatus.INTERNAL_SERVER_ERROR,
      };
    }
  }
}
