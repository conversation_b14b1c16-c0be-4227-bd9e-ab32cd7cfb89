import {
  Body,
  Controller,
  Get,
  HttpException,
  Post,
  Query,
  Req,
  UseGuards,
} from '@nestjs/common';
import { UserGuard } from '../auth/guards/user.guard';
import { ContactUsService } from './contact-us.service';
import { SubmitContactUsDto } from './dto/submit-contact-us.dto';

@Controller('contact-us')
export class ContactUsController {
  constructor(private readonly contactUsService: ContactUsService) {}

  @Post('/submit_contact_us')
  async submitContactUs(@Req() req, @Body() contactUsData: SubmitContactUsDto) {
    const contactUsCreateInput = {
      full_name: contactUsData.fullName,
      phone: contactUsData.phone,
      email: contactUsData.email,
      message: contactUsData.message,
    };

    return await this.contactUsService.create(contactUsCreateInput);
  }

  @Get('get_contacts')
  @UseGuards(UserGuard)
  async fetchMonologueFilter(@Query('_start') page: number, @Query('_limit') limit: number) {
    const resp = await this.contactUsService.getContacts(page, limit);
    if (resp.error) {
      throw new HttpException(resp.message, resp.status);
    }
    return resp;
  }
}
