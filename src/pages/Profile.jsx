import { useState, useCallback } from "react";
import { useQuery } from "@tanstack/react-query";
import ProfileVideosSection from "@/components/ProfileVideosSection";
import UserProfileList from "@/components/shared/Profile/UserProfileList";
import ScreenTopSection from "@/components/ScreenTopSection";
import { Upload } from "lucide-react";
import { useSelector } from "react-redux";

const Profile = () => {
  const [publicVideos, setPublicVideos] = useState([]);
  const [privateVideos, setPrivateVideos] = useState([]);
  const [changeStatus, setChangeStatus] = useState('');
  const { session } = useSelector((state) => state.user);
  const isCoach = session?.user?.user_metadata?.role === "coach";

  const handleSetPublicVideos = useCallback((videos) => {
    setPublicVideos(videos);
  }, []);

  const handleSetPrivateVideos = useCallback((videos) => {
    setPrivateVideos(videos);
  }, []);

  const handleChangeStatus = useCallback((status) => {
    setChangeStatus(status);
  }, []);

  return (
    <div className="flex flex-col justify-center items-center w-full">
      <div className="lg:w-[52%] w-full ">
        <ScreenTopSection
          page="Profile"
          link="/upload"
          btnText="Upload"
          Icon={Upload}
          isCoach={isCoach}
        />

        <UserProfileList
          setPublicVideos={handleSetPublicVideos}
          setPrivateVideos={handleSetPrivateVideos}
          changeStatus={changeStatus}
        />
        <div data-aos="fade-in" data-aos-once="true" className="flex flex-col">
          <ProfileVideosSection
            publicVideos={publicVideos}
            privateVideos={privateVideos}
            setChangeStatus={handleChangeStatus}
          />
        </div>
      </div>
    </div>
  );
};

export default Profile;
