/* eslint-disable @typescript-eslint/no-unused-vars */
import { Injectable } from '@nestjs/common';
import { PrismaClient } from '@prisma/client';
import { DatabaseService } from '../database/database.service';
import Stripe from 'stripe';
import { delay } from '../utils/functions';

@Injectable()
export class StripeService {
  private prisma: PrismaClient;
  private stripe: Stripe;

  constructor(private readonly dbService: DatabaseService) {
    this.prisma = dbService.getClient();
    this.stripe = new Stripe(process.env.STRIPE_SECRET_KEY);
  }

  // stripe event flow when product added: product.created (product details) -> plan.created (plan pricing)
  async handleStripeEvents(body: any) {
    try {
      if (body.type === 'product.created') {
        await this.prisma.plans.create({
          data: {
            plan_name: body.data.object.name,
            plan_product_id: body.data.object.id,
            description: body.data.object.description,
          },
        });
      } else if (body.type === 'product.updated') {
        let activePlan;
        try {
          activePlan = await this.stripe.plans.retrieve(
            body.data.object.default_price,
          );
        } catch (error) {
          activePlan = await this.stripe.prices.retrieve(
            body.data.object.default_price,
          );
        }

        if (activePlan) {
          const monthlyCharge =
            activePlan.amount > 0 ? activePlan.amount / 100 : 0;
          await this.prisma.plans.updateMany({
            data: {
              plan_name: body.data.object.name,
              description: body.data.object.description,
              plan_price_id: body.data.object.default_price,
              plan_price_monthly: monthlyCharge,
              plan_price_annual: monthlyCharge * 12,
              status: body.data.object.active ? 1 : 0,
            },
            where: {
              plan_product_id: body.data.object.id,
            },
          });
        }
      } else if (body.type === 'plan.created') {
        // Sometimes product and plan events arrive at the same time, add a little artificial
        // delay to the plan creation to make sure the product has been added to db.
        await delay(1000); // delay in milliseconds
        const planAmt =
          body.data.object.amount > 0 ? body.data.object.amount / 100 : 0;
        await this.prisma.plans.updateMany({
          data: {
            plan_price_monthly: planAmt, // amount is in smallest currency denomination, for USD amount is in cents
            plan_price_annual: planAmt * 12,
            plan_price_id: body.data.object.id,
          },
          where: {
            plan_product_id: body.data.object.product,
          },
        });
      } else if (body.type === 'product.deleted') {
        await this.prisma.plans.deleteMany({
          where: {
            plan_product_id: body.data.object.id,
          },
        });
      } else if (body.type === 'customer.subscription.updated') {
        const subscription = await this.stripe.subscriptions.retrieve(
          body.data.object.id,
        );
        // update subscription id if the subscription got cancel
        if (
          subscription &&
          ((subscription.cancel_at != null &&
            new Date(subscription.cancel_at).toISOString() <=
              new Date().toISOString()) ||
            subscription.status == 'canceled')
        ) {
          await this.prisma.public_users.updateMany({
            where: {
              stripe_customer_id: subscription.customer.toString(),
              subscription_id: subscription.id.toString(),
            },
            data: {
              plan_in_use: null,
              subscription_id: null,
            },
          });
        }
      } else if (body.type === 'customer.subscription.deleted') {
        const subscription = await this.stripe.subscriptions.retrieve(
          body.data.object.id,
        );

        if (subscription && subscription.status == 'canceled') {
          await this.prisma.public_users.updateMany({
            where: {
              subscription_id: subscription.id.toString(),
              stripe_customer_id: subscription.customer.toString(),
            },
            data: {
              plan_in_use: null,
              subscription_id: null,
            },
          });
        }
      } else if (body.type === 'invoice.payment_succeeded') {
        const invoice = await this.stripe.invoices.retrieve(
          body.data.object.id,
        );

        if (invoice && invoice.status === 'paid') {
          // check if customer already have an active subscription
          // if yes, then cancel the subscription first
          const existingSubs = await this.prisma.public_users.findFirst({
            where: { stripe_customer_id: invoice.customer.toString() },
          });

          if (existingSubs && existingSubs.subscription_id) {
            try {
              await this.stripe.subscriptions.cancel(
                existingSubs.subscription_id,
              );
            } catch (error) {
              console.log(
                'Unable to cancel existing subscription before new one',
                error,
              );
            }
          }

          const subscription = await this.stripe.subscriptions.retrieve(
            invoice.subscription.toString(),
          );
          const plan = await this.prisma.plans.findFirst({
            where: {
              plan_price_id: subscription['plan']['id'],
              // plan_price_id: 'price_1PyWt8HvbaU8UnPv9rbgSGqv',
            },
          });
          if (plan) {
            await this.prisma.public_users.updateMany({
              where: {
                stripe_customer_id: invoice.customer.toString(),
              },
              data: {
                plan_in_use: plan.plan_id,
                subscription_id: invoice.subscription.toString(),
                subscription_expires_at: plan.expire_date,
              },
            });
          }
        }
      }
    } catch (err) {
      console.log('error here:', err);
    }
  }
  async getStripeCustomer(email: string) {
    try {
      const customer = await this.prisma.public_users.findFirst({
        where: { email },
      });

      if (customer.stripe_customer_id) {
        // retrieve customer from stripe first to check valid customer
        try {
          const stripeCustomer = await this.stripe.customers.retrieve(
            customer.stripe_customer_id,
          );
          // returns user if exists already
          if (stripeCustomer && !stripeCustomer.deleted) {
            return customer;
          }
        } catch (error) {
          console.log('Error retrieving a customer:', error);
        }
      }
      const customer_name = `${customer.metadata['full_name']}`;
      const stripe_customer = await this.stripe.customers.create({
        email,
        name: customer_name,
      });
      const customer_data = await this.prisma.public_users.updateMany({
        where: { email },
        data: { stripe_customer_id: stripe_customer.id },
      });
      return customer_data[0];
    } catch (err) {
      console.error(err);
      return null;
    }
  }

  async cancelSubscription(email) {
    try {
      const customer = await this.getStripeCustomer(email);
      console.log({ customer });
      if (
        customer &&
        customer.stripe_customer_id &&
        customer.plan_in_use &&
        !customer.subscription_id
      ) {
        await this.prisma.public_users.update({
          where: { id: customer.id },
          data: { plan_in_use: null, stripe_customer_id: null },
        });
        return true;
      }

      const subscription = await this.prisma.public_users.findFirstOrThrow({
        where: {
          email,
        },
        select: {
          subscription_id: true,
        },
      });
      const subs = await this.stripe.subscriptions.retrieve(
        subscription.subscription_id.toString(),
      );
      const cancel = await this.stripe.subscriptions.update(
        subscription.subscription_id,
        { cancel_at_period_end: true },
      );
      return cancel;
    } catch (err) {
      console.log(err);
      return null;
    }
  }

  async createPaymentLink(email, plan_id, success_url, cancel_url) {
    try {
      const customer = await this.getStripeCustomer(email);
      if (customer == null) {
        return null;
      }
      const product = await this.prisma.plans.findFirst({
        where: {
          plan_id,
        },
      });

      // if plan is a free plan, then don't go to stripe
      if (+product.plan_price_monthly <= 0) {
        // assign free plan to user
        await this.prisma.public_users.updateMany({
          where: { id: customer.id },
          data: { plan_in_use: plan_id },
        });
        return process.env.PAYMENT_USER_SUCCESS_REDIRECT;
      }

      const session = await this.stripe.checkout.sessions.create({
        success_url: success_url,
        cancel_url: cancel_url,
        customer: customer.stripe_customer_id,
        line_items: [{ price: product.plan_price_id, quantity: 1 }],
        mode: 'subscription',
        allow_promotion_codes: true,
      });
      return session.url;
    } catch (err) {
      console.log(err);
      return null;
    }
  }
  async getDashboard(
    email: string,
    return_url = process.env.PAYMENT_USER_SUCCESS_REDIRECT,
  ) {
    try {
      const customer = await this.getStripeCustomer(email);
      if (customer == null) {
        return null;
      }
      const portal_session = await this.stripe.billingPortal.sessions.create({
        customer: customer.stripe_customer_id,
        return_url: return_url,
      });
      return portal_session.url;
    } catch (err) {
      console.log(err);
      return null;
    }
  }
}
