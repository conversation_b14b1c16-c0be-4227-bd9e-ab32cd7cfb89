import {
  Body,
  Controller,
  Delete,
  Get,
  HttpException,
  HttpStatus,
  Param,
  Patch,
  Post,
  Query,
  Req,
  UploadedFile,
  UploadedFiles,
  UseGuards,
  UseInterceptors,
} from '@nestjs/common';
import { CoachesService } from './coaches.service';
import { AddCoachDto } from './dto/add-coach.dto';
import { UserGuard } from '../auth/guards/user.guard';
import { Roles } from '../auth/decorators/roles.decorator';
import { USER_ROLE } from '../types/users.enum';
import { RolesGuard } from '../auth/guards/role.guard';
import {
  FileFieldsInterceptor,
  FileInterceptor,
  FilesInterceptor,
} from '@nestjs/platform-express';
import { VerifyCoachDto } from './dto/verify-coach.dto';
import { UpdateStatusDto } from './dto/update-status.dto';

@Controller('coaches')
export class CoachesController {
  constructor(private readonly coachesService: CoachesService) {}

  @Post('apply')
  @UseInterceptors(
    FileFieldsInterceptor([
      { name: 'resume', maxCount: 1 },
      { name: 'headshots', maxCount: 10 },
      { name: 'voice_reels', maxCount: 10 },
      { name: 'video_reels', maxCount: 5 },
    ]),
  )
  async apply(
    @Req() req,
    @Body() addCoachDto: AddCoachDto,
    @UploadedFiles()
    files: {
      resume?: Express.Multer.File[];
      headshots?: Express.Multer.File[];
      voice_reels?: Express.Multer.File[];
      video_reels?: Express.Multer.File[];
    },
  ) {
    const resp = await this.coachesService.apply(
      addCoachDto,
      files && files.resume ? files.resume : [],
      files && files.headshots ? files.headshots : [],
      files && files.voice_reels ? files.voice_reels : [],
      files && files.video_reels ? files.video_reels : [],
    );
    if (resp.error) {
      throw new HttpException(resp.message, resp.status);
    }
    return resp;
  }

  @Get('all')
  // @Roles(USER_ROLE.ADMIN, USER_ROLE.COACH, USER_ROLE.NORMAL_USER)
  // @UseGuards(UserGuard, RolesGuard)
  async getAllCoaches(
    @Req() req,
    @Query('expertise') expertise: string,
    @Query('name') name: string,
    @Query('page_no') page_no: number = 1,
  ) {
    const resp = await this.coachesService.getAllCoaches(
      expertise,
      name,
      page_no,
    );
    if (resp.error) {
      throw new HttpException(resp.message, resp.status);
    }
    return resp;
  }

  @Get('specific/:id') // get a specific coach by id
  // @Roles(USER_ROLE.ADMIN, USER_ROLE.COACH, USER_ROLE.NORMAL_USER)
  // @UseGuards(UserGuard, RolesGuard)
  // @UseGuards(UserGuard)
  async getCoach(@Req() req, @Param('id') id: number) {
    if (id == null) {
      throw new HttpException(
        'Coach id cannot be empty',
        HttpStatus.BAD_REQUEST,
      );
    }
    const resp = await this.coachesService.getCoach(id);
    if (resp.error) {
      throw new HttpException(resp.message, resp.status);
    }
    return resp;
  }

  @Get('unverified')
  // @Roles(USER_ROLE.ADMIN)
  // @UseGuards(UserGuard, RolesGuard)
  // @UseGuards(UserGuard)
  async getUnverifiedCoaches(@Req() req) {
    const resp = await this.coachesService.getUnverifiedCoaches();
    if (resp.error) {
      throw new HttpException(resp.message, resp.status);
    }
    return resp;
  }

  @Get('rejected')
  // @Roles(USER_ROLE.ADMIN)
  // @UseGuards(UserGuard, RolesGuard)
  // @UseGuards(UserGuard)
  async getRejectedCoaches(@Req() req) {
    const resp = await this.coachesService.getRejectedCoaches();
    if (resp.error) {
      throw new HttpException(resp.message, resp.status);
    }
    return resp;
  }

  @Get('removed')
  // @Roles(USER_ROLE.ADMIN)
  // @UseGuards(UserGuard, RolesGuard)
  // @UseGuards(UserGuard)
  async getRemovedCoaches(@Req() req) {
    const resp = await this.coachesService.getRemovedCoaches();
    if (resp.error) {
      throw new HttpException(resp.message, resp.status);
    }
    return resp;
  }

  @Get('payment_status')
  @Roles(USER_ROLE.ADMIN)
  @UseGuards(UserGuard, RolesGuard)
  @UseGuards(UserGuard)
  async getCoachPaymentStatus(@Req() req, @Query('coach_id') coach_id: number) {
    const resp = await this.coachesService.getCoachPaymentStatus(coach_id);
    if (resp.error) {
      throw new HttpException(resp.message, resp.status);
    }
    return resp;
  }

  @Delete('/remove/:id')
  // @Roles(USER_ROLE.ADMIN)
  // @UseGuards(UserGuard, RolesGuard)
  async removeCoach(@Req() req, @Param('id') id: number) {
    const resp = await this.coachesService.removeCoach(id);
    if (resp.error) {
      throw new HttpException(resp.message, resp.status);
    }
    return resp;
  }

  @Patch('/reject/:id')
  // @Roles(USER_ROLE.ADMIN)
  // @UseGuards(UserGuard, RolesGuard)
  async rejectCoach(@Req() req, @Param('id') id: number) {
    console.log(id, typeof id);
    const resp = await this.coachesService.rejectCoach(+id);
    if (resp.error) {
      throw new HttpException(resp.message, resp.status);
    }
    return resp;
  }

  @Patch('/reopen/:id')
  // @Roles(USER_ROLE.ADMIN)
  // @UseGuards(UserGuard, RolesGuard)
  async reopenCoach(@Req() req, @Param('id') id: number) {
    console.log(id, typeof id);
    const resp = await this.coachesService.reopenCoach(+id);
    if (resp.error) {
      throw new HttpException(resp.message, resp.status);
    }
    return resp;
  }

  @Patch('/verify')
  // @Roles(USER_ROLE.ADMIN)
  // @UseGuards(UserGuard, RolesGuard)
  async verifyCoach(@Req() req, @Body() verifyCoachDto: VerifyCoachDto) {
    const resp = await this.coachesService.verifyCoach(verifyCoachDto.email);
    if (resp.error) {
      throw new HttpException(resp.message, resp.status);
    }
    return resp;
  }

  @Patch('/update_status')
  @Roles(USER_ROLE.COACH)
  @UseGuards(UserGuard, RolesGuard)
  async updateStatus(@Req() req, @Body() updateStatusDto: UpdateStatusDto) {
    const resp = await this.coachesService.updateStatus(
      req.user.id,
      updateStatusDto.new_status,
    );
    if (resp.error) {
      throw new HttpException(resp.message, resp.status);
    }
    return resp;
  }

  @Post('/upload_resume')
  @UseInterceptors(FileInterceptor('file'))
  @Roles(USER_ROLE.COACH)
  @UseGuards(UserGuard, RolesGuard)
  async uploadResume(@Req() req, @UploadedFile() resume: Express.Multer.File) {
    const resp = await this.coachesService.uploadResume(req.user.id, resume);
    if (resp.error) {
      throw new HttpException(resp.message, resp.status);
    }
    return resp;
  }

  @Post('/upload_headshots')
  @UseInterceptors(FilesInterceptor('file'))
  @Roles(USER_ROLE.COACH)
  @UseGuards(UserGuard, RolesGuard)
  async uploadHeadshots(
    @Req() req,
    @UploadedFiles() headshots: Array<Express.Multer.File>,
  ) {
    const resp = await this.coachesService.uploadHeadshots(
      req.user.id,
      headshots,
    );
    if (resp.error) {
      throw new HttpException(resp.message, resp.status);
    }
    return resp;
  }

  @Post('/upload_voice_reels')
  @UseInterceptors(FilesInterceptor('file'))
  @Roles(USER_ROLE.COACH)
  @UseGuards(UserGuard, RolesGuard)
  async uploadVoiceReels(
    @Req() req,
    @UploadedFiles() voice_reels: Array<Express.Multer.File>,
  ) {
    const resp = await this.coachesService.uploadVoiceReels(
      req.user.id,
      voice_reels,
    );
    if (resp.error) {
      throw new HttpException(resp.message, resp.status);
    }
    return resp;
  }

  @Post('/upload_video_reels')
  @UseInterceptors(FilesInterceptor('file'))
  @Roles(USER_ROLE.COACH)
  @UseGuards(UserGuard, RolesGuard)
  async uploadVideoReels(
    @Req() req,
    @UploadedFiles() video_reels: Array<Express.Multer.File>,
  ) {
    const resp = await this.coachesService.uploadVideoReels(
      req.user.id,
      video_reels,
    );
    if (resp.error) {
      throw new HttpException(resp.message, resp.status);
    }
    return resp;
  }
}
