import { Injectable, Logger } from '@nestjs/common';
import { OpenAI } from 'openai';
import { HumeAnalysisResult } from './hume-ai.service';

export interface InsightResult {
  title: string;
  performance: {
    duration: number;
    overallScore: number;
    emotionalRange: number;
    technicalQuality: number;
  };
  segments: Array<{
    id: string;
    title: string;
    description: string;
    timeRange: { start: number; end: number };
    primaryEmotion: string;
    dominantEmotions: Array<{
      name: string;
      confidence: number;
      category: 'facial' | 'vocal' | 'language';
    }>;
    keyMoments: Array<{
      timestamp: number;
      significance: string;
      description: string;
      emotionIntensity: number;
    }>;
    insights: string[];
  }>;
  overallInsights: string[];
}

@Injectable()
export class AzureOpenAiService {
  private readonly logger = new Logger(AzureOpenAiService.name);
  private readonly client: OpenAI;

  constructor() {
    this.client = new OpenAI({
      apiKey: process.env.AZURE_OPENAI_API_KEY,
      baseURL: `${process.env.AZURE_OPENAI_ENDPOINT}/openai/deployments/${process.env.AZURE_OPENAI_DEPLOYMENT_NAME}`,
      defaultQuery: { 'api-version': process.env.AZURE_OPENAI_API_VERSION || '2024-02-15-preview' },
      defaultHeaders: {
        'api-key': process.env.AZURE_OPENAI_API_KEY,
      },
    });
  }

  async getSystemPrompt() {
    return `You are an expert acting coach and performance analyst with deep knowledge of emotional expression, voice analysis, and theatrical technique. You analyze audition videos to provide detailed, constructive feedback to actors.

Given CSV emotion detection data from HumeAI (face.csv and prosody.csv), create a comprehensive segmented performance analysis in the exact JSON format specified below.

**REQUIRED OUTPUT FORMAT:**
{
  "title": "Brief description of the performance or scene",
  "performance": {
    "duration": 62,
    "overallScore": 8.7,
    "emotionalRange": 9.2,
    "technicalQuality": 8.3
  },
  "segments": [
    {
      "id": "segment-1",
      "title": "Descriptive segment title",
      "description": "What happens in this segment",
      "timeRange": { "start": 4, "end": 15 },
      "primaryEmotion": "contemplation",
      "dominantEmotions": [
        { "name": "Contemplation", "confidence": 0.89, "category": "facial" },
        { "name": "Uncertainty", "confidence": 0.84, "category": "vocal" },
        { "name": "Philosophical", "confidence": 0.91, "category": "language" }
      ],
      "keyMoments": [
        {
          "timestamp": 6.5,
          "significance": "Initial hesitation",
          "description": "The slight pause before 'that is the question' shows internal conflict",
          "emotionIntensity": 0.76
        }
      ],
      "insights": [
        "Excellent use of vocal dynamics to show internal debate",
        "Physical tension visible in shoulders and jaw",
        "Language analysis shows sophisticated moral reasoning"
      ]
    }
  ],
  "overallInsights": [
    "Overall performance feedback",
    "Key strengths observed",
    "Areas for continued development"
  ]
}

**ANALYSIS GUIDELINES:**

1. **Segment Creation**:
   - Divide the performance into 3-6 meaningful segments based on emotional shifts in the CSV data
   - Each segment should be 8-20 seconds long
   - Look for clear emotional transitions in the data to define segment boundaries
   - Give each segment a descriptive title and explanation

2. **Emotion Categories**:
   - "facial": From face.csv - visual expressions, micro-expressions (Joy, Sadness, Anger, Fear, etc.)
   - "vocal": From prosody.csv - tone, pace, vocal quality, stress patterns
   - "language": Inferred from prosody text and vocal patterns - semantic content, subtext

3. **Scoring Criteria** (0-10 scale):
   - overallScore: Overall performance quality and emotional authenticity
   - emotionalRange: Variety and depth of emotions expressed
   - technicalQuality: Voice control, diction, physical technique

4. **Key Moments**:
   - Identify 1-2 significant moments per segment from the CSV data
   - Use actual timestamps from the data
   - Explain why these moments are emotionally significant

5. **Insights Style**:
   - Be specific and reference the actual emotion data
   - Use professional acting terminology
   - Focus on what the actor did well
   - Reference specific techniques (vocal dynamics, physical choices, etc.)

**DATA INTERPRETATION**:
- Use the CSV timestamps to create accurate time ranges
- Reference actual emotion confidence scores from the data
- Look for patterns and peaks in the emotion data to identify key moments
- Base segment divisions on clear emotional transitions in the data

**TONE**: Professional, encouraging, specific, and constructive. Write as an experienced acting coach who wants to help the performer grow while acknowledging their strengths.

**IMPORTANT**: Respond ONLY with valid JSON in the exact format specified above. Do not include any additional text, explanations, or markdown formatting.`;
  }

  /**
   * Generate insights from HumeAI expression analysis
   */
  async generateInsights(
    humeAnalysis: HumeAnalysisResult,
    postContent?: string
  ): Promise<InsightResult> {
    try {
      this.logger.log(`Generating insights for HumeAI job: ${humeAnalysis.job_id}`);

      const systemPrompt = await this.getSystemPrompt();
      const userPrompt = this.buildInsightPrompt(humeAnalysis, postContent);

      const response = await this.client.chat.completions.create({
        model: process.env.AZURE_OPENAI_DEPLOYMENT_NAME,
        messages: [
          {
            role: 'system',
            content: systemPrompt
          },
          {
            role: 'user',
            content: userPrompt
          }
        ],
        temperature: 0.7,
        max_tokens: 16000,
        response_format: { type: 'json_object' }
      });

      const content = response.choices[0]?.message?.content;
      if (!content) {
        throw new Error('No response content from Azure OpenAI');
      }

      const insights = JSON.parse(content) as InsightResult;

      // Validate and ensure all required fields are present
      const validatedInsights: InsightResult = {
        title: insights.title || 'Audition Performance Analysis',
        performance: {
          duration: insights.performance?.duration || 60,
          overallScore: insights.performance?.overallScore || 7.0,
          emotionalRange: insights.performance?.emotionalRange || 7.0,
          technicalQuality: insights.performance?.technicalQuality || 7.0
        },
        segments: Array.isArray(insights.segments)
          ? insights.segments.map((segment, index) => ({
              id: segment.id || `segment-${index + 1}`,
              title: segment.title || `Segment ${index + 1}`,
              description: segment.description || 'Performance segment',
              timeRange: segment.timeRange || { start: index * 15, end: (index + 1) * 15 },
              primaryEmotion: segment.primaryEmotion || 'neutral',
              dominantEmotions: Array.isArray(segment.dominantEmotions)
                ? segment.dominantEmotions
                : [{ name: 'Neutral', confidence: 0.7, category: 'facial' as const }],
              keyMoments: Array.isArray(segment.keyMoments)
                ? segment.keyMoments
                : [],
              insights: Array.isArray(segment.insights)
                ? segment.insights
                : ['Good emotional expression in this segment']
            }))
          : [{
              id: 'segment-1',
              title: 'Performance Analysis',
              description: 'Overall performance segment',
              timeRange: { start: 0, end: 60 },
              primaryEmotion: 'neutral',
              dominantEmotions: [{ name: 'Neutral', confidence: 0.7, category: 'facial' as const }],
              keyMoments: [],
              insights: ['Continue practicing and exploring emotional range.']
            }],
        overallInsights: Array.isArray(insights.overallInsights)
          ? insights.overallInsights
          : ['Good effort in your audition performance.', 'Continue developing your emotional range.']
      };

      this.logger.log(`Successfully generated insights for job: ${humeAnalysis.job_id}`);
      return validatedInsights;

    } catch (error) {
      this.logger.error(`Failed to generate insights: ${error.message}`, error.stack);
    }
  }

  /**
   * Build the prompt for Azure OpenAI based on raw CSV data from HumeAI
   */
  private buildInsightPrompt(humeAnalysis: HumeAnalysisResult, postContent?: string): string {
    const csvData = humeAnalysis.raw_csv_content || 'No CSV data available';

    return `Analyze this audition performance based on the HumeAI emotion detection data provided below. Create a segmented analysis following your system instructions.

**PERFORMANCE CONTEXT:**
${postContent ? `Performance Description: ${postContent}` : 'Audition performance analysis'}

**HUMEAI EMOTION DETECTION DATA:**
The following CSV data contains emotion measurements over time:

${csvData}

**DATA STRUCTURE NOTES:**
- face.csv contains facial expression data with timestamps and emotion confidence scores
- prosody.csv contains vocal/speech analysis with text transcripts and emotion scores
- Use the timestamps to create meaningful segments based on emotional transitions
- Reference actual confidence scores and timestamps in your analysis
- Look for patterns, peaks, and transitions in the emotion data

Please provide a comprehensive segmented analysis in the required JSON format.`;
  }

  /**
   * Health check for Azure OpenAI service
   */
  async healthCheck(): Promise<boolean> {
    try {
      const response = await this.client.chat.completions.create({
        model: process.env.AZURE_OPENAI_DEPLOYMENT_NAME,
        messages: [{ role: 'user', content: 'Hello' }],
        max_tokens: 5
      });
      
      return !!response.choices[0]?.message?.content;
    } catch (error) {
      this.logger.error(`Azure OpenAI health check failed: ${error.message}`);
      return false;
    }
  }
}
