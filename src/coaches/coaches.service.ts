import { HttpStatus, Injectable } from '@nestjs/common';
import { PrismaClient } from '@prisma/client';
import { DatabaseService } from '../database/database.service';
import { AddCoachDto } from './dto/add-coach.dto';
import { CustomResponse } from '../utils/CustomResponse';
import { delay } from '../utils/functions';
import { COACH_APPLICATION_STATUS, USER_ROLE } from '../types/users.enum';
import { SupabaseClient } from '@supabase/supabase-js';

@Injectable()
export class CoachesService {
  private readonly prisma: PrismaClient;
  private supabase: SupabaseClient;

  constructor(private readonly dbService: DatabaseService) {
    this.prisma = dbService.getClient();
    this.supabase = dbService.getSupabaseClient();
  }

  async apply(
    addCoachDto: AddCoachDto,
    resume: Express.Multer.File[],
    headshots: Express.Multer.File[],
    voice_reels: Express.Multer.File[],
    video_reels: Express.Multer.File[],
  ): Promise<CustomResponse> {
    try {
      /*
      flow: coach fills application form, this includes personal info along with
      media. Coach data entered into table, media uploaded to buckets. User not 
      created yet. Once admin verifies coach then a new user is created and an 
      otp email is sent to coach
      */
      const existing_application = await this.prisma.coaches.findMany({
        where: {
          email: addCoachDto.email,
        },
      });
      if (existing_application.length > 0) {
        if (
          existing_application[0].application_status ==
          COACH_APPLICATION_STATUS.PENDING
        ) {
          return {
            data: null,
            error: true,
            message: 'An existing pending application already exists.',
            status: HttpStatus.BAD_REQUEST,
          };
        } else if (
          existing_application[0].application_status ==
          COACH_APPLICATION_STATUS.ACCEPTED
        ) {
          return {
            data: null,
            error: true,
            message: 'Your application was already accepted.',
            status: HttpStatus.BAD_REQUEST,
          };
        } else if (
          existing_application[0].application_status ==
            COACH_APPLICATION_STATUS.REJECTED ||
          existing_application[0].application_status ==
            COACH_APPLICATION_STATUS.REMOVED
        ) {
          await this.prisma.coaches.deleteMany({
            where: {
              email: addCoachDto.email,
            },
          });
        }
      }
      const resp = await this.prisma.coaches.create({
        data: {
          expertise: addCoachDto.expertise,
          email: addCoachDto.email,
          metadata: {
            gender: addCoachDto.gender,
            full_name: addCoachDto.full_name,
          },
          verified: false,
        },
      });
      let resume_url, headshot_urls, voice_reel_urls, video_reel_urls: string[];
      if (resume && resume.length > 0) {
        resume_url = await this.uploadMultipleFiles('coach_media', resume);
      }
      if (headshots && headshots.length > 0) {
        headshot_urls = await this.uploadMultipleFiles(
          'coach_media',
          headshots,
        );
      }
      if (voice_reels && voice_reels.length > 0) {
        voice_reel_urls = await this.uploadMultipleFiles(
          'coach_media',
          voice_reels,
        );
      }
      if (video_reels && video_reels.length > 0) {
        video_reel_urls = await this.uploadMultipleFiles(
          'coach_media',
          video_reels,
        );
      }
      const updated = await this.prisma.coaches.updateMany({
        where: {
          email: addCoachDto.email,
        },
        data: {
          resumes: {
            push: resume_url ? resume_url : [],
          },
          headshots: {
            push: headshot_urls ? headshot_urls : [],
          },
          voice_reels: {
            push: voice_reel_urls ? voice_reel_urls : [],
          },
          video_reels: {
            push: video_reel_urls ? video_reel_urls : [],
          },
        },
      });

      return {
        data: resp,
        error: false,
        message: 'Coach added',
        status: HttpStatus.OK,
      };
    } catch (err) {
      console.log(err);
      return {
        data: null,
        error: true,
        message: 'Error adding coach',
        status: HttpStatus.INTERNAL_SERVER_ERROR,
      };
    }
  }

  async getAllCoaches(
    expertise: string,
    name: string,
    page_no: number,
  ): Promise<CustomResponse> {
    try {
      let where = {};
      if (expertise) {
        where['expertise'] = {};
        where['expertise']['has'] = expertise;
      }
      if (name) {
        where['metadata'] = {};
        where['metadata']['path'] = ['full_name'];
        where['metadata']['string_contains'] = name;
      }
      where['verified'] = true;
      where['application_status'] = COACH_APPLICATION_STATUS.ACCEPTED;
      const resp = await this.prisma.coaches.findMany({
        where: where,
        skip: (page_no - 1) * 10,
        take: 10,
        orderBy: {
          created_at: 'desc',
        },
        select: {
          id: true,
          email: true,
          created_at: true,
          expertise: true,
          metadata: true,
          status: true,
          user_id: true,
          users: {
            select: {
              users: {
                select: {
                  profile_picture_url: true,
                },
              },
            },
          },
        },
      });
      resp.forEach((item) => {
        item['profile_picture_url'] = item.users.users[0].profile_picture_url;
        delete item.users;
      });
      return {
        data: resp,
        error: false,
        message: 'Coaches fetched',
        status: HttpStatus.OK,
      };
    } catch (err) {
      console.log(err);
      return {
        data: null,
        error: true,
        message: 'Error fetching coaches',
        status: HttpStatus.INTERNAL_SERVER_ERROR,
      };
    }
  }

  async getUnverifiedCoaches(): Promise<CustomResponse> {
    try {
      const resp = await this.supabase
        .from('coaches')
        .select('*')
        .eq('verified', false)
        .eq('application_status', COACH_APPLICATION_STATUS.PENDING)
        .order('created_at', { ascending: false });
      return {
        data: resp.data,
        error: false,
        message: 'Unverified coaches fetched',
        status: HttpStatus.OK,
      };
    } catch (err) {
      console.log(err);
      return {
        data: null,
        error: true,
        message: 'Error fetching unverified coaches',
        status: HttpStatus.INTERNAL_SERVER_ERROR,
      };
    }
  }

  async getRejectedCoaches(): Promise<CustomResponse> {
    try {
      const resp = await this.supabase
        .from('coaches')
        .select('*')
        .eq('application_status', COACH_APPLICATION_STATUS.REJECTED)
        .order('created_at', { ascending: false });
      return {
        data: resp.data,
        error: false,
        message: 'Rejected coaches fetched',
        status: HttpStatus.OK,
      };
    } catch (err) {
      console.log(err);
      return {
        data: null,
        error: true,
        message: 'Error fetching rejected coaches',
        status: HttpStatus.INTERNAL_SERVER_ERROR,
      };
    }
  }

  async getRemovedCoaches(): Promise<CustomResponse> {
    try {
      const resp = await this.supabase
        .from('coaches')
        .select('*')
        .eq('application_status', COACH_APPLICATION_STATUS.REMOVED)
        .order('created_at', { ascending: false });
      return {
        data: resp.data,
        error: false,
        message: 'Removed coaches fetched',
        status: HttpStatus.OK,
      };
    } catch (err) {
      console.log(err);
      return {
        data: null,
        error: true,
        message: 'Error fetching removed coaches',
        status: HttpStatus.INTERNAL_SERVER_ERROR,
      };
    }
  }

  async getCoachPaymentStatus(id: number): Promise<CustomResponse> {
    try {
      const details = await this.prisma.public_users.findMany({
        where: {
          id,
        },
        select: {
          plans: true,
        },
      });
      if (details[0].plans == null) {
        return {
          data: null,
          error: false,
          message: 'No plan subscribed to',
          status: HttpStatus.OK,
        };
      } else {
        return {
          data: details[0].plans,
          error: false,
          message: 'No plan subscribed to',
          status: HttpStatus.OK,
        };
      }
    } catch (err) {
      console.log(err);
      return {
        data: null,
        error: true,
        message: 'Error fetching coach payment status',
        status: HttpStatus.INTERNAL_SERVER_ERROR,
      };
    }
  }

  async getCoach(id: number): Promise<CustomResponse> {
    try {
      const resp = await this.supabase.from('coaches').select('*').eq('id', id);
      return {
        data: resp.data[0],
        error: false,
        message: 'Coach fetched',
        status: HttpStatus.OK,
      };
    } catch (err) {
      console.log(err);
      return {
        data: null,
        error: true,
        message: 'Error fetching coach',
        status: HttpStatus.INTERNAL_SERVER_ERROR,
      };
    }
  }

  async removeCoach(id: number): Promise<CustomResponse> {
    try {
      const resp = await this.supabase
        .from('coaches')
        .update({
          application_status: COACH_APPLICATION_STATUS.REMOVED,
        })
        .eq('id', id);

      return {
        data: resp.data,
        error: false,
        message: 'Coach removed',
        status: HttpStatus.OK,
      };
    } catch (err) {
      console.log(err);
      return {
        data: null,
        error: true,
        message: 'Error removing coach',
        status: HttpStatus.INTERNAL_SERVER_ERROR,
      };
    }
  }

  async rejectCoach(id: number): Promise<CustomResponse> {
    try {
      const resp = await this.supabase
        .from('coaches')
        .update({
          application_status: COACH_APPLICATION_STATUS.REJECTED,
        })
        .eq('id', id);
      return {
        data: resp.data,
        error: false,
        message: 'Coach application rejected',
        status: HttpStatus.OK,
      };
    } catch (err) {
      console.log(err);
      return {
        data: null,
        error: true,
        message: 'Error rejecting coach application',
        status: HttpStatus.INTERNAL_SERVER_ERROR,
      };
    }
  }

  async reopenCoach(id: number): Promise<CustomResponse> {
    try {
      const resp = await this.supabase
        .from('coaches')
        .update({
          application_status: COACH_APPLICATION_STATUS.PENDING,
        })
        .eq('id', id);
      return {
        data: resp.data,
        error: false,
        message: 'Coach application reopened',
        status: HttpStatus.OK,
      };
    } catch (err) {
      console.log(err);
      return {
        data: null,
        error: true,
        message: 'Error reopening coach application',
        status: HttpStatus.INTERNAL_SERVER_ERROR,
      };
    }
  }

  async verifyCoach(email: string): Promise<CustomResponse> {
    try {
      const coach_data = await this.prisma.coaches.findFirst({
        where: {
          email,
        },
      });
      const create_user = await this.dbService.signUpOtp(email, {
        full_name: coach_data.metadata?.['full_name'],
        gender: coach_data.metadata?.['gender'],
        role: USER_ROLE.COACH,
      });
      await delay(1000); // trigger that inserts new user into table may take some time
      const user = await this.dbService.getPublicUserbyEmail(email);
      const resp = await this.prisma.coaches.updateMany({
        where: {
          email,
        },
        data: {
          verified: true,
          user_id: user.data[0].user_id,
          application_status: COACH_APPLICATION_STATUS.ACCEPTED,
        },
      });

      return {
        data: resp,
        error: false,
        message: 'Email sent to coach',
        status: HttpStatus.OK,
      };
    } catch (err) {
      console.log(err);
      return {
        data: null,
        error: true,
        message: 'Error verifying coach',
        status: HttpStatus.INTERNAL_SERVER_ERROR,
      };
    }
  }

  async uploadResume(
    user_id: string,
    resume: Express.Multer.File,
  ): Promise<CustomResponse> {
    try {
      const uniqueFileName = `${Date.now()}-${Math.random() * 100000}-${resume.originalname}`;
      const upload = await this.dbService.uploadToStorage(
        'coach_media',
        resume,
        {
          contentType: resume.mimetype,
          upsert: false,
        },
        uniqueFileName,
      );
      const file_url = await this.dbService.getFileUrl(
        'coach_media',
        uniqueFileName,
        {},
      );
      const resp = await this.prisma.coaches.updateMany({
        where: {
          user_id,
        },
        data: {
          resumes: {
            push: file_url.data.publicUrl,
          },
        },
      });
      return {
        data: resp,
        error: false,
        message: 'File uploaded',
        status: HttpStatus.OK,
      };
    } catch (err) {
      console.log(err);
      return {
        data: null,
        error: true,
        message: 'Error uploading file',
        status: HttpStatus.INTERNAL_SERVER_ERROR,
      };
    }
  }

  async uploadHeadshots(
    user_id: string,
    headshots: Array<Express.Multer.File>,
  ): Promise<CustomResponse> {
    try {
      let file_urls = await this.uploadMultipleFiles('coach_media', headshots);
      const resp = await this.prisma.coaches.updateMany({
        where: {
          user_id,
        },
        data: {
          headshots: {
            push: file_urls,
          },
        },
      });
      return {
        data: resp,
        error: false,
        message: 'File uploaded',
        status: HttpStatus.OK,
      };
    } catch (err) {
      console.log(err);
      return {
        data: null,
        error: true,
        message: 'Error uploading file',
        status: HttpStatus.INTERNAL_SERVER_ERROR,
      };
    }
  }

  async uploadVoiceReels(
    user_id: string,
    voice_reels: Array<Express.Multer.File>,
  ): Promise<CustomResponse> {
    try {
      let file_urls = await this.uploadMultipleFiles(
        'coach_media',
        voice_reels,
      );
      const resp = await this.prisma.coaches.updateMany({
        where: {
          user_id,
        },
        data: {
          voice_reels: {
            push: file_urls,
          },
        },
      });
      return {
        data: resp,
        error: false,
        message: 'File uploaded',
        status: HttpStatus.OK,
      };
    } catch (err) {
      console.log(err);
      return {
        data: null,
        error: true,
        message: 'Error uploading file',
        status: HttpStatus.INTERNAL_SERVER_ERROR,
      };
    }
  }

  async uploadVideoReels(
    user_id: string,
    video_reels: Array<Express.Multer.File>,
  ): Promise<CustomResponse> {
    try {
      let file_urls = await this.uploadMultipleFiles(
        'coach_media',
        video_reels,
      );
      const resp = await this.prisma.coaches.updateMany({
        where: {
          user_id,
        },
        data: {
          video_reels: {
            push: file_urls,
          },
        },
      });
      return {
        data: resp,
        error: false,
        message: 'File uploaded',
        status: HttpStatus.OK,
      };
    } catch (err) {
      console.log(err);
      return {
        data: null,
        error: true,
        message: 'Error uploading file',
        status: HttpStatus.INTERNAL_SERVER_ERROR,
      };
    }
  }

  async updateStatus(
    user_id: string,
    new_status: string,
  ): Promise<CustomResponse> {
    try {
      const update = await this.prisma.coaches.updateMany({
        where: {
          user_id,
        },
        data: {
          status: new_status,
        },
      });
      return {
        data: update,
        error: false,
        message: 'Status Updated',
        status: HttpStatus.OK,
      };
    } catch (err) {
      console.log(err);
      return {
        data: null,
        error: true,
        message: 'Error updating status',
        status: HttpStatus.INTERNAL_SERVER_ERROR,
      };
    }
  }

  private async uploadMultipleFiles(
    bucket: string,
    files: Array<Express.Multer.File>,
  ): Promise<string[]> {
    let file_urls: string[] = [];
    for (let file of files) {
      const uniqueFileName = `${Date.now()}-${Math.random() * 100000}-${file.originalname}`;
      const upload = await this.dbService.uploadToStorage(
        'coach_media',
        file,
        {
          contentType: file.mimetype,
          upsert: false,
        },
        uniqueFileName,
      );
      const file_url = await this.dbService.getFileUrl(
        'coach_media',
        uniqueFileName,
        {},
      );
      file_urls.push(file_url.data.publicUrl);
    }
    return file_urls;
  }
}
