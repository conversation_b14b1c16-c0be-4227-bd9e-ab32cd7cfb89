import { <PERSON>du<PERSON> } from '@nestjs/common';
import { PostsService } from './posts.service';
import { PostsController } from './posts.controller';
import { DatabaseModule } from 'src/database/database.module';
import { HttpModule } from '@nestjs/axios';
import { CompressorModule } from '../compressor/compressor.module';
import { ExpressionAnalysisModule } from '../expression-analysis/expression-analysis.module';

@Module({
  imports: [DatabaseModule, HttpModule, CompressorModule, ExpressionAnalysisModule],
  controllers: [PostsController],
  providers: [PostsService],
  exports: [PostsService], // Export so other modules can use it
})
export class PostsModule {}
