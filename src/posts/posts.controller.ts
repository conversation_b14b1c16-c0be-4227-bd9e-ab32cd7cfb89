import {
  <PERSON>,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  UseGuards,
  HttpException,
  HttpStatus,
  UseInterceptors,
  UploadedFile,
  Req,
  Query,
} from '@nestjs/common';
import { PostsService } from './posts.service';
import { CreatePostDto } from './dto/create-post.dto';
import { AddCommentDto, UpdatePostDto } from './dto/update-post.dto';
import { UserGuard } from 'src/auth/guards/user.guard';
import { FileInterceptor } from '@nestjs/platform-express';

@Controller('posts')
export class PostsController {
  constructor(private readonly postsService: PostsService) {}

  @Post('add_post')
  @UseGuards(UserGuard)
  @UseInterceptors(FileInterceptor('file'))
  async create(
    @Req() req,
    @Body() createPostDto: CreatePostDto,
    @UploadedFile() video: Express.Multer.File,
  ) {
    const resp = await this.postsService.create(
      req.user.public_user_data[0].id,
      createPostDto,
      video,
    );
    if (resp.error) {
      throw new HttpException(resp.message, resp.status);
    }
    return resp;
  }

  @Delete('/:post_id')
  @UseGuards(UserGuard)
  async deletePost(@Req() req, @Param('post_id') post_id: number) {
    const resp = await this.postsService.deletePost(
      post_id,
      req.user.public_user_data[0].id,
    );
    if (resp.error) {
      throw new HttpException(resp.message, resp.status);
    }
    return resp;
  }

  @Get('/') // get all posts
  @UseGuards(UserGuard)
  async findAll(@Req() req, @Query('page_no') page_no: number = 1) {
    const resp = await this.postsService.findAll(page_no, req.user.id);
    if (resp.error) {
      throw new HttpException(resp.message, resp.status);
    }
    return resp;
  }

  @Get('private')
  @UseGuards(UserGuard)
  async findAllPrivate(@Req() req, @Query('page_no') page_no: number = 1) {
    const resp = await this.postsService.findPrivate(
      req.user.id,
      page_no,
      req.user.public_user_data[0].id,
    );
    if (resp.error) {
      throw new HttpException(resp.message, resp.status);
    }
    return resp;
  }

  @Get('own_public')
  @UseGuards(UserGuard)
  async findOwnPublic(@Req() req, @Query('page_no') page_no: number = 1) {
    const resp = await this.postsService.findOwnPublic(
      req.user.id,
      page_no,
      req.user.public_user_data[0].id,
    );
    if (resp.error) {
      throw new HttpException(resp.message, resp.status);
    }
    return resp;
  }

  @Post('add_upvote/:post_id')
  @UseGuards(UserGuard)
  async upvotePost(@Req() req, @Param('post_id') post_id: number) {
    const resp = await this.postsService.upvotePost(req.user.id, post_id);
    if (resp.error) {
      throw new HttpException(resp.message, resp.status);
    }
    return resp;
  }

  @Delete('remove_upvote/:post_id')
  @UseGuards(UserGuard)
  async removePostUpvote(@Req() req, @Param('post_id') post_id: number) {
    const resp = await this.postsService.removeUpvote(req.user.id, post_id);
    if (resp.error) {
      throw new HttpException(resp.message, resp.status);
    }
    return resp;
  }

  @Post('add_comment')
  @UseGuards(UserGuard)
  async addComment(@Req() req, @Body() addCommentDto: AddCommentDto) {
    const resp = await this.postsService.addComment(req.user.id, addCommentDto);
    if (resp.error) {
      throw new HttpException(resp.message, resp.status);
    }
    return resp;
  }

  @Get('comments/:post_id')
  @UseGuards(UserGuard)
  async getComments(@Req() req, @Param('post_id') post_id: number) {
    const resp = await this.postsService.getComments(post_id);
    if (resp.error) {
      throw new HttpException(resp.message, resp.status);
    }
    return resp;
  }

  @Delete('delete_comment/:comment_id') // using comment id instead of a combination of post and user id becasue a user can have multiple comments on a single post compared to upvote where a user has only one upvote on a post
  @UseGuards(UserGuard)
  async deleteComment(@Req() req, @Param('comment_id') comment_id: number) {
    const resp = await this.postsService.deleteComment(comment_id, req.user.id);
    if (resp.error) {
      throw new HttpException(resp.message, resp.status);
    }
    return resp;
  }

  @Patch('toggle_visibility/:post_id')
  @UseGuards(UserGuard)
  async makePublic(@Req() req, @Param('post_id') post_id: number) {
    const resp = await this.postsService.togglePostVisibility(
      post_id,
      req.user.public_user_data[0].id,
    );
    if (resp.error) {
      throw new HttpException(resp.message, resp.status);
    }
    return resp;
  }

  @Get('shareable_link/:post_id')
  @UseGuards(UserGuard)
  async getShareableLink(@Req() req, @Param('post_id') post_id: number) {
    const resp = await this.postsService.getShareableLink(post_id);
    if (resp.error) {
      throw new HttpException(resp.message, resp.status);
    }
    return resp;
  }

  @Get(':post_id')
  @UseGuards(UserGuard)
  async getPost(
    @Req() req,
    @Param('post_id') post_id: number,
    @Query('view_token') view_token?: string,
  ) {
    const resp = await this.postsService.getPost(
      req.user.id,
      req.user.public_user_data[0].id,
      post_id,
      view_token,
    );
    if (resp.error) {
      throw new HttpException(resp.message, resp.status);
    }
    return resp;
  }

  @Patch('invalidate_shareable_links')
  @UseGuards(UserGuard)
  async invalidateShareableLinks(
    @Req() req,
    @Query('post_id') post_id: number,
  ) {
    const resp = await this.postsService.invalidateShareableLinks(
      req.user.public_user_data[0].id,
      post_id,
    );
    if (resp.error) {
      throw new HttpException(resp.message, resp.status);
    }
    return resp;
  }
}
