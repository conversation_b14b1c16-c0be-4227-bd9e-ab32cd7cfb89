import { HttpStatus, Injectable, OnModuleDestroy } from '@nestjs/common';
import { PrismaClient } from '@prisma/client';
import {
  AdminUserAttributes,
  createClient,
  SignUpWithPasswordCredentials,
  SupabaseClient,
} from '@supabase/supabase-js';
import { CustomResponse, EventLog } from 'src/utils/CustomResponse';
// this makes sure only one prisma client exists and is injected
// into wherever it is needed rather than having to create
// new client
@Injectable()
export class DatabaseService implements OnModuleDestroy {
  async onModuleDestroy() {
    await this.flushEventBuffer();
  }
  private prismaClient: PrismaClient;

  // my strategy with the supabase client is to not expose it in services anywhere directly
  // and instead use the 'safer' prisma client. If I need some methods of the supabase client
  // I expose them through the database service so I can get an overall picture from this one file.
  private supabase: SupabaseClient;

  public eventBuffer: EventLog[] = [];

  constructor() {
    console.log('Connecting to Prisma Client');
    this.prismaClient = new PrismaClient();
    // need supabase connection to utilize some supabase functions not available through prisma
    this.supabase = createClient(
      process.env.SUPABASE_URL,
      process.env.SUPABASE_KEY,
    );
  }

  public getClient() {
    return this.prismaClient;
  }

  public getSupabaseClient() {
    return this.supabase;
  }

  public async getUserbyToken(token: string) {
    return await this.supabase.auth.getUser(token);
  }

  public async getPublicUser(user_id: string) {
    return await this.supabase
      .from('users')
      .select('*, plan_in_use(*)')
      .eq('user_id', user_id);
  }

  public async getPublicUserbyEmail(email: string) {
    return await this.supabase.from('users').select('*').eq('email', email);
  }

  public async uploadToStorage(
    path: string,
    file: Express.Multer.File,
    options: object,
    file_name: string,
  ): Promise<CustomResponse> {
    const response = await this.supabase.storage
      .from(path)
      .upload(`${file_name}`, file.buffer, options);
    if (response.error) {
      return {
        error: true,
        message: response.error.message,
        data: null,
        status: HttpStatus.OK,
      };
    }
    return {
      error: false,
      message: `File ${file.originalname} uploaded to bucket on path ${path}`,
      data: response.data,
      status: HttpStatus.INTERNAL_SERVER_ERROR,
    };
  }

  public async deleteFromStorage(
    path: string,
    filenames: string[],
  ): Promise<CustomResponse> {
    const response = await this.supabase.storage.from(path).remove(filenames);
    if (response.error) {
      return {
        error: true,
        message: response.error.message,
        data: null,
        status: HttpStatus.OK,
      };
    }
    return {
      error: false,
      message: `File Deleted`,
      data: response.data,
      status: HttpStatus.INTERNAL_SERVER_ERROR,
    };
  }

  public async getFileUrl(
    path: string,
    fileName: string,
    options: object,
  ): Promise<CustomResponse> {
    const response = this.supabase.storage
      .from(path)
      .getPublicUrl(fileName, options);
    if (response.data == null) {
      return {
        error: true,
        message:
          'Error fetching file url, the file does not exist or the bucket may not be public',
        data: null,
        status: HttpStatus.OK,
      };
    }
    return {
      error: false,
      message: 'File url fetched',
      data: response.data,
      status: HttpStatus.INTERNAL_SERVER_ERROR,
    };
  }

  public async supabaseLoginWithEmail(
    email: string,
    password: string,
  ): Promise<CustomResponse> {
    const response = await this.supabase.auth.signInWithPassword({
      email,
      password,
    });
    if (response.error) {
      return {
        error: true,
        message: response.error.message,
        data: null,
        status: HttpStatus.OK,
      };
    }
    return {
      error: false,
      message: 'Successfully logged in',
      data: response.data,
      status: HttpStatus.INTERNAL_SERVER_ERROR,
    };
  }

  public async signUp(
    credentials: SignUpWithPasswordCredentials,
  ): Promise<CustomResponse> {
    const response = await this.supabase.auth.signUp(credentials);
    if (response.error) {
      return {
        error: true,
        message: response.error.message,
        data: null,
        status: HttpStatus.OK,
      };
    }
    return {
      error: false,
      message: 'Successfully signed up',
      data: response.data,
      status: HttpStatus.INTERNAL_SERVER_ERROR,
    };
  }

  public async signUpOtp(
    email: string,
    metadata: object,
  ): Promise<CustomResponse> {
    console.log(process.env.FRONTEND_URL, encodeURI(email));
    const response = await this.supabase.auth.signInWithOtp({
      email,
      options: {
        data: metadata,
        shouldCreateUser: true,
        emailRedirectTo: process.env.FRONTEND_URL + '/set-password',
      },
    });
    if (response.error) {
      return {
        error: true,
        message: response.error.message,
        data: null,
        status: HttpStatus.OK,
      };
    }
    return {
      error: false,
      message: 'Email sent',
      data: response.data,
      status: HttpStatus.INTERNAL_SERVER_ERROR,
    };
  }

  public async updateUserbyId(
    user_id: string,
    data: AdminUserAttributes,
  ): Promise<CustomResponse> {
    const response = await this.supabase.auth.admin.updateUserById(
      user_id,
      data,
    );
    if (response.error) {
      return {
        data: null,
        error: true,
        message: 'Error updating user',
        status: HttpStatus.INTERNAL_SERVER_ERROR,
      };
    }
    return {
      data: response,
      error: false,
      message: 'User updated',
      status: HttpStatus.OK,
    };
  }

  public async flushEventBuffer() {
    await this.prismaClient.track_events.createMany({
      data: this.eventBuffer,
    });
    this.eventBuffer = [];
  }

  public async sendWaitlistMail() {
    const users = await this.prismaClient.waitlist.findMany();
    users.forEach(async (user) => {
      await this.supabase.auth.admin.inviteUserByEmail(user.email, {
        redirectTo: process.env.WAITLIST_REDIRECT_URL,
      });
    });
  }

  async updatePasswordPrisma(emailId: string, newPassword: string) {
    // Fetch user
    const user = await this.prismaClient.users.findFirst({ where: { email: emailId } });

    if (!user) {
      throw new Error('User not found');
    }

    // Verify old password
    // const isPasswordValid = await bcrypt.compare(oldPassword, user.encrypted_password);
    // if (!isPasswordValid) {
    //   throw new Error('Old password is incorrect');
    // }

    // Hash new password
    const hashedNewPassword = newPassword;// await bcrypt.hash(newPassword, SALT_ROUNDS);

    // Update password in DB
    await this.prismaClient.users.update({
      where: { id: emailId },
      data: {
        encrypted_password: hashedNewPassword,
        updated_at: new Date(),
      },
    });

    return { message: 'Password updated successfully' };
  }
}
