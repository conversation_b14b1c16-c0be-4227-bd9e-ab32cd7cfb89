import HeaderSection from "@/components/shared/HeaderSection";
import Navbar from "@/components/shared/Navbar";
import FooterSection from "@/components/shared/FooterSection";
import TalentSection from "@/components/shared/TalentSection";
import { useGetAllPlansQuery } from "@/redux/features/planApi";
import Plans from "@/components/shared/PricingPageComponents/Plans";
import PricingPlanSkeleton from "@/components/shared/Pricing/PricingPlanSkeleton";
import { Helmet } from "react-helmet-async";

// import images
import girl from "../assets/images/pricing-showcase.png";
import credit_card from "../assets/images/credit_card.jpg";

const PricingPage = () => {
  const { data: userPlansData, isLoading: plansLoading } = useGetAllPlansQuery();

  const plans =
    userPlansData?.data?.map((plan) => ({
      title: plan.plan_name,
      price: plan.plan_price_monthly,
      features: plan.description ? plan.description.split(". ") : [],
      type: plan.type
    })) || [];

  return (
    <>
      <Helmet>
        <title>Audition Rooms Pricing | Flexible Plans for Aspiring Actors</title>
        <meta name="title" content="Audition Rooms Pricing | Flexible Plans for Aspiring
        Actors" />
        <meta name="Description" content="Choose the perfect plan with Audition Rooms'
        flexible pricing options for every actor. From free basic access to premium tools for pro
        users, start your acting career today with the best audition software." />
        <meta name="Keywords" content="Audition Software Pricing, Talent Management
        Pricing, Audition software with flexible pricing plans, Cost-effective online casting
        platform for talent, Subscription plans for actor management software, Low-cost tools for
        audition and casting management, Affordable casting platform for actors, Acting career
        pricing plans" />
      </Helmet>
      <section className="bg-primary1 animate-smooth-appear">
        <Navbar />
        <HeaderSection image={credit_card} title="Pricing" />
        <div className="bg-primary1 pt-12">
          <div className="flex justify-center items-center flex-col">
            <h1 className="text-text1 text-3xl font-extrabold text-center px-5">
              Flexible Plans for Every Aspiring Actor
            </h1>
            <p className="text-text1 p-5 w-11/12 text-center max-w-[900px]">
              Whether you're just getting started or looking to enhance your craft, our Audition Software Pricing is designed to cater to your needs. Choose from flexible and affordable options to get the tools that suit your journey.
            </p>
          </div>
        </div>
        <div className="flex justify-center flex-col items-center w-full bg-primary1 text-[#FAFAFA] pb-8">
          <div className="lg:w-[60%] 2xl:w-[60%] w-full p-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {plansLoading ?
                <>
                  <PricingPlanSkeleton />
                  <PricingPlanSkeleton />
                </>
                :
                plans?.map((plan, index) => (
                  <Plans key={index} {...plan} />
                ))
              }
            </div>
          </div>
        </div>
        <TalentSection
          image={girl}
          title={`Your Acting Journey, <br />  Just a <span class="text-purple">Step Away</span>`}
          desc="Choose your plan and start your acting journey today!"
          btnText="Explore Plans Now"
        />
        <FooterSection />
      </section>
    </>
  );
};

export default PricingPage;
