
import { But<PERSON> } from "@/components/ui/button";
import { MoveRight } from "lucide-react";
import { Link } from "react-router-dom";

// images import
import Actor2Img from '../../assets/images/actor2.png';
import WaveImg from '../../assets/images/wave.png';

const TalentSection = ({ image, title, desc, btnText }) => {
  return (
    <div className="h-auto py-5 md:h-[450px] sm:h-[450px] lg:h-[450px] xl:h-[520px] w-full max-sm:block md::flex lg:flex pl-10 pr-8 items-center bg-gradient-to-r from-blue3/100 from-40% to-navItemActive/50 overflow-hidden">
      <div className="w-3/5 max-sm:w-full">
        <div className="text-2xl lg:text-6xl md:text-5xl  sm:text-4xl font-bold text-text1" dangerouslySetInnerHTML={{ __html: title }}></div>
        <p className="text-text2 py-5">{desc}</p>
        <Link to='/signup'>
          <Button className="bg-navItemActive hover:bg-navItemActive/90 text-white py-3 px-6 rounded-lg h-14">
            {btnText} <MoveRight className="ml-2 h-5 w-5" />
          </Button>
        </Link>
      </div>
      <div className="relative items-center justify-end h-full w-2/5 max-sm:w-full max-sm:block md:flex">
        <img src={image ? image : Actor2Img} alt="actor" className="max-sm:mt-4 max-sm:relative md:absolute lg:absolute object-cover object-top rounded-3xl h-[80%] max-sm:w-full md:w-[95%] lg:w-[95%] z-10" />
        <img src={WaveImg} alt="actor" className="absolute h-[85%] object-center top-16 right-5 rotate-[18deg]" />
      </div>
    </div>
  );
};

export default TalentSection;
