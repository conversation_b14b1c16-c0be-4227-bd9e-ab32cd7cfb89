import { Dialog, DialogContent, DialogTitle } from "@/components/ui/dialog";
import VideoRecorder from "@/components/VideoRecorder";
import { ScrollArea } from "./ui/scroll-area";

const VideoRecorderDialog = ({
  isOpen,
  onClose,
  monologue,
  onViewRecording,
  parentId,
}) => {
  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogTitle>
        <DialogContent className="min-w-[80%] h-[90%] bg-transparent border-none text-text1">
          <ScrollArea className="rounded-md pr-3">
            <VideoRecorder
              monologue={monologue}
              onViewRecording={onViewRecording}
              closeDialog={onClose}
              parentId={parentId}
            />
          </ScrollArea>
        </DialogContent>
      </DialogTitle>
    </Dialog>
  );
};

export default VideoRecorderDialog;
